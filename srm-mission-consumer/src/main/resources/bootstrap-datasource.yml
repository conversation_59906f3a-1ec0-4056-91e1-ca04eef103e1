spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 8
      min-idle: 1
      max-active: 30
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: select 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      driver-class-name: com.mysql.jdbc.Driver
      web-stat-filter:
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*" #不统计这些请求数据
        enabled: true
      stat-view-servlet: #访问监控网页的登录用户名和密码
        login-username: druid
        login-password: druid
        enabled: true
        allow: ""
    work-id: 4
  jpa:
    database: mysql
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      hibernate:
        jdbc:
          batch_size: 100
          order_inserts: true
          order_updates: true
          default_batch_fetch_size: 100
    show-sql: false