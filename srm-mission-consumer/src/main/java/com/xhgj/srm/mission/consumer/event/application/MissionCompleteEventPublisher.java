package com.xhgj.srm.mission.consumer.event.application;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.mission.common.MissionSourceEnum;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/8/13 13:29
 */
@Component
@Slf4j
public class MissionCompleteEventPublisher {
  private final ApplicationEventPublisher eventPublisher;

  public MissionCompleteEventPublisher(ApplicationEventPublisher eventPublisher) {
    this.eventPublisher = eventPublisher;
  }

  public void publish(Object source, Mission mission) {
    eventPublisher.publishEvent(
        new MissionCompleteEvent(
            source,
            mission.getId(),
            MissionSourceEnum.fromCode(mission.getResource()),
            mission.getType(),
            mission.getCreateManId(),
            Objects.equals(mission.getState(), Constants.MISSION_STATE_SUCCESS)));
  }
}
