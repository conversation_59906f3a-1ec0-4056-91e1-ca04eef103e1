package com.xhgj.srm.mission.consumer.handlers.imports.execs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormCallStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormExecutionStatusEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormReviewStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.service.SupplierInvoiceService;
import com.xhgj.srm.v2.dto.purchaseOrder.SupplierOrder2V2Details;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderDetailV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderProductV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.MissionCompleteResult;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.imports.ImportMissionCompleteResult;
import com.xhgj.srm.mission.consumer.handlers.imports.execs.params.purchaseOrder.InboundDeliveryImportV2DTO;
import com.xhgj.srm.mission.consumer.utils.ConsistencyCheck;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhgj.srm.service.ShareSupplierOrderDetailService;
import com.xhgj.srm.v2.repository.SupplierOrderDetailV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderV2Repository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
@Slf4j
public class InboundDeliveryImportV2ExecService implements ExecService {

  private static final String LOCK_FOR_SUPPLIER_ORDER_INBOUND = "srm:lock_for_supplier_order_inbound_v2:{}";
  private static final int LOCK_WAIT_TIME = 30; // 等待锁的最长时间（秒）
  private static final int LOCK_LEASE_TIME = 120; // 锁自动释放时间（秒）
  @Resource
  private DownloadThenUpUtil downloadThenUpUtil;
  @Resource
  private RedissonClient redissonClient;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;
  @Resource
  private SupplierOrderV2Repository supplierOrderV2Repository;
  @Resource
  private FcMissionService fcMissionService;
  @Resource
  private ApplicationContext applicationContext;
  @Resource
  private ShareSupplierOrderDetailService shareSupplierOrderDetailService;
  @Resource
  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;

  @Resource
  private SupplierOrderDetailV2Repository supplierOrderDetailV2Repository;
  @Resource
  private SupplierInvoiceService supplierInvoiceService;

  private static final List<String> SOURCE = CollUtil.toList("SAP");

  private static final List<String> REVERSAL_STATUS = CollUtil.toList("8", "9", "10");
  @Resource
  private PlatformTransactionManager transactionManager;
  /**
   * #check 单行数据校验
   */
  private void check(InboundDeliveryImportV2DTO dto) {
    // 采购订单号
    if (StrUtil.isBlank(dto.getCode())) {
      throw new CheckException("采购订单号不能为空");
    }
    // 采购订单物料行id
    if (StrUtil.isBlank(dto.getPurchaseOrderDetailRowId())) {
      throw new CheckException("采购订单物料行id不能为空");
    }
    // 审批状态
    if (StrUtil.isBlank(dto.getAssessStatus())) {
      throw new CheckException("审批状态不能为空");
    }
    // 创建日期
    if (StrUtil.isBlank(dto.getCreateTimeStr())) {
      throw new CheckException("创建日期不能为空");
    }
    // 过账日期
    if (StrUtil.isBlank(dto.getCreateTimeStr())) {
      throw new CheckException("过账日期不能为空");
    }
    // 入库数量
    if (StrUtil.isBlank(dto.getInStockQty())) {
      throw new CheckException("入库数量不能为空");
    }
    // 入库数量最大填写三位小数
    BigDecimal incomeQty =  Convert.toBigDecimal(dto.getInStockQty());
    if (incomeQty != null && incomeQty.scale() > 3) {
      throw new CheckException("入库数量最大填写三位小数");
    }
    // 退库数量
    if (StrUtil.isBlank(dto.getOutStockQty())) {
      throw new CheckException("退库数量不能为空");
    }
    // 退库数量最大填写三位小数
    BigDecimal quantityQty =  Convert.toBigDecimal(dto.getOutStockQty());
    if (quantityQty != null && quantityQty.scale() > 3) {
      throw new CheckException("入库数量最大填写三位小数");
    }
    // 已开票数量
    if (StrUtil.isBlank(dto.getInvoiceQty())) {
      throw new CheckException("已开票数量不能为空");
    }
    // 已开票数量最大填写三位小数
    BigDecimal invoiceQty =   Convert.toBigDecimal(dto.getInvoiceQty());
    if (invoiceQty != null && invoiceQty.scale() > 3) {
      throw new CheckException("已开票数量最大填写三位小数");
    }
    // SAP物料凭证号
    if (StrUtil.isBlank(dto.getProductVoucherNo())) {
      throw new CheckException("SAP物料凭证号不能为空");
    }
    // SAP物料凭证行项目
    if (StrUtil.isBlank(dto.getProductVoucherLineItem())) {
      throw new CheckException("SAP物料凭证行项目不能为空");
    }
    // 冲销状态
    if (StrUtil.isBlank(dto.getReversalStatus())) {
      throw new CheckException("冲销状态不能为空");
    }
    boolean contains = REVERSAL_STATUS.contains(dto.getReversalStatus());
    if (!contains) {
      throw new CheckException("冲销状态填写不正确");
    }
    // 来源
    if (StrUtil.isBlank(dto.getSource())) {
      throw new CheckException("来源不能为空");
    }
    boolean containsSource = SOURCE.contains(dto.getSource());
    if (!containsSource) {
      throw new CheckException("来源填写不正确");
    }
    // 仓库执行状态
    if (StrUtil.isBlank(dto.getWarehouseExecuteStatus())) {
      throw new CheckException("仓库执行状态不能为空");
    }
    boolean containsWarehouseExecuteStatus = Objects.isNull(SupplierOrderFormExecutionStatusEnum.fromKey(dto.getWarehouseExecuteStatus()));
    if (containsWarehouseExecuteStatus) {
      throw new CheckException("仓库执行状态填写不正确");
    }
  }


  /**
   * # check 分组数据一致性校验
   */
  private void consistencyCheck(List<InboundDeliveryImportV2DTO> list) {
    List<Function<InboundDeliveryImportV2DTO, Object>> extractors = CollUtil.toList(
        InboundDeliveryImportV2DTO::getCode,
        InboundDeliveryImportV2DTO::getSource,
        InboundDeliveryImportV2DTO::getReversalStatus,
        InboundDeliveryImportV2DTO::getProductVoucherNo,
        InboundDeliveryImportV2DTO::getTrackNum,
        InboundDeliveryImportV2DTO::getLogisticsCompanyName,
        InboundDeliveryImportV2DTO::getLogisticsCompanyCode,
        InboundDeliveryImportV2DTO::getWarehouseCode
    );
    ConsistencyCheck.validateFieldsConsistencyByMD5(list, extractors, "数据不一致");
  }


  /**
   * 执行操作
   * @param collection
   * @param dispatchParam
   * @return
   */
  @Override
  public MissionCompleteResult exec(Collection<?> collection, MissionDispatchParam dispatchParam) {
    // 转换为JSONObject
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 获取filePath
    String filePath = jsonObject.getString("filePath");
    // 获取fileName
    String fileName = jsonObject.getString("fileName");
    // collection转换为SupplierImportParam
    List<InboundDeliveryImportV2DTO> InboundDeliveryImportV2DTOList = (List<InboundDeliveryImportV2DTO>) collection;
    int rowIndex = 1;
    String currentCode = null;
    AtomicInteger successCount = new AtomicInteger(0);
    List<InboundDeliveryImportV2DTO> groupList = new ArrayList<>();
    for (InboundDeliveryImportV2DTO dto : InboundDeliveryImportV2DTOList) {
      String code = StrUtil.format("{}/{}", dto.getCode(), dto.getProductVoucherNo());
      dto.setRowIndex(rowIndex++);
      // 如果是新的code组
      if (currentCode == null || !currentCode.equals(code)) {
        // 保存上一组数据(如果存在)
        if (!groupList.isEmpty()) {
          this.saveOne(groupList, dispatchParam.getMissionId(), successCount);
          groupList.clear();
        }
        currentCode = code;
      }
      groupList.add(dto);
    }
    // 保存最后一组数据
    if (!groupList.isEmpty()) {
      this.saveOne(groupList, dispatchParam.getMissionId(), successCount);
    }
    return ImportMissionCompleteResult.builder()
        .successCount(successCount.get())
        .fileName(fileName)
        .filePath(filePath)
        .build();
  }

  /**
   * 准备数据
   * @param dispatchParam
   * @return
   */
  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
    // 转换为JSONObject
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 获取filePath
    String filePath = jsonObject.getString("filePath");
    // 获取fileName
    String fileName = jsonObject.getString("fileName");
    // oss下载文件
    List<InboundDeliveryImportV2DTO> result = new ArrayList<>();
    try ( InputStream inputStream = downloadThenUpUtil.getInputStreamFromOSS(filePath);
        Workbook book = ExcelUtil.buildByFile(fileName, inputStream);) {
      if (book == null) {
        throw new CheckException("文件为空");
      }
      // 获取sheet
      Sheet sheet = book.getSheetAt(0);
      // 获取总行数
      int lastRowNum = sheet.getPhysicalNumberOfRows();
      // 如果总行数小于1则抛出异常
      if (lastRowNum < 1) {
        throw new CheckException("导入数据为空");
      }
      // 从第三行开始读取
      int startRow = 2;
      // 读取数据
      for (int i = startRow; i <= lastRowNum; i++) {
        // 获取当前行
        Row row = sheet.getRow(i);
        if (row == null) {
          continue;
        }
        int cellNum = 0;
        // 获取固定列
        // 订单号
        String code = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 采购订单物料行id
        String purchaseOrderDetailRowId = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 入库单号
        String warehouseOrderCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));

        // 审批状态
        String assessStatus = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 仓库执行状态
        String warehouseExecuteStatus = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 创建时间
        String createTimeStr = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 过账日期
        String postingDateStr = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 物料编码
        String productCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 入库数量
        String inStockQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 退库数量
        String outStockQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 批号
        String batchNo = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 已开票数量
        String invoiceQty = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 入库仓库编码
        String warehouseCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 物流公司编码
        String logisticsCompanyCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 物流公司名称
        String logisticsCompanyName = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 快递单号
        String trackNum = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // sap物料凭证号
        String productVoucherNo = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // SAP物料凭证行项目
        String productVoucherLineItem = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 冲销状态
        String reversalStatus = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 来源 填写SAP
        String source = ExcelUtil.getCellStringValue(row.getCell(cellNum++));

        InboundDeliveryImportV2DTO dto = InboundDeliveryImportV2DTO
            .builder()
            .code(code)
            .productCode(productCode)
            .inStockQty(inStockQty)
            .outStockQty(outStockQty)
            .batchNo(batchNo)
            .invoiceQty(invoiceQty)
            .warehouseCode(warehouseCode)
            .logisticsCompanyCode(logisticsCompanyCode)
            .logisticsCompanyName(logisticsCompanyName)
            .trackNum(trackNum)
            .productVoucherNo(productVoucherNo)
            .productVoucherLineItem(productVoucherLineItem)
            .reversalStatus(reversalStatus)
            .source(source)
            .purchaseOrderDetailRowId(purchaseOrderDetailRowId)
            .createTimeStr(createTimeStr)
            .postingDateStr(postingDateStr)
            .assessStatus(assessStatus)
            .inboundDeliveryNo(warehouseOrderCode)
            .warehouseExecuteStatus(warehouseExecuteStatus)
            .build();
        result.add(dto);
      }
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    if (CollUtil.isEmpty(result)) {
      throw new CheckException("导入数据为空");
    }
    return result;
  }

  /**
   * 保存入库单
   * @param list
   * @param missionId
   * @param successCount
   */
  private void saveOne(List<InboundDeliveryImportV2DTO> list, String missionId, AtomicInteger successCount) {
    // 获取锁，防止同一时间多个线程操作同一数据
    RLock lock =
        redissonClient.getLock(StrUtil.format(LOCK_FOR_SUPPLIER_ORDER_INBOUND, list.get(0).getCode()));
    try {
      boolean locked = lock.tryLock(LOCK_WAIT_TIME, LOCK_LEASE_TIME, TimeUnit.SECONDS);
      try {
        InboundDeliveryImportV2ExecService proxy =
            applicationContext.getBean(InboundDeliveryImportV2ExecService.class);
        // 保存采购订单
        proxy.saveOneTransactional(list);
      } catch (Exception e) {
        // 处理异常
        log.error("导入入库单失败,第【{}】行数据写入失败,异常信息为:", list.get(0).getRowIndex(), e);
        allFail(list, e.getMessage());
      }
      // 保存日志
      saveLogs(list, missionId, successCount);
    } catch (Exception e) {
      log.error("导入入库单失败,第【{}】行数据写入失败,异常信息为:", list.get(0).getRowIndex(), e);
      allFail(list, "导入入库单失败:" + e.getMessage());
      saveLogs(list, missionId, successCount);
    } finally {
      if (lock.isHeldByCurrentThread()) {
        lock.unlock();
      }
    }
  }


  /**
   * 保存入库单事务
   * @param list
   */
  public void saveOneTransactional(List<InboundDeliveryImportV2DTO> list) {
    // 开启事务
    TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
    try {
      transactionTemplate.execute(status -> {
        // 0.校验
        // #check 数据一致性校验
        this.consistencyCheck(list);
        for (InboundDeliveryImportV2DTO dto : list) {
          try {
            // #check 单行数据基础校验
            this.check(dto);
          } catch (Exception e) {
            dto.setIsFailed(true);
            dto.setFailedReason(e.getMessage());
          }
        }
        // #check 校验采购订单
        SupplierOrderV2 supplierOrder =
            supplierOrderV2Repository.findFirstByCodeAndStateIn(list.get(0).getCode(),
                CollUtil.toList(Constants.STATE_OK));
        if (supplierOrder == null) {
          // #check 采购订单校验
          throw new CheckException("采购订单不存在");
        }
        SupplierOrder2V2Details supplierOrder2V2Details =
            shareSupplierOrderDetailService.getDetailsByOrderIdsV2(
                    Collections.singletonList(supplierOrder.getId())).stream().findFirst()
                .orElse(new SupplierOrder2V2Details(supplierOrder, new ArrayList<>()));
        List<SupplierOrderDetailV2> supplierOrderDetails = supplierOrder2V2Details.getSupplierOrderDetails();
        if (CollUtil.isEmpty(supplierOrderDetails)) {
          // #check 采购订单明细校验
          throw new CheckException("采购订单明细不存在");
        }
        // #check 仓库校验
        InventoryLocation location =
            inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(
                    supplierOrder.getGroupCode(), list.get(0).getWarehouseCode(), Constants.STATE_OK)
                .orElse(new InventoryLocation());
        // #check 校验是否本来就存在
        List<SupplierOrderToFormV2> supplierOrderToForms =
            supplierOrderToFormV2Repository.findBySupplierOrderIdAndTypeAndState(
                supplierOrder.getId(), SupplierOrderFormType.WAREHOUSING.getType(),
                Constants.STATE_OK);
        if (supplierOrderToForms.stream()
            .filter(item -> item.getProductVoucher().equals(list.get(0).getProductVoucherNo()))
            .findAny().orElse(null) != null) {
          // #check 入库单Form校验
          throw new CheckException("入库单已存在");
        }
        // 1.创建入库单Form
        SupplierOrderToFormV2 inForm = this.createSupplierOrderInForm(list, supplierOrder, location);
        if (inForm == null) {
          // #check 入库单Form校验
          throw new CheckException("入库单Form不存在");
        }
        supplierOrderToFormV2Repository.saveAndFlush(inForm);
        // 2.创建入库单详情
        List<SupplierOrderDetailV2> inDetails = this.createSupplierOrderInDetail(list, inForm,
            supplierOrderDetails, location);

        // 3.更新发票
        supplierInvoiceService.batchSetSupplierOpenInvoiceState(
            Collections.singletonList(supplierOrder.getId()));
        if (CollUtil.isEmpty(inDetails)) {
          // #check 入库单详情校验
          throw new CheckException("入库单详情不存在");
        }
        supplierOrderDetailV2Repository.saveAll(inDetails);
        supplierOrderDetailV2Repository.flush();
        return null;
      });
    } catch (Exception e) {
      // 处理异常
      log.error("导入入库单失败,第【{}】行数据写入失败,异常信息为:", list.get(0).getRowIndex(), e);
      throw e;
    }
  }



  /**
   * 1.创建入库单Form
\   * @param supplierOrder
   * @return
   */
  private SupplierOrderToFormV2 createSupplierOrderInForm(List<InboundDeliveryImportV2DTO> originList,
      SupplierOrderV2 supplierOrderV2, InventoryLocation location) {
    // 过滤掉失败的 + 需要入库的
    List<InboundDeliveryImportV2DTO> list =
        originList.stream().filter(dto -> !Boolean.TRUE.equals(dto.getIsFailed()))
            .collect(Collectors.toList());
    if (CollUtil.isEmpty(list)) {
      return null;
    }
    BigDecimal num = list.stream().map(item -> Convert.toBigDecimal(item.getInStockQty()))
        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    InboundDeliveryImportV2DTO importV2DTO = list.get(0);
    String logisticsCompanyCode = StrUtil.emptyIfNull(importV2DTO.getLogisticsCompanyCode());
    String logisticsCompanyName = StrUtil.emptyIfNull(importV2DTO.getLogisticsCompanyName());
    // 快递单号
    String trackNum = StrUtil.emptyIfNull(importV2DTO.getTrackNum());

    // 审批状态
    String assessStatus = importV2DTO.getAssessStatus();
    // 创建日期
    Long createTime = importV2DTO.getCreateTime();
    // 过账日期
    Long postingDate = importV2DTO.getPostingDate();
    // 物料凭证
    String productVoucherNo = importV2DTO.getProductVoucherNo();
    // 冲销状态
    String reversalStatus = importV2DTO.getReversalStatus();
    if ("10".equals(reversalStatus)) {
      reversalStatus = null;
    }
    // 来源
    String source = importV2DTO.getSource();
    SupplierOrderToFormV2 supplierOrderToFormV2 = new SupplierOrderToFormV2();
    supplierOrderToFormV2.setSupplierOrderId(supplierOrderV2.getId());
    supplierOrderToFormV2.setType(SupplierOrderFormType.WAREHOUSING.getType());
    supplierOrderToFormV2.setState(Constants.STATE_OK);
    supplierOrderToFormV2.setLogisticsCompany(logisticsCompanyName);
    supplierOrderToFormV2.setLogisticsCode(logisticsCompanyCode);
    supplierOrderToFormV2.setTrackNum(trackNum);
    supplierOrderToFormV2.setStatus(reversalStatus);
    supplierOrderToFormV2.setUpdateTime(System.currentTimeMillis());
    supplierOrderToFormV2.setReturnPrice(BigDecimal.ZERO);
    supplierOrderToFormV2.setNum(num);
    supplierOrderToFormV2.setStockOutput(null);
    supplierOrderToFormV2.setReturnStock(null);
    supplierOrderToFormV2.setBatchNumber(null);
    supplierOrderToFormV2.setReturnId(null);
    supplierOrderToFormV2.setWarehousing(null);
    supplierOrderToFormV2.setRemark(null);
    supplierOrderToFormV2.setNoticeReceipt(null);
    supplierOrderToFormV2.setSendSms(null);
    supplierOrderToFormV2.setLogisticsInformation(null);
    supplierOrderToFormV2.setProductVoucher(productVoucherNo);
    supplierOrderToFormV2.setProductVoucherYear(null);
    supplierOrderToFormV2.setSapReversalNo(null);
    supplierOrderToFormV2.setDeliverFormId(null);
    supplierOrderToFormV2.setReturnReason(null);
    supplierOrderToFormV2.setSource(source);
    supplierOrderToFormV2.setCreateTime(createTime);
    supplierOrderToFormV2.setPostingDate(postingDate);
    // 产品说只会导入审核通过的，默认写死
    supplierOrderToFormV2.setReviewStatus(SupplierOrderFormReviewStatus.NORMAL.getCode());
    supplierOrderToFormV2.setExecutionStatus(importV2DTO.getWarehouseExecuteStatus());
    supplierOrderToFormV2.setWarehouseCode(location.getWarehouse());
    supplierOrderToFormV2.setWarehouseName(location.getWarehouseName());
    supplierOrderToFormV2.setFormCode(importV2DTO.getInboundDeliveryNo());
    supplierOrderToFormV2.setCallStatus(SupplierOrderFormCallStatus.CALL_SUCCESS.getStatus());
    return supplierOrderToFormV2;
  }

  /**
   * 2.创建入库单详情
   * @param inForm
   * @param supplierOrderDetails
   * @param location
   * @return
   */
  private List<SupplierOrderDetailV2> createSupplierOrderInDetail(
      List<InboundDeliveryImportV2DTO> originList,
      SupplierOrderToFormV2 inForm,
      List<SupplierOrderDetailV2> supplierOrderDetails,
      InventoryLocation location) {
    // 过滤掉失败的
    List<InboundDeliveryImportV2DTO> list =
        originList.stream().filter(dto -> !Boolean.TRUE.equals(dto.getIsFailed()))
            .collect(Collectors.toList());
    if (CollUtil.isEmpty(list)) {
      return new ArrayList<>();
    }
    List<SupplierOrderDetailV2> res = new ArrayList<>();
    int index = 0;
    for (InboundDeliveryImportV2DTO dto : list) {
      SupplierOrderDetailV2 supplierOrderDetailV2 = supplierOrderDetails.stream()
          .filter(item -> item.getSortNum().toString().equals(dto.getPurchaseOrderDetailRowId()))
          .findFirst().orElse(null);
      if (supplierOrderDetailV2 == null) {
        dto.setIsFailed(true);
        dto.setFailedReason("物料明细不存在");
        continue;
      }
      SupplierOrderProductV2 supplierOrderProduct = supplierOrderDetailV2.getSupplierOrderProduct();
      SupplierOrderDetailV2 inDetail = new SupplierOrderDetailV2();
      inDetail.setOrderProductId(supplierOrderProduct.getId());
      inDetail.setOrderToFormId(inForm.getId());
      BigDecimal inStockQty =
          Optional.ofNullable(Convert.toBigDecimal(dto.getInStockQty())).orElse(BigDecimal.ZERO);
      BigDecimal outStockQty =
          Optional.ofNullable(Convert.toBigDecimal(dto.getOutStockQty())).orElse(BigDecimal.ZERO);
      inDetail.setNum(inStockQty);
      inDetail.setWaitQty(supplierOrderDetailV2.getWaitQty());
      inDetail.setShipQty(supplierOrderDetailV2.getShipQty());
      inDetail.setReturnQty(outStockQty);
      inDetail.setCancelQty(supplierOrderDetailV2.getCancelQty());
      inDetail.setStockInputQty(inStockQty);
      inDetail.setWaitStockInputQty(supplierOrderDetailV2.getWaitStockInputQty());
      inDetail.setRemainQty(supplierOrderDetailV2.getRemainQty());
      inDetail.setStockOutputQty(outStockQty);
      inDetail.setSettleQty(supplierOrderDetailV2.getSettleQty());
      inDetail.setMark(supplierOrderDetailV2.getMark());
      inDetail.setSalesOrderNo(supplierOrderDetailV2.getSalesOrderNo());
      inDetail.setCreateTime(System.currentTimeMillis());
      inDetail.setUpdateTime(System.currentTimeMillis());
      inDetail.setState(Constants.STATE_OK);
      inDetail.setSortNum(supplierOrderDetailV2.getSortNum());
      inDetail.setDetailedErpId(null);
      inDetail.setReturnRowAndNumJson("{}");
      inDetail.setPrice(supplierOrderDetailV2.getPrice());
      inDetail.setTotalPrice(inDetail.getPrice().multiply(inDetail.getNum()));
      inDetail.setOpenRedInvoice(null);
      inDetail.setErpClose(null);
      inDetail.setDescription("");
      inDetail.setPurchaseApplyForOrderId(null);
      inDetail.setWarehouse(location.getWarehouse());
      inDetail.setWarehouseName(location.getWarehouseName());
      inDetail.setTaxRate(supplierOrderDetailV2.getTaxRate());
      inDetail.setTotalAmountIncludingTax(inDetail.getPrice().multiply(inDetail.getNum()));
      inDetail.setFreeState(supplierOrderDetailV2.getFreeState());
      inDetail.setProjectType(supplierOrderDetailV2.getProjectType());
      inDetail.setEntrustDetailId(null);
      inDetail.setSapRowId(null);
      inDetail.setPurchaseDeliverTime(supplierOrderDetailV2.getDeliverTime());
      inDetail.setDeliverTime(supplierOrderDetailV2.getDeliverTime());
      inDetail.setInWareHouseId(null);
      inDetail.setInWareHouseName(null);
      // 批号问题
      inDetail.setBatchNo(dto.getBatchNo());
      inDetail.setSapReversalRowNo(null);
      inDetail.setOpenInvoiceState("0");
      BigDecimal InvoiceQty =
          Optional.ofNullable(Convert.toBigDecimal(dto.getInvoiceQty())).orElse(BigDecimal.ZERO);
      inDetail.setInvoicedNum(InvoiceQty);
      if (InvoiceQty.compareTo(BigDecimal.ZERO) > 0) {
        inDetail.setOpenInvoiceState("1");
      }
      inDetail.setPurchaseOrderId(inForm.getSupplierOrderId());
      inDetail.setInvoicableNum(inStockQty.subtract(InvoiceQty));
      inDetail.setProductRate(supplierOrderDetailV2.getProductRate());
      inDetail.setMarkupCoefficient(BigDecimal.ZERO);
      inDetail.setTransferPrice(BigDecimal.ZERO);
      inDetail.setSurcharge(BigDecimal.ZERO);
      inDetail.setSettlementPrice(supplierOrderDetailV2.getSettlementPrice());
      inDetail.setTotalSettlementPrice(supplierOrderDetailV2.getTotalSettlementPrice());
      inDetail.setTariff(BigDecimal.ZERO);
      inDetail.setTariffAmount(BigDecimal.ZERO);
      inDetail.setPaymentAmount(BigDecimal.ZERO);
      inDetail.setFreight(BigDecimal.ZERO);
      inDetail.setProjectNo(null);
      inDetail.setProjectName(null);
      inDetail.setFreightSupplierId(null);
      inDetail.setTariffSupplierId(null);
      inDetail.setFreightSupplierName(null);
      inDetail.setTariffSupplierName(null);
      inDetail.setReturnPrice(BigDecimal.ZERO);
      inDetail.setReturnAmount(BigDecimal.ZERO);
      inDetail.setIncidentalAmount(null);
      inDetail.setIncidentalSupplierId(null);
      inDetail.setIncidentalSupplierName(null);
      inDetail.setOriginalPrice(BigDecimal.ZERO);
      inDetail.setOriginalTotalPrice(BigDecimal.ZERO);
      inDetail.setReturnFlag(false);
      inDetail.setDetailedId(supplierOrderDetailV2.getId());
      inDetail.setSapRowId(dto.getProductVoucherLineItem());
      inDetail.setOrderToFormType(inForm.getType());
      res.add(inDetail);
      index++;
    }
    return res;
  }

  /**
   * 所有未失败的行失败处理
   */
  private void allFail(List<InboundDeliveryImportV2DTO> list, String msg) {
    for (InboundDeliveryImportV2DTO dto : list) {
      if (Boolean.TRUE.equals(dto.getIsFailed())) {
        continue;
      }
      dto.setIsFailed(true);
      dto.setFailedReason(msg);
    }
  }

  /**
   * 保存任务执行日志
   * @param list
   * @param missionId
   */
  private void saveLogs(List<InboundDeliveryImportV2DTO> list, String missionId,
      AtomicInteger successCount) {
    for (InboundDeliveryImportV2DTO dto : list) {
      if (Boolean.TRUE.equals(dto.getIsFailed())) {
        fcMissionService.createMissionDetail(missionId,
            String.format("第【%s】行,", dto.getRowIndex()), dto.getFailedReason());
      } else {
        fcMissionService.createMissionDetail(missionId,
            String.format("第【%s】行,", dto.getRowIndex()), StrUtil.EMPTY);
        successCount.incrementAndGet();
      }
    }
  }

}
