package com.xhgj.srm.mission.consumer.handlers.exports.execs;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.enums.AccountingPeriodEnum;
import com.xhgj.srm.common.enums.contract.LandingMerchantContractPaymentConditionEnum;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.common.vo.order.OrderNeedPaymentListVO;
import com.xhgj.srm.jpa.dao.OrderNeedPaymentDao;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrderLink;
import com.xhgj.srm.jpa.repository.OrderPaymentToOrderLinkRepository;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.MissionCompleteResult;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.exports.ExportMissionCompleteResult;
import com.xhgj.srm.service.SharePlatformService;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.mvc.base.PageResult;
import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * OrderNeedPaymentExecService
 */
@Component
public class OrderNeedPaymentExecService implements ExecService {

  @Resource
  private OrderNeedPaymentDao orderNeedPaymentDao;
  @Resource
  private ExportUtil ex;
  @Resource
  private SharePlatformService platformService;
  @Resource
  private OrderPaymentToOrderLinkRepository orderPaymentToOrderLinkRepository;
  @Resource
  private FcMissionService fcMissionService;

  @Override
  @SneakyThrows
  public MissionCompleteResult exec(Collection<?> collection, MissionDispatchParam dispatchParam) {
    try (SXSSFWorkbook book = new SXSSFWorkbook()) {
      CellStyle baseStyle = ex.getBaseStyle(book);
      CellStyle titleStyle = ex.getTitleStyle(book);
      titleStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
      titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
      List<String> titles = new ArrayList<>(Constants_Excel.EXPORT_ORDER_NEED_PAYMENT_LIST_TITLE_LIST);
      List<Integer> titleSize = new ArrayList<>();
      for (String t : Constants_Excel.EXPORT_ORDER_NEED_PAYMENT_LIST_TITLE_LIST) {
        titleSize.add(20);
      }
      Sheet sheet = ex.createSheet(book, "需付款列表导出", titleSize);
      Row rowTitle = sheet.createRow(0);
      ex.createTitle(titles, titleStyle, rowTitle);
      List<OrderNeedPaymentListVO> vos = (List<OrderNeedPaymentListVO>) collection;

      int index = 1;
      long count = 0;
      for (int i = 0; i < vos.size(); i++) {
        int col = 0;
        int rowNum = index + 1;
        Row row = sheet.createRow(index);
        OrderNeedPaymentListVO vo = vos.get(i);
        ex.createCell(row, col++, vo.getOrderNo(), baseStyle);
        String platformName = platformService.findNameByCode(vo.getPlatformCode());

        ex.createCell(row, col++, StrUtil.emptyIfNull(platformName), baseStyle);
        ex.createCell(row, col++, vo.getCreateTime() == null ? StrUtil.EMPTY
            : DateUtils.formatTimeStampToNormalDateTime(vo.getCreateTime()), baseStyle);
        //发货状态
        String shipState = StrUtil.EMPTY;
        if (vo.getFirstShipTime() == null && vo.getAllShipTime() == null) {
          shipState = "未发货";
        } else if (vo.getAllShipTime() != null) {
          shipState = "已发货";
        } else if (vo.getFirstShipTime() != null) {
          shipState = "部分发货";
        }
        ex.createCell(row, col++, shipState, baseStyle);
        ex.createCell(row, col++, vo.getFirstShipTime() == null ? StrUtil.EMPTY
            : DateUtils.formatTimeStampToNormalDateTime(vo.getFirstShipTime()), baseStyle);
        ex.createCell(row, col++, vo.getAllShipTime() == null ? StrUtil.EMPTY
            : DateUtils.formatTimeStampToNormalDateTime(vo.getAllShipTime()), baseStyle);
        // 签收凭证
        ex.createCell(row, col++, Constants_order.SIGN_VOUCHER_MAP.get(vo.getSignVoucherState()), baseStyle);
        ex.createCell(row, col++, vo.getConfirmVoucherTime() == null ? StrUtil.EMPTY
            : DateUtils.formatTimeStampToNormalDateTime(vo.getConfirmVoucherTime()), baseStyle);
        // 客户开票
        ex.createCell(row, col++, Constants_order.INVOICE_STATE_MAP.get(vo.getInvoicingState()), baseStyle);
        ex.createCell(row, col++, vo.getCustomerInvoiceTime() == null ? StrUtil.EMPTY
            : DateUtils.formatTimeStampToNormalDateTime(vo.getCustomerInvoiceTime()), baseStyle);
        // 供应商开票状态
        ex.createCell(row, col++, Constants.ORDER_SUPPLIER_INVOICE_STATE_TYPE.get(vo.getSupplierOpenInvoiceStatus()), baseStyle);
        ex.createCell(row, col++, vo.getConfirmAccountOpenInvoiceTime() == null ? StrUtil.EMPTY
            : DateUtils.formatTimeStampToNormalDateTime(vo.getConfirmAccountOpenInvoiceTime()), baseStyle);
        ex.createCell(row, col++, vo.getCustomerAcceptTime() == null ? StrUtil.EMPTY
            : DateUtils.formatTimeStampToNormalDateTime(vo.getCustomerAcceptTime()), baseStyle);
        // 客户回款
        ex.createCell(row, col++, Constants_order.getCustomerPaybackStateNameByReturnProgress(
            vo.getCustomerPayback(), Constants_order.CUSTOMER_PAYBACK_UN), baseStyle);
        // 客户回款方式
        ex.createCell(row, col++, vo.getPaymentMethodValue(), baseStyle);
        //回款比例
        ex.createCell(row, col++, BigDecimalUtil.formatForStandard(
                NumberUtil.mul(vo.getShowRate(), 100)).toPlainString() + "%", baseStyle);
        ex.createCell(row, col++, vo.getBankSerialNo(), baseStyle);
        ex.createCell(row, col++, vo.getBillNo(), baseStyle);
        //付款发起条件
        String paymentTerms = StrUtil.EMPTY;
        StringBuilder paymentTermsBuild = new StringBuilder();
        if (StrUtil.isNotBlank(vo.getPaymentCondition())) {
          String[] termsArray = StrUtil.split(vo.getPaymentCondition(), StrUtil.COMMA);
          for (String s : termsArray) {
            Optional.ofNullable(LandingMerchantContractPaymentConditionEnum.valueOfByKey(s))
                .ifPresent(item -> {
                  paymentTermsBuild.append(item.getValue());
                  paymentTermsBuild.append(StrUtil.COMMA);
                });
          }
          // 去除最后一个,
          if (paymentTermsBuild.length() > 0) {
            paymentTerms = paymentTermsBuild.substring(0, paymentTermsBuild.length() - 1);
          }
        }
        ex.createCell(row, col++, paymentTerms, baseStyle);
        ex.createCell(row, col++, vo.getPaymentConditionTime() == null ? StrUtil.EMPTY
            : DateUtils.formatTimeStampToNormalDateTime(vo.getPaymentConditionTime()), baseStyle);
        //对应账期
        if (BooleanUtil.isTrue(vo.getBackToBack())) {
          ex.createCell(row, col++, AccountingPeriodEnum.BACK_TO_BACK.getValue(), baseStyle);
        } else {
          ex.createCell(row, col++, vo.getAccountingPeriod() == null ? StrUtil.EMPTY
              : vo.getAccountingPeriod() + "天", baseStyle);
        }
        ex.createCell(row, col++, vo.getPaymentDate() == null ? StrUtil.EMPTY
            : DateUtils.formatTimeStampToNormalDateTime(vo.getPaymentDate()), baseStyle);
        //付款方式
        ex.createCell(row, col++, vo.getSapPaymentMethodValue(), baseStyle);
        //供应商名称
        ex.createCell(row, col++, vo.getSupplierName(), baseStyle);
        //实际订货金额
        ex.createCell(row, col++, BigDecimalUtil.formatForStandard(vo.getOrderActualAmount()).toPlainString(), baseStyle);
        //可提款金额
        ex.createCell(row, col++, BigDecimalUtil.formatForStandard(vo.getAvailableAmount()).toPlainString(), baseStyle);
        //已提款金额
        ex.createCell(row, col++, BigDecimalUtil.formatForStandard(vo.getPaidAmount()).toPlainString(), baseStyle);
        //退款金额
        ex.createCell(row, col++, BigDecimalUtil.formatForStandard(vo.getReturnAmount()).toPlainString(), baseStyle);
        //剩余可提款金额
        ex.createCell(row, col++, BigDecimalUtil.formatForStandard(vo.getRemainingAmount()).toPlainString(), baseStyle);
        //关联付款单
        String linkedPaymentNos =
            orderPaymentToOrderLinkRepository.findByOrderNeedPaymentId(vo.getId()).stream()
                .map(OrderPaymentToOrderLink::getPaymentNo).collect(Collectors.joining(StrUtil.COMMA));
        ex.createCell(row, col++, linkedPaymentNos, baseStyle);

        index++;
        count++;
        fcMissionService.createMissionDetail(dispatchParam.getMissionId(), "第【" + rowNum + "】行",
            StrUtil.EMPTY);
      }
      String now = String.valueOf(System.currentTimeMillis());
      String fileNewName = "【后台】需付款列表导出" + now + ".xlsx";
      // 创建临时文件
      File tempFile = File.createTempFile("excel_", ".xlsx");
      try (FileOutputStream fileOutputStream = new FileOutputStream(tempFile)) {
        // 将 XSSFWorkbook 内容写入临时文件
        book.write(fileOutputStream);
        book.dispose();
        book.close();
      }
      return ExportMissionCompleteResult.builder().successCount(count).fileName(fileNewName)
          .tempFile(tempFile).build();
    }

  }

  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
    Map<String, Object> queryMap =
        JSON.parseObject(dispatchParam.getParams(), new TypeReference<Map<String, Object>>() {});
    PageResult<OrderNeedPaymentListVO> page = orderNeedPaymentDao.getPage(queryMap);
    return page.getContent();
  }
}
