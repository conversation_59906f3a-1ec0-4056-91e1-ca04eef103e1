package com.xhgj.srm.mission.consumer.handlers.dataProcess;/**
 * @since 2025/4/10 18:37
 */

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.repository.PurchaseApplyForOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderProductRepository;
import com.xhgj.srm.jpa.util.LazyLoaderContext;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SupplierOrderDetailOld2Handler implements DataProcess {

  @Resource
  private SupplierOrderDetailRepository supplierOrderDetailRepository;
  @Resource
  private SupplierOrderProductRepository supplierOrderProductRepository;
  @Resource
  private PurchaseApplyForOrderRepository purchaseApplyForOrderRepository;

  @Override
  public void process(MissionDispatchParam dispatchParam) {
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 分页参数
    int pageSize = 2000;
    int currentPage = 0;
    boolean hasMoreData = true;
    // 批量保存阈值
    int batchSize = 100;

    // 总处理记录计数
    int totalProcessed = 0;
    // 成功处理记录计数
    int totalSuccess = 0;
    // 失败记录计数
    int totalFailed = 0;

    log.info("开始处理供应商订单产品数据更新任务");
    long startTime = System.currentTimeMillis();
    while (hasMoreData) {
      // 分页查询旧数据
      int finalCurrentPage = currentPage;
      Page<SupplierOrderDetail> oldDataPage = LazyLoaderContext.lazyLoad(() -> supplierOrderDetailRepository.getOldData2(PageRequest.of(finalCurrentPage, pageSize)));
      List<SupplierOrderDetail> oldData = oldDataPage.getContent();
      List<String> purchaseApplyIds =
          oldData.stream().map(SupplierOrderDetail::getPurchaseApplyForOrderId).distinct()
              .collect(Collectors.toList());
      List<String> productIds =
          oldData.stream().map(SupplierOrderDetail::getOrderProductId).distinct()
              .collect(Collectors.toList());
      purchaseApplyIds.add("-1");
      productIds.add("-1");
      Map<String, PurchaseApplyForOrder> purchaseApplyForOrderMap =
          purchaseApplyForOrderRepository.findAllById(purchaseApplyIds)
              .stream().collect(Collectors.toMap(PurchaseApplyForOrder::getId, e -> e));
      Map<String, SupplierOrderProduct> supplierOrderProductMap =
          supplierOrderProductRepository.findAllById(productIds)
              .stream().collect(Collectors.toMap(SupplierOrderProduct::getId, e -> e));
      // 如果当前页没有数据或者是最后一页，结束循环
      if (oldData.isEmpty() || !oldDataPage.hasNext()) {
        hasMoreData = false;
      }

      log.info("正在处理第{}页数据，获取到{}条记录", currentPage + 1, oldData.size());
      int currentPageProcessed = 0;
      int currentPageSuccess = 0;
      List<SupplierOrderProduct> updates = new ArrayList<>();
      List<SupplierOrderDetail> updates2 = new ArrayList<>();
      for (SupplierOrderDetail oldDatum : oldData) {
        try {
          currentPageProcessed++;
          totalProcessed++;
          PurchaseApplyForOrder purchaseApplyForOrder =
              purchaseApplyForOrderMap.get(oldDatum.getPurchaseApplyForOrderId());
          SupplierOrderProduct supplierOrderProduct = supplierOrderProductMap.get(oldDatum.getOrderProductId());
          supplierOrderProduct.setSalesman(StrUtil.emptyToNull(purchaseApplyForOrder.getSalesman()));
          supplierOrderProduct.setFollowUpPersonName(StrUtil.emptyToNull(purchaseApplyForOrder.getFollowUpPersonName()));
          supplierOrderProduct.setMakeManName(StrUtil.emptyToNull(purchaseApplyForOrder.getMakeManName()));
          supplierOrderProduct.setBusinessCompanyName(StrUtil.emptyToNull(purchaseApplyForOrder.getBusinessCompanyName()));
          supplierOrderProduct.setSoldToParty(StrUtil.emptyToNull(purchaseApplyForOrder.getSoldToParty()));
          oldDatum.setSalesOrderNo(StrUtil.emptyToNull(purchaseApplyForOrder.getSaleOrderNo() + "-" + purchaseApplyForOrder.getSaleOrderProductRowId()));
          oldDatum.setProjectNo(StrUtil.emptyToNull(purchaseApplyForOrder.getProjectNo()));
          oldDatum.setProjectName(StrUtil.emptyToNull(purchaseApplyForOrder.getProjectName()));
          updates.add(supplierOrderProduct);
          updates2.add(oldDatum);
          currentPageSuccess++;
          totalSuccess++;

          // 每积累指定条数据执行一次批量保存
          if (updates.size() >= batchSize) {
            boolean error = false;
            try {
              supplierOrderProductRepository.saveAll(updates);
            } catch (Exception e) {
              error = true;
            } finally {
              updates.clear();
            }

            try {
              supplierOrderDetailRepository.saveAll(updates2);
            } catch (Exception e) {
              error = true;
            }finally {
              updates2.clear();
            }
            if (Boolean.FALSE.equals(error)) {
              log.info("成功批量更新{}条供应商订单产品数据，当前页进度：{}/{}，总进度：{}",
                  updates.size(), currentPageProcessed, oldData.size(), totalProcessed);
            }
          }

        } catch (Exception e) {
          totalFailed++;
          log.error("处理第{}页数据时发生异常，当前处理进度：{}/{}，总进度：{}，异常信息：{}",
              currentPage + 1, currentPageProcessed, oldData.size(), totalProcessed, e.getMessage());
          continue;
        }
      }
      // 保存最后一批未达到阈值的数据
      if (!updates.isEmpty()) {
        boolean error = false;
        try {
          supplierOrderProductRepository.saveAll(updates);
        } catch (Exception e) {
          error = true;
        } finally {
          updates.clear();
        }

        try {
          supplierOrderDetailRepository.saveAll(updates2);
        } catch (Exception e) {
          error = true;
        }finally {
          updates2.clear();
        }
        if (Boolean.FALSE.equals(error)) {
          log.info("成功批量更新剩余{}条供应商订单产品数据，当前页共处理：{}/{}，当前页成功：{}，总进度：{}",
              updates.size(), currentPageProcessed, oldData.size(), currentPageSuccess, totalProcessed);
        }
      }

      log.info("第{}页处理完成，处理：{}条，成功：{}条，总处理：{}条，总成功：{}条，总失败：{}条",
          currentPage + 1, currentPageProcessed, currentPageSuccess,
          totalProcessed, totalSuccess, totalFailed);
      // 进入下一页
      currentPage++;
    }

    long endTime = System.currentTimeMillis();
    log.info("供应商订单产品数据更新任务完成，总处理：{}条，成功：{}条，失败：{}条，耗时：{}秒",
        totalProcessed, totalSuccess, totalFailed, (endTime - startTime) / 1000);
  }
}
