package com.xhgj.srm.mission.consumer.handlers.exports.execs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.enums.contract.LandingMerchantContractPaymentConditionEnum;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.dto.order.OrderPaymentDetailExportDTO;
import com.xhgj.srm.dto.order.OrderPaymentListQuery;
import com.xhgj.srm.jpa.dao.OrderPaymentToOrderDao;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderNeedPayment;
import com.xhgj.srm.jpa.entity.OrderPayment;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrder;
import com.xhgj.srm.jpa.entity.OrderPaymentToOrderLink;
import com.xhgj.srm.jpa.entity.OrderReceiptRecord;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.OrderNeedPaymentRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentDao;
import com.xhgj.srm.jpa.repository.OrderPaymentRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentToOrderLinkRepository;
import com.xhgj.srm.jpa.repository.OrderPaymentToOrderRepository;
import com.xhgj.srm.jpa.repository.OrderReceiptRecordRepository;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.factory.MapStructFactory;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.exports.ExportMissionCompleteResult;
import com.xhgj.srm.service.SharePlatformService;
import com.xhgj.srm.service.TempSupplierUserService;
import com.xhiot.boot.core.common.exception.CheckException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * OrderPaymentExportExec
 */
@Slf4j
@Component
public class OrderPaymentExecService implements ExecService {

  @Resource
  private OrderPaymentRepository orderPaymentRepository;
  @Resource
  private TempSupplierUserService supplierUserService;
  @Resource
  private UserRepository userRepository;
  @Resource
  private OrderPaymentDao orderPaymentDao;
  @Resource
  private OrderPaymentToOrderDao orderPaymentToOrderDao;
  @Resource
  private SharePlatformService platformService;
  @Resource
  private OrderPaymentToOrderRepository orderPaymentToOrderRepository;
  @Resource
  private OrderRepository orderRepository;
  @Resource
  private FcMissionService fcMissionService;
  @Resource
  private ExportUtil ex;
  @Resource
  private OrderPaymentToOrderLinkRepository orderPaymentToOrderLinkRepository;
  @Resource
  private OrderNeedPaymentRepository orderNeedPaymentRepository;
  @Resource
  private OrderReceiptRecordRepository orderReceiptRecordRepository;


  @SneakyThrows
  @Override
  public ExportMissionCompleteResult exec(Collection<?> collection,
      MissionDispatchParam dispatchParam) {
    try (XSSFWorkbook book = new XSSFWorkbook();) {
    CellStyle baseStyle = ex.getBaseStyle(book);
    CellStyle titleStyle = ex.getTitleStyle(book);
    titleStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
    titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    List<String> titles = new ArrayList<>(Constants_Excel.EXPORT_ORDER_PAYMENT_LIST);
    List<Integer> titleSize = new ArrayList<>();
    for (String t : Constants_Excel.EXPORT_ORDER_PAYMENT_LIST) {
      titleSize.add(30);
    }
    Sheet sheet = ex.createSheet(book, "付款单导出", titleSize);
    Row rowTitle = sheet.createRow(0);
    ex.createTitle(titles, titleStyle, rowTitle);
    List<OrderPaymentDetailExportDTO> vos = (List<OrderPaymentDetailExportDTO>) collection;
    int count = 0;
    // 写入数据
    int index = 1; // 假设从第二行开始写入数据
      for (int i = 0; i < vos.size(); i++) {
        int col = 0;
        int rowNum = index + 1;
        Row row = sheet.createRow(index);
        OrderPaymentDetailExportDTO vo = vos.get(i);
        ex.createCell(row, col++, vo.getPaymentNo(), baseStyle);
        ex.createCell(row, col++, vo.getPaymentStatus(), baseStyle);
        ex.createCell(row, col++, vo.getOrderCount(), baseStyle);
        ex.createCell(row, col++, vo.getApplyPrice(), baseStyle);
        ex.createCell(row, col++, vo.getPaymentPrice(), baseStyle);
        ex.createCell(row, col++, vo.getSubmitMan(), baseStyle);
        ex.createCell(row, col++, vo.getCreateTime(), baseStyle);
        ex.createCell(row, col++, vo.getAutoDraw(), baseStyle);
        ex.createCell(row, col++, vo.getOrderNo(), baseStyle);
        ex.createCell(row, col++, vo.getPlatform(), baseStyle);
        // 签收凭证
        ex.createCell(row, col++, Constants_order.SIGN_VOUCHER_MAP.get(vo.getSignVoucherState()), baseStyle);
        // 供应商开票
        ex.createCell(row, col++, vo.getOrderInvoiceStatus(), baseStyle);
        // 客户回款方式
        ex.createCell(row, col++, vo.getCustomerPaymentType(), baseStyle);
        ex.createCell(row, col++, vo.getShowRate(), baseStyle);
        ex.createCell(row, col++, vo.getBankSerialNo(), baseStyle);
        ex.createCell(row, col++, vo.getBillNo(), baseStyle);
        //付款发起条件
        String paymentTerms = StrUtil.EMPTY;
        StringBuilder paymentTermsBuild = new StringBuilder();
        if (StrUtil.isNotBlank(vo.getPaymentCondition())) {
          String[] termsArray = StrUtil.split(vo.getPaymentCondition(), StrUtil.COMMA);
          for (String s : termsArray) {
            Optional.ofNullable(LandingMerchantContractPaymentConditionEnum.valueOfByKey(s))
                .ifPresent(item -> {
                  paymentTermsBuild.append(item.getValue());
                  paymentTermsBuild.append(StrUtil.COMMA);
                });
          }
          // 去除最后一个,
          if (paymentTermsBuild.length() > 0) {
            paymentTerms = paymentTermsBuild.substring(0, paymentTermsBuild.length() - 1);
          }
        }
        ex.createCell(row, col++, paymentTerms, baseStyle);
        ex.createCell(row, col++, vo.getPaymentConditionTime(), baseStyle);
        // 对应账期
        ex.createCell(row, col++, vo.getAccountingPeriod(), baseStyle);
        ex.createCell(row, col++, vo.getPaymentDate(), baseStyle);
        // 付款方式
        ex.createCell(row, col++, vo.getPaymentMethod(), baseStyle);
        ex.createCell(row, col++, vo.getSupplierName(), baseStyle);
        ex.createCell(row, col++, vo.getFinalPrice(), baseStyle);
        ex.createCell(row, col++, vo.getWithdrawalAmount(), baseStyle);

        index++;
        count++;
        fcMissionService.createMissionDetail(dispatchParam.getMissionId(), "第【" + rowNum + "】行",
            StrUtil.EMPTY);
      }

    String now = String.valueOf(System.currentTimeMillis());
    String fileNewName = "付款单" + now + ".xlsx";
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
    book.write(outputStream);
    return ExportMissionCompleteResult.builder()
        .successCount(count)
        .fileName(fileNewName)
        .bytes(outputStream.toByteArray())
        .build();
    }
  }

  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
    String userId = dispatchParam.getUserId();
    String params = dispatchParam.getParams();
    User user = userRepository.findById(userId).orElse(null);
    String supplierId = StrUtil.EMPTY;
    if (user == null) {
      SupplierUser supplierUser = supplierUserService.get(userId,
          () -> CheckException.noFindException(SupplierUser.class, userId));
      supplierId = supplierUser.getSupplierId();
    }
    List<OrderPayment> orderPaymentList;
    OrderPaymentListQuery orderPaymentListQuery;
    JSONObject jsonObject = JSON.parseObject(params);

    List<String> ids = Optional.ofNullable(jsonObject)
        .map(obj -> obj.getJSONArray("ids"))
        .map(jsonArray -> jsonArray.toJavaList(String.class))
        .orElseGet(ArrayList::new);
    if (CollUtil.isNotEmpty(ids)) {
      orderPaymentList = orderPaymentRepository.findAllById(ids);
    } else {
//      BeanUtil.copyProperties(jsonObject, orderPaymentListQuery);
      orderPaymentListQuery = MapStructFactory.INSTANCE.toOrderPaymentListQuery(jsonObject);
      Page<OrderPayment> page =
          orderPaymentDao.findPageRef(orderPaymentListQuery.toQueryMap(supplierId));
      orderPaymentList = page.getContent();
    }
    List<OrderPaymentDetailExportDTO> detailExportDTOS = new ArrayList<>();
    for (OrderPayment orderPayment : orderPaymentList) {
      List<OrderPaymentToOrderLink> paymentToOrderLinkList =
          orderPaymentToOrderLinkRepository.findByPaymentId(orderPayment.getId());
      if (CollUtil.isNotEmpty(paymentToOrderLinkList)) {
        detailExportDTOS.addAll(paymentToOrderLinkList.stream().map(link -> {
          Order order =
              link.getOrderId() != null ? orderRepository.findById(link.getOrderId()).orElse(null)
                  : null;
          OrderReceiptRecord orderReceiptRecord =
              link.getReceiptRecordId() != null ? orderReceiptRecordRepository.findById(
                  link.getReceiptRecordId()).orElse(null) : null;
          String platformName =
              order != null ? platformService.findNameByCode(order.getType()) : StrUtil.EMPTY;
          BigDecimal showRate = orderReceiptRecord != null ? Optional.ofNullable(
                  orderNeedPaymentRepository.findFirstByOrderReceiptIdAndState(
                      orderReceiptRecord.getId(), Constants.STATE_OK))
              .map(OrderNeedPayment::getShowRate).orElse(BigDecimal.ZERO) : BigDecimal.ZERO;
          return new OrderPaymentDetailExportDTO(orderPayment, order, platformName,
              orderReceiptRecord, showRate, link.getAmount());
        }).collect(Collectors.toList()));
      } else {
        detailExportDTOS.add(new OrderPaymentDetailExportDTO(orderPayment));
      }
    }
    return detailExportDTOS;
  }

}
