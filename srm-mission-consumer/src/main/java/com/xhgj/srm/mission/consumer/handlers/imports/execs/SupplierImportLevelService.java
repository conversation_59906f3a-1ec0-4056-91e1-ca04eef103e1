package com.xhgj.srm.mission.consumer.handlers.imports.execs;/**
 * @since 2024/12/18 14:18
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.repository.SupplierInGroupRepository;
import com.xhgj.srm.mission.common.MissionDispatchParam;
import com.xhgj.srm.mission.consumer.framework.MissionCompleteResult;
import com.xhgj.srm.mission.consumer.framework.service.FcMissionService;
import com.xhgj.srm.mission.consumer.handlers.ExecService;
import com.xhgj.srm.mission.consumer.handlers.imports.ImportMissionCompleteResult;
import com.xhgj.srm.mission.consumer.handlers.imports.common.SupplierImportCommon;
import com.xhgj.srm.mission.consumer.handlers.imports.execs.params.supplier.SupplierImportParam;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SupplierImportLevelService implements ExecService {

  @Resource
  DownloadThenUpUtil downloadThenUpUtil;
  @Resource
  SupplierImportCommon supplierImportCommon;
  @Resource
  FcMissionService fcMissionService;
  @Resource
  SupplierInGroupRepository supplierInGroupRepository;

  @Override
  public MissionCompleteResult exec(Collection<?> collection, MissionDispatchParam dispatchParam) {
    // 转换为JSONObject
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 获取filePath
    String filePath = jsonObject.getString("filePath");
    // 获取fileName
    String fileName = jsonObject.getString("fileName");
    // collection转换为SupplierImportParam
    List<SupplierImportParam> supplierImportParams = (List<SupplierImportParam>) collection;
    // 执行导入
    Iterator<SupplierImportParam> iterator = supplierImportParams.iterator();
    int rowIndex = 1;
    int successCount = 0;
    while (iterator.hasNext()) {
      SupplierImportParam param = iterator.next();
      try {
        // 0.校验必填项
        supplierImportCommon.checkImportValue(param, this);
        // 1.校验供应商
        SupplierInGroup supplierInGroup = supplierImportCommon.checkSupplierInGroup(param);
        // 2.更新等级
        supplierInGroup.setEnterpriseLevel(param.getSupplierLevel());
        supplierInGroup.setUpdateTime(System.currentTimeMillis());
        supplierInGroup.setUpdateMan(param.getUserId());
        supplierInGroupRepository.saveAndFlush(supplierInGroup);
        fcMissionService.createMissionDetail(dispatchParam.getMissionId(),
            String.format("第【%s】行", rowIndex), StrUtil.EMPTY);
        successCount++;
        iterator.remove();
      } catch (Exception e) {
        log.error("导出供应商等级异常,第【{}】行数据写入失败,异常信息为:", rowIndex, e);
        fcMissionService.createMissionDetail(dispatchParam.getMissionId(),
            String.format("第【%s】行,", rowIndex), e.getMessage());
      }
      rowIndex++;
    }
    return ImportMissionCompleteResult.builder()
        .successCount(successCount)
        .fileName(fileName)
        .filePath(filePath)
        .build();
  }

  @Override
  public Collection<?> prepare(MissionDispatchParam dispatchParam) {
    // 转换为JSONObject
    JSONObject jsonObject = JSONObject.parseObject(dispatchParam.getParams());
    // 获取用户id
    String userId = jsonObject.getString("userId");
    // 获取filePath
    String filePath = jsonObject.getString("filePath");
    // 获取fileName
    String fileName = jsonObject.getString("fileName");
    // oss下载文件
    List<SupplierImportParam> result = new ArrayList<>();
    try ( InputStream inputStream = downloadThenUpUtil.getInputStreamFromOSS(filePath);
        Workbook book = ExcelUtil.buildByFile(fileName, inputStream);) {
      if (book == null) {
        throw new CheckException("文件为空");
      }
      // 获取sheet
      Sheet sheet = book.getSheetAt(0);
      // 获取总行数
      int lastRowNum = sheet.getPhysicalNumberOfRows();
      // 如果总行数小于1则抛出异常
      if (lastRowNum < 1) {
        throw new CheckException("导入数据为空");
      }
      // 从第二行开始读取
      int startRow = 1;
      // 读取数据
      for (int i = startRow; i <= lastRowNum; i++) {
        // 获取当前行
        Row row = sheet.getRow(i);
        if (row == null) {
          continue;
        }
//        boolean isRowEmpty = true;
//        for (Cell cell : row) {
//          if (cell != null && !cell.toString().trim().isEmpty()) {
//            isRowEmpty = false;
//            break;
//          }
//        }
//        if (isRowEmpty) {
//          continue;
//        }
//        int lastCellNum = row.getLastCellNum();
//        if (lastCellNum < 4) {
//          throw new CheckException("导入数据列数不足");
//        }
        int cellNum = 0;
        // 获取固定列
        // 供应商名称
        String supplierName = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 供应商编码
        String mdmCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 供应商组织编码
        String groupCode = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        // 供应商等级
        String level = ExcelUtil.getCellStringValue(row.getCell(cellNum++));
        SupplierImportParam one =
            SupplierImportParam.builder().supplierName(supplierName).mdmCode(mdmCode)
                .groupCode(groupCode).supplierLevel(level)
                .userId(userId)
                .build();
        result.add(one);
      }
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    if (CollUtil.isEmpty(result)) {
      throw new CheckException("导入数据为空");
    }
    return result;
  }
}

