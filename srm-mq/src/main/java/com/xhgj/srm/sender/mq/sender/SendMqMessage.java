package com.xhgj.srm.sender.mq.sender;

import com.xhgj.srm.sender.mq.QueueEnum;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON> <PERSON>hy on 2023/9/22
 */
@Component
public class SendMqMessage {
  @Autowired
  private AmqpTemplate rabbitTemplate;

  /**
   * 同步签收凭证信息给crm系统
   * @param payload 负载
   */
  public void sendReceiptVoucherMessage(String payload) {
    rabbitTemplate.convertAndSend(
        QueueEnum.QUEUE_SRM_TO_CRM_RECEIPT_VOUCHER.getExchange(),
        QueueEnum.QUEUE_SRM_TO_CRM_RECEIPT_VOUCHER.getName(), payload);
    // 发送给oms
    rabbitTemplate.convertAndSend(
        QueueEnum.QUEUE_SRM_TO_OMS_RECEIPT_VOUCHER.getExchange(),
        QueueEnum.QUEUE_SRM_TO_OMS_RECEIPT_VOUCHER.getName(), payload);
  }

}
