package com.xhgj.srm.sender.mq.sender;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.sender.mq.QueueEnum;
import com.xhgj.srm.sender.mq.payload.PurchaseInfoRecordPayload;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/1/2 11:21
 */
@Component
public class PurchaseInfoRecordMQSender {
  @Autowired private RabbitTemplate template;

  /**
   * 发送创建采购价格库的消息
   *
   * @param orderCode 订单编码
   */
  public void sendMsg(String orderCode) {
    Assert.notBlank(orderCode);
    sendJsonMsg(
        JSON.toJSONString(new PurchaseInfoRecordPayload(orderCode, System.currentTimeMillis())));
  }

  /**
   * 发送创建采购价格库的消息
   *
   * @param orderCode 订单编码
   */
  public void sendMsg(String orderCode, String versionContext) {
    Assert.notBlank(orderCode);
    sendJsonMsg(JSON.toJSONString(
        new PurchaseInfoRecordPayload(orderCode, versionContext, System.currentTimeMillis())));
  }

  /**
   * 发送创建采购价格库的消息
   *
   * @param json 已经处理好的 json 消息体
   */
  public void sendJsonMsg(String json) {
    template.convertAndSend(
        QueueEnum.QUEUE_PURCHASE_INFO_RECORD_CREATE.getExchange(),
        QueueEnum.QUEUE_PURCHASE_INFO_RECORD_CREATE.getRouteKey(),
        json);
  }
}
