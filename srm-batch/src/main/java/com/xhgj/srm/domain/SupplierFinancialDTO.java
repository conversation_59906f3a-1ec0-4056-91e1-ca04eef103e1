package com.xhgj.srm.domain;

import com.xhgj.srm.jpa.entity.Financial;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/11 14:18
 */
@Data
@NoArgsConstructor
public class SupplierFinancialDTO {

  @ApiModelProperty("开户银行")
  private String bankName;

  @ApiModelProperty("银行账号")
  private String bankNum;

  @ApiModelProperty("账户名称")
  private String bankAccount;

  @ApiModelProperty("开户行地址")
  private String bankAddress;

  @ApiModelProperty("联行号")
  private String bankCode;

  @ApiModelProperty("swiftcode")
  private String swiftCode;

  @ApiModelProperty("开户许可证，仅国内供应商")
  private String accountUrl;

  public SupplierFinancialDTO(Financial financial) {
    this.bankName = financial.getBankName();
    this.bankNum = financial.getBankNum();
    this.bankAccount = financial.getBankAccount();
    this.bankAddress = financial.getBankAddress();
    this.bankCode = financial.getBankCode();
    this.swiftCode = financial.getSwiftCode();
    this.accountUrl = financial.getAccountUrl();
  }
}
