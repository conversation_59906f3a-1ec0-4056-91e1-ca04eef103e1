package com.xhgj.srm.utils;

import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.jpa.entity.Product;
import com.xhiot.boot.core.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class ProductExcelUtil {

    @Autowired
    ExportUtil ex;


    public Sheet buildSheet(Workbook book, String sheetName) {
        List<String> titles = Constants_Excel.PRODUCT_YDR_EXCEL_TITLE_LIST;
        List<Integer> titlesWidth = Constants_Excel.PRODUCT_YDC_EXCEL_EXPORT_TITLE_WIDTH_LIST;
        return buildSheet(book, sheetName, titlesWidth, titles);
    }

    /**
     * 构造 sheet 并生成表头
     */
    public Sheet buildSheet(
            Workbook book, String sheetName, List<Integer> titlesWidth, List<String> titles) {
        Sheet sheet = ex.createSheet(book, sheetName, titlesWidth);
        CellStyle titleStyle = ex.getTitleStyle(book);
        titleStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        buildExcelHeader(book, sheet, titles.size() - 1, 0);
        return sheet;
    }


    public void buildExcelHeader(Workbook workbook, Sheet sheet, int titleSize, int fieldSize) {
        // 首行标题行
        Row firstRow = sheet.createRow(0);
        // 行高 29
        firstRow.setHeight((short) (29 * 20));
        CellStyle firstStyle = workbook.createCellStyle();
        // 背景色 25%灰
        firstStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        firstStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 水平居中
        firstStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        firstStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 字体
        Font font = workbook.createFont();
        font.setFontName("黑体");
        // 字号
        font.setFontHeightInPoints((short) 12);
        firstStyle.setFont(font);
        ex.createCell(firstRow, 0, "物料预导出", firstStyle);
        // 合并单元格
        ex.createRegion(sheet, 0, 0, 0, titleSize);
        // 第二行属性分组
        // 样式
        CellStyle split0 = ex.getBaseStyle(workbook);
        split0.setFillForegroundColor(IndexedColors.GOLD.getIndex());
        split0.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        split0.setFont(font);
        CellStyle split1 = ex.getBaseStyle(workbook);
        split1.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        split1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        split1.setFont(font);
        Row secondRow = sheet.createRow(1);
        secondRow.setHeight((short) (21 * 27));
        CellStyle baseStyle = ex.getBaseStyle(workbook);
        ex.createCell(secondRow, 0, "物料编码", baseStyle);
        ex.createCell(secondRow, 1, "名称", baseStyle);
        ex.createCell(secondRow, 2, "四级类目名称", baseStyle);
        ex.createCell(secondRow, 3, "品牌", baseStyle);
        ex.createCell(secondRow, 4, "基本单位名称", baseStyle);
        ex.createCell(secondRow, 5, "型号规格", baseStyle);
        ex.createCell(secondRow, 6, "起订量", baseStyle);
        ex.createCell(secondRow, 7, "拓展属性,绿色为必填属性", split0);
        if (fieldSize > 0) {
            ex.createRegion(sheet, 1, 1, 7, 7 + fieldSize);
        }
    }


    public void buildRow(
            Product product, Row row, CellStyle baseStyle) {
        ex.createCell(row, 0, product.getTempCode(), baseStyle);
        ex.createCell(row, 1, product.getName(), baseStyle);
        ex.createCell(row, 2, product.getFourthCateName(), baseStyle);
        ex.createCell(row, 3, product.getBrandnameCn() + "/" + product.getBrandnameEn(), baseStyle);
        ex.createCell(row, 4, product.getBasicUnit(), baseStyle);
        ex.createCell(row, 5, product.getModel(), baseStyle);
        ex.createCell(row, 6, !StringUtils.isNullOrEmpty(product.getOrderQuantity()) ? product.getOrderQuantity() : "", baseStyle);
    }
}
