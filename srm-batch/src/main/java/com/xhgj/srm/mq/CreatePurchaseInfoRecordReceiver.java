package com.xhgj.srm.mq;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.sender.mq.Constants_MQ;
import com.xhgj.srm.sender.mq.payload.PurchaseInfoRecordPayload;
import com.xhgj.srm.service.PurchaseInfoRecordService;
import com.xhgj.srm.unified.factory.UnifiedSqlFactory;
import com.xhiot.boot.core.config.BootConfig;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 监听采购价格库创建消息（采购订单审核通过）
 *
 * <AUTHOR>
 * @since 2024/1/2 13:13
 */
@Component
@Slf4j
public class CreatePurchaseInfoRecordReceiver {
  @Autowired private PurchaseInfoRecordService purchaseInfoRecordService;
  @Autowired private BootConfig bootConfig;

  @RabbitHandler
  @RabbitListener(queues = Constants_MQ.QUEUE_NAME_MQ_PURCHASE_INFO_RECORD_CREATE)
  public void handleTask(String payload, Channel channel, Message message) throws IOException {
    String routingKey = message.getMessageProperties().getReceivedRoutingKey();
    log.info("process {} payload:【{}】", routingKey, payload);
    try {
      PurchaseInfoRecordPayload recordPayload =
          JSON.parseObject(payload, PurchaseInfoRecordPayload.class);
      VersionEnum versionEnum = UnifiedSqlFactory.convertToTargetType(recordPayload.getVersionContext(), VersionEnum.class);
      if (versionEnum != null) {
        ShardingContext.setVersion(versionEnum);
      }
      purchaseInfoRecordService.createByPurchaseOrder(
          recordPayload.getOrderCode(), recordPayload.getMsgTime());
    } catch (Exception e) {
      log.error("payload: 【" + payload + "】");
      log.error(ExceptionUtil.stacktraceToString(e));
      DingUtils.sendMsgByWarningRobot(
          "【"
              + bootConfig.getEnv()
              + "环境 "
              + bootConfig.getAppName()
              + "】根据采购订单生成采购价格库消息消费失败，消息体【"
              + payload
              + "】，异常信息【"
              + e.getMessage()
              + "】，可再次生成一条消息处理该数据，请及时处理",
          bootConfig.getEnv());
    } finally {
      ShardingContext.clear();
      channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }
  }
}
