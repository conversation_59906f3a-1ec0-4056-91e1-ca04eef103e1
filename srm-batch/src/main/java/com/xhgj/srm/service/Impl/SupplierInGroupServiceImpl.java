package com.xhgj.srm.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.xhgj.srm.jpa.dao.GroupDao;
import com.xhgj.srm.jpa.dao.SupplierInGroupDao;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.repository.SupplierInGroupRepository;
import com.xhgj.srm.service.GroupService;
import com.xhgj.srm.service.SupplierInGroupService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/7/12 15:24
 */
@Service
@Slf4j
public class SupplierInGroupServiceImpl implements SupplierInGroupService {

  @Autowired SupplierInGroupRepository repository;

  @Autowired SupplierInGroupDao dao;

  @Autowired GroupService groupService;
  @Resource
  private GroupDao groupDao;

  private final String DEFAULT_PRINCIPAL = "万聚国际（杭州）供应链有限公司";

  @Override
  public BootBaseRepository<SupplierInGroup, String> getRepository() {
    return repository;
  }

  @Override
  public List<SupplierInGroup> getSupplierInGroupChinaByUserId(
      String userId, String groupId, List<String> supplierInGroupIdList) {
    Assert.notEmpty(userId);
    Assert.notEmpty(groupId);
    return dao.getSupplierInGroupChinaByUserId(userId, groupId, supplierInGroupIdList);
  }

  @Override
  public List<SupplierInGroup> getSupplierInGroupAbroadByUserId(String userId, String groupId,
      List<String> supplierInGroupIdList) {
    Assert.notEmpty(userId);
    Assert.notEmpty(groupId);
    return dao.getSupplierInGroupAbroadByUserId(userId, groupId, supplierInGroupIdList);
  }

  @Override
  public List<SupplierInGroup> getSupplierInGroupPersonByUserId(String userId, String groupId,
      List<String> supplierInGroupIdList) {
    Assert.notEmpty(userId);
    Assert.notEmpty(groupId);
    return dao.getSupplierInGroupPersonByUserId(userId, groupId, supplierInGroupIdList);
  }

  @Override
  public SupplierInGroup getSupplierInGroupByEnterpriseNameAndGroupCode(String enterpriseName,
      String groupCode,String supType) {
    Assert.notEmpty(enterpriseName);
    Assert.notEmpty(groupCode);
    return dao.getSupplierInGroupByEnterpriseNameAndGroupCode(enterpriseName,groupCode,supType);
  }

  @Override
  public SupplierInGroup getSupplierInGroupByMDMAndGroupCode(String mdmCode, String groupCode) {
    Assert.notEmpty(mdmCode);
    Assert.notEmpty(groupCode);
    return dao.getSupplierInGroupByMDMAndGroupCode(mdmCode, groupCode);
  }

  @Override
  public boolean isWanJuOrganization(Supplier supplier) {
    if (supplier == null) {
      return false;
    }
    //查询该供应商所有对应的组织
    List<SupplierInGroup> supplierInGroups = dao.getAllBySupplier(supplier.getId());
    if (CollUtil.isEmpty(supplierInGroups)) {
      return false;
    }
    //组织去重
    Set<String> organizations = supplierInGroups.stream().map(supplierInGroup -> {
      String groupId = supplierInGroup.getGroupId();
      Group group = groupDao.get(groupId);
      if (group != null) {
        return group.getName();
      }
      return "";
    }).collect(Collectors.toSet());
    return organizations.contains(DEFAULT_PRINCIPAL);
  }
}
