package com.xhgj.srm.service.Impl;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.sender.mq.sender.BatchTaskMqSender;
import com.xhgj.srm.service.EntryRegistrationExcelService;
import com.xhgj.srm.service.ExcelService;
import com.xhgj.srm.service.TaskService;
import com.xhiot.boot.core.common.exception.CheckException;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

@Slf4j
@Service
public class TaskServiceImpl implements TaskService {

    @Autowired
    ExcelService excelService;
    @Autowired
    BatchTaskMqSender batchSupplierMqSender;
    @Autowired
    MissionRepository missionRepository;
  @Resource
  EntryRegistrationExcelService entryRegistrationExcelService;

    @Override
    public void handleTask(String missionId, String params, String type) {
      ShardingContext.clear();
      ShardingContext.setVersion(params);
        String source = "1";
      Mission mission = missionRepository.findById(missionId)
          .orElseThrow(() -> CheckException.noFindException(Mission.class, missionId));
      // 设置开始时间
      mission.setStartTime(System.currentTimeMillis());
      missionRepository.save(mission);
       if (Constants_Batch.BATCH_TASK_YDC.equals(type)) {
            excelService.exportHCProduct(mission, params);
        } else if (Constants_Batch.BATCH_TASK_SXFG.equals(type)) {
            excelService.updateImportProductExpand(mission, params);
        } else if (Constants_Batch.BATCH_TASK_PGGYSFZCZ.equals(type)) {
            excelService.updateExcelSupplierManage(mission);
            source = "2";
        } else if (Constants_Batch.BATCH_TASK_PGHTFZR.equals(type)) {
            excelService.updateExcelContractManager(mission);
            source = "2";
        } else if (Constants_Batch.BATCH_TASK_INQUIRY.equals(type)) {
            excelService.InquiryBatchExcelImpSave(mission, params);
            source = "2";
        } else if (Constants_Batch.BATCH_TASK_DCHT.equals(type)) {
            excelService.exportContract(mission, params);
            source = "2";
        } else if (Constants_Batch.BATCH_TASK_DCGYS.equals(type)) {
            excelService.exportNormalSupplier(mission, params);
            source = "2";
        } else if (Constants_Batch.BATCH_TASK_FILLING_IN.equals(type)) {
            excelService.FilingBatchExcelImpSave(mission, params);
        } else if (Constants_Batch.BATCH_TASK_FILLING_OUT.equals(type)) {
            excelService.exportFilling(mission, params);
        } else if (Constants_Batch.BATCH_TASK_GROUP_IN.equals(type)){
            // 导入组织
            excelService.importGroup(mission, params);
            source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_GROUP_OUT.equals(type)){
            // 导出组织
            excelService.exportGroup(mission, params);
            source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_DEPARTMENT_IN.equals(type)){
            // 导入部门
            excelService.importDepartment(mission, params);
            source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_DEPARTMENT_OUT.equals(type)){
            // 导出部门
            excelService.exportDepartment(mission, params);
            source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_USER_IN.equals(type)){
            // 导入用户
            excelService.importUser(mission, params);
            source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_USER_OUT.equals(type)){
            // 导出用户
            excelService.exportUser(mission, params);
            source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_SUPPLIER_IN_GROUP_OUT.equals(type)){
            // 导出供应商列表
            excelService.exportSupplierInGroup(mission, params);
            source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_SUPPLIER_IN_GROUP_IN.equals(type)){
            // 导入供应商列表
            excelService.importSupplierInGroup(mission, params);
            source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_IMPORT_CORRECTION_CONTACT.equals(type)){
            // 导入批改联系人
            excelService.importCorrectionContact(mission, params);
            source = Constants.PLATFORM_TYPE_AFTER;
    } else if (Constants_Batch.BATCH_TASK_EXPORT_SUPPLIER_ORDER_DETAILS.equals(type)) {
      excelService.exportSupplierOrderDetails(mission, params);
      source = Constants.PLATFORM_TYPE_AFTER;
    } else if (Constants_Batch.BATCH_TASK_EXPORT_SUPPLIER_ORDER.equals(type)) {
      excelService.exportSupplierOrder(mission, params);
      source = Constants.PLATFORM_TYPE_AFTER;
    } else if (Constants_Batch.BATCH_TASK_EXPORT_SUPPLIER_ORDER_DETAILS_SUPPLIER.equals(type)) {
      excelService.exportSupplierOrderDetailsBySupplier(mission, params);
      source = Constants.PLATFORM_TYPE_AFTER;
    } else if (Constants_Batch.BATCH_TASK_EXPORT_ORDER.equals(type)) {
      excelService.exportOrder(mission, params);
      source = Constants.PLATFORM_TYPE_AFTER;
        }else if(Constants_Batch.BATCH_TASK_ORDER_INVOICE.equals(type)){
          excelService.exportOrderInvoice(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
    } else if (Constants_Batch.BATCH_TASK_EXPORT_ORDER_PRODUCT_DETAIL.equals(type)) {
      excelService.exportOrderProductDetail(mission, params);
      source = Constants.PLATFORM_TYPE_AFTER;
    } else if (Constants_Batch.BATCH_TASK_PRODUCT_IN.equals(type)) {
          excelService.newProductBatchExcelImpSave(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        }else if(Constants_Batch.BATCH_TASK_PROJECT_IN.equals(type)){
          excelService.ProjectBatchExcelImpSave(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        }else if(Constants_Batch.BATCH_TASK_PICTURE_IN.equals(type)){
          excelService.PictureBatchExcelImpSave(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        } else if (Constants_Batch.BATCH_TASK_LANDING_MERCHANT_PERFORMANCE_INFO_IN.equals(type)) {
          excelService.importLandingMerchantPerformanceInfo(mission);
          source = Constants_Batch.SOURCE_TYPE;
        } else if (Constants_Batch.BATCH_TASK_ORDER_ACCOUNT_OUT.equals(type)) {
          excelService.exportOrderAccount(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_EXPORT_ORDER_NEW.equals(type)) {
          excelService.exportOrderNew(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_PRODUCT_STOCK.equals(type)) {
          excelService.exportProductStock(mission, params);
          source = Constants.PLATFORM_TYPE_BEFORE;
        }else if (Constants_Batch.BATCH_TASK_PRODUCT_STOCK_IN.equals(type)) {
          excelService.productStockImport(mission, params);
          source = Constants.PLATFORM_TYPE_BEFORE;
        } else if (Objects.equals(Constants_Batch.BATCH_TASK_CONTRACT_IMPORT, type)) {
          excelService.importContractInfo(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        } else if (Objects.equals(Constants_Batch.BATCH_TASK_IMPORT_CONTRACT_FILE, type)) {
          excelService.batchImportContractFile(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        } else if (Constants_Batch.BATCH_TASK_EXPORT_PURCHASE_INFO_RECORD.equals(type)) {
          excelService.exportPurchaseInfoRecord(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        } else if (Constants_Batch.BATCH_TASK_EXPORT_PURCHASE_APPLY_FOR_ORDER.equals(type)){
          excelService.exportPurchaseApplyForOrder(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_IMPORT_RELATION_TICKET.equals(type)){
          excelService.importRelationLargeTicket(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        } else if (Constants_Batch.BATCH_TASK_SUPPLIER_REGISTERED_ADDRESS.equals(type)) {
          excelService.updateSupplierRegisteredAddress(mission);
          source = Constants.PLATFORM_TYPE_AFTER;
        } else if (Constants_Batch.BATCH_TASK_SUPPLIER_EMPOWER.equals(type)) {
          excelService.updateSupplierEmpower(mission);
        } else if (Constants_Batch.BATCH_TASK_EXPORT_PAYMENT_APPLICATION_FINANCIAL_VOUCHERS.equals(
            type)) {
          excelService.exportFinancialVouchers(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        } else if (Constants_Batch.BATCH_TASK_EXPORT_PAYMENT_APPLICATION_INVOICE_VOUCHERS.equals(
            type)) {
          excelService.exportInvoiceVouchers(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        }else if(Constants_Batch.BATCH_TASK_EXPORT_PURCHASE_ORDER.equals(type)){
          excelService.exportPurchaseOrder(mission, params);
          source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_EXPORT_SUPPLIER_ORDER_PRODUCT_DETAIL.equals(type)) {
          excelService.exportPurchaseOrderProduct(mission,params);
          source = Constants.PLATFORM_TYPE_AFTER;
        } else if (Constants_Batch.BATCH_PRINT_OUT_PURCHASE_APPLY_ORDER_ACCEPT_TEMP.equals(type)) {
          excelService.batchPrintOutPurchaseApplyOrderAcceptTemp(mission,params);
          source = Constants.PLATFORM_TYPE_AFTER;
    } else if (Constants_Batch.THE_GROUND_QUOTIENT_IS_DERIVED.equals(type)) {
      excelService.exportLandingMerchantContract(mission, params);
      source = Constants.PLATFORM_TYPE_AFTER;
    } else if (Constants_Batch.BATCH_TASK_IMPORT_WAREHOUSE_INVOICE_NUM.equals(type)) {
          excelService.importWarehouseInvoiceNum(mission,params);
          source = Constants.PLATFORM_TYPE_AFTER;
        }else if (Constants_Batch.BATCH_TASK_IMPORT_FINANCIAL_VOUCHER.equals(type)) {
          excelService.importFinancialVoucher(mission,params);
          source = Constants.PLATFORM_TYPE_AFTER;
    } else if (Constants_Batch.BATCH_TASK_EXPORT_ORDER_DETAIL.equals(type)) {
      excelService.exportOrderDetail(mission, params);
      source = Constants.PLATFORM_TYPE_AFTER;
    } else if (Constants_Batch.BATCH_TASK_IMPORT_RETURN_RED_INVOICE_NUM.equals(type)) {
          excelService.importReturnRedInvoiceNum(mission,params);
          source = Constants.PLATFORM_TYPE_AFTER;
      } else if (Constants_Batch.BATCH_TASK_EXPORT_ENTRY_REGISTRATION.equals(type)) {
        entryRegistrationExcelService.exportEntryRegistrationExcel(mission, params);
        source = Constants.PLATFORM_TYPE_AFTER;
      } else if (Constants_Batch.BATCH_PRINT_OUT_PURCHASE_APPLY_ORDER_ACCEPT_TEMP_V2.equals(type)) {
         excelService.batchPrintOutPurchaseApplyOrderAcceptTempV2(mission,params);
         source = Constants.PLATFORM_TYPE_AFTER;
       }
       ShardingContext.clear();
        //mq通知供应商端任务完成
        mission = missionRepository.findById(missionId).orElse(null);
        String state = Constants.MISSION_STATE_FAIL;
        String missionName = "";
        if (mission != null) {
            state = mission.getState();
            missionName = mission.getType();
        }
        if (Constants_Batch.SOURCE_TYPE.equals(source)) {
            batchSupplierMqSender.toDoneManageBatchTask(mission.getCreateManId(), missionId, missionName, state);
        } else {
            batchSupplierMqSender.toDoneBatchTask(mission.getCreateManId(), missionId, missionName, state);
        }
    }

}
