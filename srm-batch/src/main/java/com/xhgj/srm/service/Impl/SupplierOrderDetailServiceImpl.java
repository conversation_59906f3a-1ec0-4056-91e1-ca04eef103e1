package com.xhgj.srm.service.Impl;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.SupplierOrderDetailDao;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.service.SupplierOrderDetailService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/11/28 15:20
 */
@Service
public class SupplierOrderDetailServiceImpl implements SupplierOrderDetailService {

  @Autowired private SupplierOrderDetailRepository repository;
  @Autowired private SupplierOrderDetailDao dao;

  @Override
  public BootBaseRepository<SupplierOrderDetail, String> getRepository() {
    return repository;
  }

  @Override
  public List<SupplierOrderDetail> getByOrderToFormId(String orderToFormId) {
    return repository.getAllByOrderToFormIdAndStateOrderBySortNumAsc(
        orderToFormId, Constants.STATE_OK);
  }

  @Override
  public List<String> getAllIds() {
    return dao.findAllIds();
  }
}
