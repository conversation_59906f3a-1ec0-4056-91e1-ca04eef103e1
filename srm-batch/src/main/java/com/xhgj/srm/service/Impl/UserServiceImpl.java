package com.xhgj.srm.service.Impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhgj.srm.jpa.dao.UserCheckDao;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.dao.UserToGroupDao;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.UserCheck;
import com.xhgj.srm.jpa.entity.UserToGroup;
import com.xhgj.srm.jpa.repository.UserCheckRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.jpa.repository.UserToGroupRepository;
import com.xhgj.srm.service.UserService;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/7/7 16:15
 */
@Service
@Slf4j
public class UserServiceImpl implements UserService {

  @Autowired private UserDao dao;
  @Autowired private UserRepository repository;
  @Autowired private UserToGroupRepository userToGroupRepository;
  @Autowired private UserCheckRepository userCheckRepository;
  @Autowired private UserToGroupDao userToGroupDao;
  @Autowired private UserCheckDao userCheckDao;


  @Override
  public List<User> getUserByOrg(String erpCode) {
    Assert.notEmpty(erpCode);
    return dao.getUserByOrg(erpCode);
  }

  @Override
  public BootBaseRepository<User, String> getRepository() {
    return repository;
  }

  @Override
  public User getUserByCode(String code) {
    Assert.notEmpty(code);
    return dao.getUserByCode(code);
  }

  @Override
  public void addOrUpdateUserExcel(String name, String mobile, String erpCode, String erpId,
                                   String roleCode, String addZl, String addYb, String addXm, String addLx,
                                   String addDs, String updateZl, String updateYb, String updateXm, String updateLx, String updateDs,
                                   String blockZl, String blockYb, String blockXm, String blockLx, String blockDs,
                                   String groupId, String departId,String userId,
      String updateUserId) {
    UserToGroup userToGroup = null;
    if(!StringUtils.isNullOrEmpty(userId)){
      userToGroup = userToGroupDao.getUserToGroupByUserIdAndGroupId(userId,groupId);
    }
    User user = null;
    if(StringUtils.isNullOrEmpty(userId)){
      user = new User();
      user.setCode(erpCode);
      user.setRole(roleCode);
      user.setErpId(erpId);
      user.setRealName(name);
      user.setMobile(mobile);
      user.setName(mobile);
      user.setState(Constants.STATE_OK);
      user.setCreateTime(System.currentTimeMillis());
      repository.save(user);
      // 保存修改审核关系
      saveAddUserCheck(
              user, SupplierLevelEnum.STRATEGIC.getCode(), addZl);
      saveUpdateUserCheck(
              user, SupplierLevelEnum.STRATEGIC.getCode(), updateZl);
      saveBlockUserCheck(
              user, SupplierLevelEnum.STRATEGIC.getCode(), blockZl);
      saveAddUserCheck(
          user, SupplierLevelEnum.HIGH_QUALITY.getCode(), addZl);
      saveUpdateUserCheck(
          user, SupplierLevelEnum.HIGH_QUALITY.getCode(), updateZl);
      saveBlockUserCheck(
          user, SupplierLevelEnum.HIGH_QUALITY.getCode(), blockZl);
      saveAddUserCheck(
              user, SupplierLevelEnum.GENERAL.getCode(), addYb);
      saveUpdateUserCheck(
              user, SupplierLevelEnum.GENERAL.getCode(), updateYb);
      saveBlockUserCheck(
              user, SupplierLevelEnum.GENERAL.getCode(), blockYb);
      saveAddUserCheck(
              user, SupplierLevelEnum.SPORADIC.getCode(), addLx);
      saveUpdateUserCheck(
              user, SupplierLevelEnum.SPORADIC.getCode(), updateLx);
      saveBlockUserCheck(
              user, SupplierLevelEnum.SPORADIC.getCode(), blockLx);
      userId = user.getId();
    }
    if(userToGroup==null){
      userToGroup = new UserToGroup();
      userToGroup.setUserId(userId);
      userToGroup.setGroupId(groupId);
      userToGroup.setDeptId(departId);
      userToGroup.setState(Constants.STATE_OK);
      userToGroup.setCreateTime(System.currentTimeMillis());
      userToGroupRepository.save(userToGroup);
    }
  }

  /**
   * 保存新增供应商审核
   *
   * @param user 用户对象
   * @param level 供应商等级 {@link SupplierLevelEnum}
   * @param erpCode 审核erp编码
   */
  private void saveAddUserCheck(User user, String level, String erpCode) {
    saveUserCheck(user, Constants.SUPPLIERCHECKTYPE_MAP_ADD, level, erpCode);
  }

  /**
   * 保存修改供应商审核
   *
   * @param user 用户对象
   * @param level 供应商等级 {@link SupplierLevelEnum}
   * @param erpCode 审核erp编码
   */
  private void saveUpdateUserCheck(User user, String level, String erpCode) {
    saveUserCheck(user, Constants.SUPPLIERCHECKTYPE_MAP_UPDATE, level, erpCode);
  }

  /**
   * 保存拉黑供应商审核
   *
   * @param user 用户对象
   * @param level 供应商等级 {@link SupplierLevelEnum}
   * @param erpCode 审核erp编码
   */
  private void saveBlockUserCheck(User user, String level, String erpCode) {
    saveUserCheck(user, Constants.SUPPLIERCHECKTYPE_MAP_SHIELD, level, erpCode);
  }

  /**
   * @Author: liuyq @Date: 2022/7/14 14:30
   *
   * @param user 用户对象
   * @param type 审核类型(1-新增,2修改,3拉黑)
   * @param level 供应商等级 {@link SupplierLevelEnum}
   * @param erpCode 审核erp编码
   * @return void
   */
  private void saveUserCheck(User user, String type, String level, String erpCode) {
    if(!StringUtils.isNullOrEmpty(erpCode)){
      UserCheck userCheck = userCheckDao.getUserCheckByUser(user.getId(),type,level);
      if(userCheck==null){
        userCheck = new UserCheck();
        userCheck.setSupplierType(level);
        userCheck.setState(Constants.STATE_OK);
        userCheck.setType(type);
        userCheck.setCreateTime(System.currentTimeMillis());
        userCheck.setUser(user);
        userCheck.setUserErpCode(erpCode);
        userCheckRepository.save(userCheck);
      }
    }
  }

  @Override
  public List<String> getAllUserIdList(String state, String excludeId) {
    return CollUtil.emptyIfNull(repository.getAllByStateAndExcludeId(state, excludeId));
  }
}
