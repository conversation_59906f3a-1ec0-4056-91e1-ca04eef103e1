package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.ShortMessageEnum;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.dao.SupplierUserDao;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.request.service.third.xhgj.XhgjSMSRequest;
import com.xhiot.boot.core.config.BootConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
  *@ClassName AsyncTaskService
  *<AUTHOR>
  *@Date 2023/9/19 15:56
*/
@Component
@Slf4j
public class AsyncTaskService {
  @Resource
  private SupplierUserDao supplierUserDao;
  @Resource
  private XhgjSMSRequest xhgjSMSRequest;
  @Resource
  private BootConfig bootConfig;

  @Async
  public void noticeSendMsg(String title, List<String> supplierId){
    List<String> mobileList = new ArrayList<>();
    for (String s : supplierId) {
      List<SupplierUser> supplierUsers =
          supplierUserDao.getSupplierUserListBySidAsc(s);
      if(CollUtil.isNotEmpty(supplierUsers)) {
        mobileList.addAll(supplierUsers.stream().filter(u -> StrUtil.isNotEmpty(u.getMobile())).map(SupplierUser::getMobile).collect(Collectors.toList()));
      }
    }
    try {
      xhgjSMSRequest.sendSms(ShortMessageEnum.NOTICE_MSG,
          String.join(",", mobileList),
          buildParamsMap(title));
    } catch (Exception e) {
      String env = bootConfig.getEnv();
      DingUtils.sendMsgByWarningRobot(
          "【" + env + "环境 " + bootConfig.getAppName() + "】 公告：【" + title + "】发送短信失败，请及时处理！",
          env);
    }
  }

  private Map<String, String> buildParamsMap(String title) {
    return MapUtil.of("title", title);
  }
}
