package com.xhgj.srm.service.impl;

import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.BooleanEnum;
import com.xhgj.srm.dto.entryregistration.ApprovalResultCallbackParam;
import com.xhgj.srm.service.DingTalkApprovalCallback;
import com.xhgj.srm.service.EntryRegistrationOAService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * @Author: fanghuanxu
 * @Date: 2025/2/28 8:39
 * @Description: 入驻报备单审批结果事件处理
 */
@Service
public class EntryOrderCallBackImpl implements DingTalkApprovalCallback {
  @Resource
  private EntryRegistrationOAService entryRegistrationOAService;


  @Override
  public void doPassHandle(ApprovalResult approvalResult) {
    ApprovalResultCallbackParam param = new ApprovalResultCallbackParam();
    param.setAuditId(approvalResult.getProcessInstanceId());
    param.setApprovalResult(BooleanEnum.YES.getKey());
    param.setRejection(null);
    entryRegistrationOAService.approvalResultCallback(param);
  }

  @Override
  public void doRejectHandle(ApprovalResult approvalResult) {
    ApprovalResultCallbackParam param = new ApprovalResultCallbackParam();
    param.setAuditId(approvalResult.getProcessInstanceId());
    param.setApprovalResult(BooleanEnum.NO.getKey());
    param.setRejection(approvalResult.getRemark());
    entryRegistrationOAService.approvalResultCallback(param);
  }
}
