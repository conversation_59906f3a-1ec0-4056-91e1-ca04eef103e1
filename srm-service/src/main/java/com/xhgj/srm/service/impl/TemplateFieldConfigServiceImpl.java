package com.xhgj.srm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.constants.Constants_FieldConfig;
import com.xhgj.srm.common.enums.BootDictEnum;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.dto.filedConfig.ProcurementFieldConfigDTO;
import com.xhgj.srm.dto.filedConfig.ProcurementFieldDTO;
import com.xhgj.srm.dto.filedConfig.TemplateFieldDTO;
import com.xhgj.srm.dto.filedConfig.TitleFieldListDTO;
import com.xhgj.srm.dto.filedConfig.UpdateProcurementShipperDTO;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ButtonConfigParam;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ButtonFieldConverter;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ButtonFieldDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ButtonFieldVo;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ButtonTypeConfigDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ButtonTypeConfigItemDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.ConfigItemDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.FieldDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.FieldDto.DefaultValue;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.OrderTypeFieldDto;
import com.xhgj.srm.dto.filedConfig.purchaseOrderV2.PurchaseOrderConfigVo;
import com.xhgj.srm.jpa.dao.TemplateFieldConfigDao;
import com.xhgj.srm.jpa.entity.FieldConfig;
import com.xhgj.srm.jpa.entity.TemplateFieldConfig;
import com.xhgj.srm.jpa.repository.FieldConfigRepository;
import com.xhgj.srm.jpa.repository.TemplateFieldConfigRepository;
import com.xhgj.srm.service.FieldConfigService;
import com.xhgj.srm.service.TemplateFieldConfigService;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form;
import com.xhgj.srm.v2.form.PurchaseOrderAddV2Form.ProductDetailAdd;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.dict.core.entity.BootDictDetail;
import com.xhiot.boot.dict.core.service.BootDictDetailService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
public class TemplateFieldConfigServiceImpl implements TemplateFieldConfigService {

  @Autowired
  private TemplateFieldConfigRepository repository;
  @Autowired private TemplateFieldConfigDao dao;
  @Autowired private FieldConfigService fieldConfigService;
  @Autowired private BootDictDetailService bootDictDetailService;
  @Autowired private FieldConfigRepository fieldConfigRepository;
  @Autowired private ExportUtil ex;
  @Resource private PlatformTransactionManager transactionManager;

  @Override
  public BootBaseRepository<TemplateFieldConfig, String> getRepository() {
    return repository;
  }

  @Override
  public List<ProcurementFieldConfigDTO> findConfigListByBigType(String bigType) {
    List<TemplateFieldConfig> list = repository.findByStateAndBigType(Constants.STATE_OK,
        bigType);
    if (CollUtil.isNotEmpty(list)) {
      return ProcurementFieldConfigDTO.getInstance(list);
    }
    return Collections.emptyList();
  }

  @Override
  public ProcurementFieldDTO getTemplateFieldInfo(String key, String type) {
    ProcurementFieldDTO dto = new ProcurementFieldDTO();
    if (Objects.equals(Constants.STATE_OK, type)) {
      if (StrUtil.isNotBlank(key)) {
        // 获取当前已有详情
        TemplateFieldConfig config =
            repository.findById(key).orElseThrow(() -> new CheckException("无此记录"));
        dto = JSONObject.parseObject(config.getText(), ProcurementFieldDTO.class);
      } else {
        // 获取默认详情
        dto = fieldConfigService.buildProcurementField();
      }
    } else if (Objects.equals(Constants.TWO, type)) {
      // 货主编码
      TemplateFieldConfig config =
          repository.findFirstByOrganizationRoleLikeAndState(key, Constants.STATE_OK,
              Constants_FieldConfig.BIG_TYPE_PURCHASE_APPLY);
      if (Objects.isNull(config)) {
        throw new CheckException("请联系管理员配置采购申请模版");
      }
      ProcurementFieldDTO oldDto = JSONObject.parseObject(config.getText(), ProcurementFieldDTO.class);
      // 主逻辑调用通用方法
      List<TitleFieldListDTO> componentDetail = oldDto.getComponentDetail();
      dto.setComponentDetail(processDetail(componentDetail));

      List<TitleFieldListDTO> productDetail = oldDto.getProductDetail();
      dto.setProductDetail(processDetail(productDetail));

      List<TitleFieldListDTO> procurementApplicationList = oldDto.getProcurementApplicationList();
      dto.setProcurementApplicationList(processDetail(procurementApplicationList));

      List<TitleFieldListDTO> procurementApplicationInfo = oldDto.getProcurementApplicationInfo();
      dto.setProcurementApplicationInfo(processDetail(procurementApplicationInfo));
    }
    return dto;
  }
  private List<TitleFieldListDTO> processDetail(List<TitleFieldListDTO> detailList) {
    if (detailList == null) {
      return new ArrayList<>(); // 如果输入为 null，返回空列表
    }

    List<TitleFieldListDTO> newList = new ArrayList<>();
    for (TitleFieldListDTO titleFieldListDTO : detailList) {
      TitleFieldListDTO newTitleFieldListDTO = new TitleFieldListDTO();
      newTitleFieldListDTO.setTitle(titleFieldListDTO.getTitle());

      // 使用 stream 过滤字段
      List<TemplateFieldDTO> filteredFields = titleFieldListDTO.getFields() != null
          ? titleFieldListDTO.getFields().stream()
          .filter(templateFieldDTO -> Constants.STATE_OK.equals(templateFieldDTO.getIsDefault()))
          .collect(Collectors.toList())
          : new ArrayList<>();

      newTitleFieldListDTO.setFields(filteredFields);
      newList.add(newTitleFieldListDTO);
    }
    return newList;
  }

  @Override
  public Map<String, String> buildFieldMap(String code) {

    TemplateFieldConfig config =
        repository.findFirstByOrganizationRoleLikeAndState(code, Constants.STATE_OK,
            Constants_FieldConfig.BIG_TYPE_PURCHASE_APPLY);
    if (config == null) {
      throw new CheckException("请先配置组织模版");
    }
    if (StrUtil.isBlank(config.getText())) {
      throw new CheckException("请先配置组织模版");
    }
    ProcurementFieldDTO configField =  JSONObject.parseObject(config.getText(),
        ProcurementFieldDTO.class);
    Map<String, String> fieldNameMap = new HashMap<>();
    // 过滤出fields中TemplateFieldDTO的isDefault是true的
    configField.getProcurementApplicationList().stream().map(TitleFieldListDTO::getFields).
            flatMap(List::stream).filter(templateFieldDTO -> Constants.STATE_OK.equals(templateFieldDTO.getIsDefault()))
        .forEach(templateFieldDTO -> {
          // 将字段名称和key放入map中
          fieldNameMap.put(templateFieldDTO.getName(), templateFieldDTO.getKey());
        });
    configField.getProcurementApplicationInfo().stream().map(TitleFieldListDTO::getFields).
            flatMap(List::stream).filter(templateFieldDTO -> Constants.STATE_OK.equals(templateFieldDTO.getIsDefault()))
        .forEach(templateFieldDTO -> {
          // 将字段名称和key放入map中
          fieldNameMap.put(templateFieldDTO.getName(), templateFieldDTO.getKey());
        });
    configField.getProductDetail().stream().map(TitleFieldListDTO::getFields).
            flatMap(List::stream).filter(templateFieldDTO -> Constants.STATE_OK.equals(templateFieldDTO.getIsDefault()))
        .forEach(templateFieldDTO -> {
          // 将字段名称和key放入map中
          fieldNameMap.put(templateFieldDTO.getName(), templateFieldDTO.getKey());
        });
    configField.getComponentDetail().stream().map(TitleFieldListDTO::getFields).
            flatMap(List::stream).filter(templateFieldDTO -> Constants.STATE_OK.equals(templateFieldDTO.getIsDefault()))
        .forEach(templateFieldDTO -> {
          // 将字段名称和key放入map中
          fieldNameMap.put(templateFieldDTO.getName(), templateFieldDTO.getKey());
        });
    return fieldNameMap;
  }

  @Override
  public void updateProcurementShipper(UpdateProcurementShipperDTO dto) {
    List<String> newOrganizationRole =
        StrUtil.split(dto.getOrganizationRole(), ',', true, true);
    List<TemplateFieldConfig> list = dao.findByStateAndBigTypeAndExcludeId(Constants.STATE_OK,
        dto.getBigType(),
        StrUtil.emptyIfNull(dto.getId()));
    List<String> oldOrg = new ArrayList<>();
    for (TemplateFieldConfig config : list) {
      oldOrg.addAll(StrUtil.split(config.getOrganizationRole(), ',', true, true));
    }
    // 判断 newOrganizationRole 和 oldOrg 是否有重复的
    if (CollUtil.containsAny(newOrganizationRole, oldOrg)) {
      throw new CheckException("已存在模版的使用组织不能在应用与另外一个模版");
    }
    TemplateFieldConfig config =
        repository.findById(dto.getId()).orElseThrow(() -> new CheckException("无此记录"));
    config.setOrganizationRole(CollUtil.join(newOrganizationRole, ","));
    repository.save(config);
  }



  @Override
  public TemplateFieldConfig saveOrUpdate(String id,String name,String remark,String textJson,String bigType) {
    TemplateFieldConfig config;
    if (StrUtil.isNotBlank(id)) {
      // 修改
      config =
          repository.findById(id).orElseThrow(() -> new CheckException("无此记录"));
    } else {
      // 新增
      config = new TemplateFieldConfig();
      config.setState(Constants.STATE_OK);
      config.setCreateTime(System.currentTimeMillis());
      config.setBigType(bigType);
    }
    config.setName(name);
    config.setRemark(remark);
    config.setText(textJson);
    return repository.saveAndFlush(config);
  }

  @Override
  public void deleteTemplate(List<String> ids) {
    if (CollUtil.isNotEmpty(ids)) {
      List<TemplateFieldConfig> list =
          dao.findByInId(ids);
      for (TemplateFieldConfig config : list) {
        config.setState(Constants.STATE_DELETE);
        repository.save(config);
      }
    }
  }

  @Override
  public PurchaseOrderConfigVo purchaseOrderV2Detail(String id) {
    PurchaseOrderConfigVo res = new PurchaseOrderConfigVo();
    if (StrUtil.isBlank(id)) {
      ConfigItemDto configItem = new ConfigItemDto();
      // 1. 订单列表视图
      List<TitleFieldListDTO> defaultView =
          fieldConfigService.getPurchaseOrderV2DefaultView();
      // 2. 订单字段详情
      List<OrderTypeFieldDto> fieldList = buildOrderTypeField();
      configItem.setOrderList(defaultView);
      configItem.setOrderTypeList(fieldList);
      res.setConfigItemDto(configItem);
      return res;
    }
    TemplateFieldConfig config = repository.findById(id)
        .orElseThrow(() -> CheckException.noFindException(TemplateFieldConfig.class, id));
    ConfigItemDto configItem = JSONObject.parseObject(config.getText(), ConfigItemDto.class);
    res.setId(id);
    res.setName(config.getName());
    res.setRemark(config.getRemark());
    res.setConfigItemDto(configItem);
    return res;
  }

  /**
   * 基于字典信息构建采购订单配置详情
   * @return
   */
  private List<OrderTypeFieldDto> buildOrderTypeField() {
    List<OrderTypeFieldDto> res = new ArrayList<>();
    List<BootDictDetail> orderTypeDict =
        bootDictDetailService.listByDictCode(BootDictEnum.PROCUREMENT_ORDER.getKey());
    Map<String, List<FieldConfig>> type2Config =
        fieldConfigRepository.findAllByBigType(Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER)
            .stream().collect(
                Collectors.groupingBy(FieldConfig::getGroupType, LinkedHashMap::new, Collectors.toList()));
    for (BootDictDetail dict : orderTypeDict) {
      OrderTypeFieldDto typeField = new OrderTypeFieldDto();
      // 1. 基本信息
      typeField.setTitle(dict.getValue());
      typeField.setAllowAdd(Constants.STATE_NO);
      typeField.setOrderTypeEnum(dict.getKey());
      typeField.setOrderTypeDesc(null);
      // 2. 字段信息
      List<FieldDto> fields =
          type2Config.getOrDefault(dict.getKey(), Collections.emptyList())
              .stream().map(FieldConfig::getExtJson)
              .map(json->JSONObject.parseObject(json,FieldDto.class))
              .collect(Collectors.toList());
      typeField.setFields(fields);
      res.add(typeField);
    }
    return res;
  }

  /**
   * 枚举key取值
   * @see BootDictEnum
   */
  @Override
  public void purchaseOrderV2FieldInit(MultipartFile file) {
    List<BootDictDetail> orderTypeDict =
        bootDictDetailService.listByDictCode(BootDictEnum.PROCUREMENT_ORDER.getKey());
    try (Workbook book = ex.buildByFile(file.getOriginalFilename(), file.getInputStream())) {
      if (book == null) {
        throw new CheckException("文件异常");
      }
      for (BootDictDetail dict : orderTypeDict) {
        Sheet sheet = book.getSheet(dict.getValue());
        if (ObjectUtil.isNull(sheet)) {
          continue;
        }
        log.info("采购订单模版解析,当前sheet:{}", dict.getValue());
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        try {
          transactionTemplate.execute(status -> {
            // 先删后增
            fieldConfigRepository.deleteAllByBigTypeAndGroupTypeIn(
                Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER,
                Collections.singletonList(dict.getKey()));
            int startRow = 2;
            int totalRow = sheet.getPhysicalNumberOfRows();
            List<FieldConfig> fieldConfigList = new ArrayList<>();
            int sort = 0;
            for (int i = startRow; i < totalRow; i++) {
              Row row = sheet.getRow(i);
              int col = 0;
              if (row == null || ex.isEmptyRow(row)) {
                continue;
              }
              // 模块
              String module = ex.getCellStringValue(row.getCell(col++));
              // 字段
              String name = ex.getCellStringValue(row.getCell(col++));
              if (StrUtil.isBlank(name)) {
                log.warn("防止空行-字段名称为空,跳过当前行");
                continue;
              }
              // 备注
              String remark = ex.getCellStringValue(row.getCell(col++));
              // 是否展示
              String show = ex.getCellStringValue(row.getCell(col++));
              // 是否可修改
              String showEditable = ex.getCellStringValue(row.getCell(col++));
              // 暂存时是否必填
              String requiredStage = ex.getCellStringValue(row.getCell(col++));
              // 是否可修改
              String requiredStageEditable = ex.getCellStringValue(row.getCell(col++));
              // 新增时是否必填
              String requiredAdd = ex.getCellStringValue(row.getCell(col++));
              // 是否可修改
              String requiredAddEditable = ex.getCellStringValue(row.getCell(col++));
              // 默认值
              String defaultValue = ex.getCellStringValue(row.getCell(col++));
              // 是否只读
              String readonly = ex.getCellStringValue(row.getCell(col++));
              // 是否可修改
              String readonlyEditable = ex.getCellStringValue(row.getCell(col++));
              // 对应后端实体名称
              String entityFieldName = ex.getCellStringValue(row.getCell(col++));
              // 2. 存值
              FieldConfig config = new FieldConfig();
              config.setName(name);
              config.setGroupType(dict.getKey());
              config.setTitle(dict.getValue());
              config.setBigType(Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER);
              config.setSort(sort += 10);
              FieldDto fieldDto = new FieldDto();
              fieldDto.setModule(module);
              fieldDto.setName(name);
              fieldDto.setShow(getByBoolStr(show));
              fieldDto.setShowEditable(getByBoolStr(showEditable));
              fieldDto.setRequiredStage(getByBoolStr(requiredStage));
              fieldDto.setRequiredStageEditable(getByBoolStr(requiredStageEditable));
              fieldDto.setRequiredAdd(getByBoolStr(requiredAdd));
              fieldDto.setRequiredAddEditable(getByBoolStr(requiredAddEditable));
              // 默认值
              DefaultValue defaultValueObj = new DefaultValue();
              // 含 # 认为是枚举值
              if (StrUtil.contains(defaultValue, "#")) {
                String[] split = defaultValue.split("#");
                if (split.length == 2) {
                  defaultValueObj.setIsEnum(Constants.STATE_OK);
                  defaultValueObj.setEnumKey(split[0]);
                  defaultValueObj.setDefaultValue(convertNullString(split[1]));
                } else {
                  // 兼容空值作为枚举值的情况
                  defaultValueObj.setIsEnum(Constants.STATE_OK);
                  defaultValueObj.setEnumKey(split[0]);
                  defaultValueObj.setDefaultValue("");
                }
              } else {
                defaultValueObj.setDefaultValue(convertNullString(defaultValue));
              }
              fieldDto.setDefaultValue(defaultValueObj);
              fieldDto.setReadonly(getByBoolStr(readonly));
              fieldDto.setReadonlyEditable(getByBoolStr(readonlyEditable));
              fieldDto.setRemark(remark);
              fieldDto.setEntityFieldName(StrUtil.split(entityFieldName,','));
              config.setExtJson(JSONObject.toJSONString(fieldDto));
              fieldConfigList.add(config);
            }
            log.info("采购订单模版解析,当前sheet:{} 解析完成,共{}行", dict.getValue(), fieldConfigList.size());
            fieldConfigRepository.saveAll(fieldConfigList);
            fieldConfigRepository.flush();
            return null;
          });
        } catch (Exception e) {
          log.error("sheet:" + dict.getValue() + "处理失败", e);
        }
      }
    } catch (Exception e) {
      log.error("采购订单模版解析失败", e);
      throw new CheckException("采购订单模版解析失败");
    }
  }

  @Override
  public List<FieldDto> getPurchaseOrderV2FieldConfig(String userCode, String orderType) {
    TemplateFieldConfig config =
        repository.findFirstByOrganizationRoleLikeAndState(userCode, Constants.STATE_OK,
            Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER);
    if (config == null || StrUtil.isBlank(config.getText())) {
      return null;
    }
    return Optional.ofNullable(JSONObject.parseObject(config.getText(), ConfigItemDto.class))
            .map(ConfigItemDto::getOrderTypeList)
            .orElse(Collections.emptyList())
            .stream()
            // 对应的订单类型
            .filter(orderTypeFieldDto -> StrUtil.equals(orderTypeFieldDto.getOrderTypeEnum(), orderType))
            .findFirst()
            // 配置的所有字段
            .map(OrderTypeFieldDto::getFields)
            .orElse(Collections.emptyList());
  }

  @Override
  public FieldDto getPurchaseOrderV2FieldConfig(String userGroup, String orderType,
      String entityFieldName) {
    List<FieldDto> fieldList = getPurchaseOrderV2FieldConfig(userGroup, orderType);
    return fieldList.stream()
        .filter(dto->CollUtil.contains(dto.getEntityFieldName(),entityFieldName))
        .findFirst()
        .orElse(null);
  }

  @Override
  public boolean isFieldShow(String userGroup, String orderType, String entityFieldName) {
    String state =
        Optional.ofNullable(getPurchaseOrderV2FieldConfig(userGroup, orderType, entityFieldName))
            .map(FieldDto::getShow)
            .orElse(Constants.STATE_NO);
    return Constants.STATE_OK.equals(state);
  }

  @Override
  public void checkFieldRequired(String userGroup, String orderType, PurchaseOrderAddV2Form form) {
    List<FieldDto> fieldConfig = getPurchaseOrderV2FieldConfig(userGroup, orderType);
    if (CollUtil.isEmpty(fieldConfig)) {
      throw new CheckException("采购订单新增需要提前配置订单模版，如有需要请系统管理员");
    }

    boolean saveTypeStaging = Constants.SAP_INVOICE_STAGING.equals(form.getSaveType());
    List<FieldDto> needValidFields = fieldConfig.stream()
        .filter(field -> Constants.STATE_OK.equals(field.getShow()))
        .filter(field -> Constants.STATE_OK.equals(saveTypeStaging ? field.getRequiredStage() : field.getRequiredAdd()))
        .collect(Collectors.toList());

    // 构建普通字段校验映射
    Map<String, FieldDto> commonFieldMap = createFieldMap(needValidFields);

    // 校验普通字段
    validateCommonFields(form, commonFieldMap);

    // 构建product字段校验映射
    Map<String, FieldDto> productFieldMap = createProductFieldMap(needValidFields);

    // 校验productList中的字段
    validateProductListFields(form, productFieldMap);
  }

  // 创建普通字段映射（字段名 -> FieldDto）
  private Map<String, FieldDto> createFieldMap(List<FieldDto> needValidFields) {
    return needValidFields.stream()
        .flatMap(dto ->
            dto.getEntityFieldName().stream().map(name -> new AbstractMap.SimpleEntry<>(name, dto))
        )
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1));
  }

  // 创建product字段映射（product字段名 -> FieldDto）
  private Map<String, FieldDto> createProductFieldMap(List<FieldDto> needValidFields) {
    return needValidFields.stream()
        .flatMap(dto -> dto.getEntityFieldName().stream()
            .filter(name -> name.startsWith(Constants_FieldConfig.PURCHASE_ORDER_ITEM))
            .map(name -> {
              // productList + '.' + 字段名
              String productFieldName = name.replace(Constants_FieldConfig.PURCHASE_ORDER_ITEM + ".", "");
              return new AbstractMap.SimpleEntry<>(productFieldName, dto);
            }))
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1));
  }

  // 校验普通字段
  private void validateCommonFields(PurchaseOrderAddV2Form form, Map<String, FieldDto> fieldMap) {
    for (Field field : ReflectUtil.getFields(form.getClass())) {
      if (Constants_FieldConfig.PURCHASE_ORDER_ITEM.equals(field.getName())) {
        continue;
      }
      FieldDto fieldDto = fieldMap.get(field.getName());
      if (fieldDto != null) {
        Object value = ReflectUtil.getFieldValue(form, field);
        validateFieldValue(value, fieldDto);
      }
    }
  }

  // 校验productList中的字段
  private void validateProductListFields(PurchaseOrderAddV2Form form, Map<String, FieldDto> productFieldMap) {
    if (productFieldMap.isEmpty()) {
      return;
    }
    List<ProductDetailAdd> productList = (List<ProductDetailAdd>) ReflectUtil.getFieldValue(form,
        Constants_FieldConfig.PURCHASE_ORDER_ITEM);
    if (CollUtil.isEmpty(productList)) {
      return;
    }
    for (ProductDetailAdd product : productList) {
      Stream.of(ReflectUtil.getFields(product.getClass())).forEach(pField -> {
        FieldDto fieldDto = productFieldMap.get(pField.getName());
        if (fieldDto != null) {
          Object value = ReflectUtil.getFieldValue(product, pField);
          validateFieldValue(value, fieldDto);
        }
      });
    }
  }

  // 通用字段值校验
  private void validateFieldValue(Object value, FieldDto fieldDto) {
    if (Objects.isNull(value) || StrUtil.isBlankIfStr(value)) {
      throw new CheckException("【" + fieldDto.getName() + "】为必填项");
    }
  }

  @Override
  public OrderTypeFieldDto orderFieldByOrg(String orgCode, String orderType) {
    // 货主编码
    TemplateFieldConfig config =
        repository.findFirstByOrganizationRoleLikeAndState(orgCode, Constants.STATE_OK,
            Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER);
    if (Objects.isNull(config)) {
      throw new CheckException("请联系管理员配置采购订单模版");
    }
    ConfigItemDto configItem = JSONObject.parseObject(config.getText(), ConfigItemDto.class);
    return CollUtil.emptyIfNull(configItem.getOrderTypeList()).stream()
        .filter(item->StrUtil.equals(item.getOrderTypeEnum(),orderType))
        .findFirst().orElseThrow(()-> new CheckException("当前采购订单类型未配置模版,请联系管理员"));
  }

  @Override
  public List<TitleFieldListDTO> listFieldByOrg(String orgCode) {
    TemplateFieldConfig config =
        repository.findFirstByOrganizationRoleLikeAndState(orgCode, Constants.STATE_OK,
            Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER);
    if (Objects.isNull(config)) {
      throw new CheckException("请联系管理员配置采购订单模版");
    }
    ConfigItemDto configItem = JSONObject.parseObject(config.getText(), ConfigItemDto.class);

    return configItem.getOrderList();
  }

  @Override
  public List<OrderTypeFieldDto> orderPopByOrg(String orgCode) {
    TemplateFieldConfig config =
        repository.findFirstByOrganizationRoleLikeAndState(orgCode, Constants.STATE_OK,
            Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER);
    if (Objects.isNull(config)) {
      throw new CheckException("请联系管理员配置采购订单模版");
    }
    ConfigItemDto configItem = JSONObject.parseObject(config.getText(), ConfigItemDto.class);
    return CollUtil.emptyIfNull(configItem.getOrderTypeList()).stream()
        .map(item -> BeanUtil.copyProperties(item,OrderTypeFieldDto.class,"fields"))
        // 先启用
        .sorted(Comparator.comparing(OrderTypeFieldDto::getAllowAdd).reversed())
        .collect(Collectors.toList());
  }

  @Override
  @Transactional
  public TemplateFieldConfig saveOrUpdate(String id, String name, String remark, ConfigItemDto configItemDto) {
    // 重新构建configItemDto
    // dbVo or defaultVo
    PurchaseOrderConfigVo vo = purchaseOrderV2Detail(id);
    ConfigItemDto dbConfig = vo.getConfigItemDto();
    dbConfig.setOrderList(configItemDto.getOrderList());
    List<OrderTypeFieldDto> dbFieldConfig = dbConfig.getOrderTypeList();
    List<OrderTypeFieldDto> paramTypeConfig = configItemDto.getOrderTypeList();
    // 根据枚举 orderTypeEnum 替换dbFieldConfig
    Map<String, OrderTypeFieldDto> dbConfigMap = dbFieldConfig.stream()
        .collect(Collectors.toMap(OrderTypeFieldDto::getOrderTypeEnum, dto -> dto,(k1,k2)->k1));
    for (OrderTypeFieldDto paramConfig : paramTypeConfig) {
      String orderTypeEnum = paramConfig.getOrderTypeEnum();
      OrderTypeFieldDto dbConfigItem = dbConfigMap.get(orderTypeEnum);
      if (dbConfigItem != null) {
        // 替换
        dbConfigItem.setFields(paramConfig.getFields());
        dbConfigItem.setAllowAdd(paramConfig.getAllowAdd());
        dbConfigItem.setOrderTypeDesc(paramConfig.getOrderTypeDesc());
      }
    }

    return saveOrUpdate(id,name, remark,
        JSONObject.toJSONString(dbConfig), Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER);
  }

  @Override
  public void checkPush(String orgCode, String orderType) {
    TemplateFieldConfig config =
        repository.findFirstByOrganizationRoleLikeAndState(orgCode, Constants.STATE_OK,
            Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER);
    if (Objects.isNull(config)) {
      throw new CheckException("未配置订单模版请联系管理员");
    }
    ConfigItemDto configItem = JSONObject.parseObject(config.getText(), ConfigItemDto.class);
    OrderTypeFieldDto orderTypeFieldDto =
        CollUtil.emptyIfNull(configItem.getOrderTypeList()).stream()
            .filter(item -> StrUtil.equals(item.getOrderTypeEnum(), orderType)).findFirst()
            .orElseThrow(() -> new CheckException("当前采购订单类型未配置模版,请联系管理员"));
    if (!StrUtil.equals(orderTypeFieldDto.getAllowAdd(),Constants.STATE_OK)) {
      throw new CheckException("不允许新增当前类型的采购订单,请联系管理员");
    }
  }

  @Override
  @Transactional
  public TemplateFieldConfig saveOrUpdate(String id, String name, String remark,
      ButtonTypeConfigDto param) {
    ButtonConfigParam vo = purchaseOrderV2ButtonDetail(id);
    ButtonTypeConfigDto dbConfig = vo.getButtonTypeConfigDto();
    for (ButtonTypeConfigItemDto itemParam : param.getTypeConfig()) {
      String orderTypeEnum = itemParam.getOrderTypeEnum();
      ButtonTypeConfigItemDto dbConfigItem = dbConfig.getTypeConfig().stream()
          .filter(item -> StrUtil.equals(item.getOrderTypeEnum(), orderTypeEnum))
          .findFirst().orElse(null);
      if (dbConfigItem != null) {
        // 替换
        dbConfigItem.setFieldVo(itemParam.getFieldVo());
      }
    }

    return saveOrUpdate(id,name, remark,
        JSONObject.toJSONString(dbConfig), Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER_BUTTON);
  }

  @Override
  public ButtonConfigParam purchaseOrderV2ButtonDetail(String id) {
    ButtonConfigParam res = new ButtonConfigParam();
    if (StrUtil.isBlank(id)) {
      ButtonTypeConfigDto configDto = new ButtonTypeConfigDto();
      // 1. 默认视图
      List<ButtonTypeConfigItemDto> defaultView = buildOrderTypeButtonConfig();
      configDto.setTypeConfig(defaultView);
      res.setButtonTypeConfigDto(configDto);
      return res;
    }
    TemplateFieldConfig config = repository.findById(id)
        .orElseThrow(() -> CheckException.noFindException(TemplateFieldConfig.class, id));
    ButtonTypeConfigDto configDto = JSONObject.parseObject(config.getText(), ButtonTypeConfigDto.class);
    res.setId(id);
    res.setName(config.getName());
    res.setRemark(config.getRemark());
    res.setButtonTypeConfigDto(configDto);
    return res;
  }

  /**
   * 基于字典信息构建采购订单按钮配置
   * @return
   */
  private List<ButtonTypeConfigItemDto> buildOrderTypeButtonConfig() {
    List<ButtonTypeConfigItemDto> res = new ArrayList<>();
    List<BootDictDetail> orderTypeDict =
        bootDictDetailService.listByDictCode(BootDictEnum.PROCUREMENT_ORDER.getKey());
    Map<String, List<FieldConfig>> type2Config =
        fieldConfigRepository.findAllByBigType(Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER_BUTTON)
            .stream().collect(
                Collectors.groupingBy(FieldConfig::getGroupType, LinkedHashMap::new, Collectors.toList()));
    for (BootDictDetail dict : orderTypeDict) {
      ButtonTypeConfigItemDto item = new ButtonTypeConfigItemDto();
      // 1. 基本信息
      item.setTitle(dict.getValue());
      item.setOrderTypeEnum(dict.getKey());
      // 2. 字段信息
      List<ButtonFieldDto> fields =
          type2Config.getOrDefault(dict.getKey(), Collections.emptyList())
              .stream().map(FieldConfig::getExtJson)
              .map(json->JSONObject.parseObject(json,ButtonFieldDto.class))
              .collect(Collectors.toList());
      // dto -> vo
      List<ButtonFieldVo> buttonFieldVos = ButtonFieldConverter.convert(fields);
      item.setFieldVo(buttonFieldVos);
      res.add(item);
    }
    return res;
  }

  @Override
  public void purchaseOrderV2ButtonFieldInit(MultipartFile file) throws IOException {
    List<BootDictDetail> orderTypeDict =
        bootDictDetailService.listByDictCode(BootDictEnum.PROCUREMENT_ORDER.getKey());
    try (Workbook book = ex.buildByFile(file.getOriginalFilename(), file.getInputStream())) {
      if (book == null) {
        throw new CheckException("文件异常");
      }
      for (BootDictDetail dict : orderTypeDict) {
        Sheet sheet = book.getSheet(dict.getValue());
        if (Objects.isNull(sheet)) {
          continue;
        }
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        int startRow = 1;
        int totalRow = sheet.getPhysicalNumberOfRows();
        List<FieldConfig> fieldConfigList = new ArrayList<>();
        int sort = 0;
        for (int i = startRow; i < totalRow; i++) {
          Row row = sheet.getRow(i);
          int col = 0;
          if (row == null || ex.isEmptyRow(row)) {
            continue;
          }
          // 所属页
          String page = ex.getCellStringValue(row.getCell(col++));
          // 条件
          String condition = ex.getCellStringValue(row.getCell(col++));
          // 按钮
          String buttonName = ex.getCellStringValue(row.getCell(col++));
          if (StrUtil.isBlank(buttonName)) {
            log.warn("防止空行-字段名称为空,跳过当前行");
            continue;
          }
          // 默认勾选
          String isChecked = ex.getCellStringValue(row.getCell(col++));
          // 是否可修改
          String isUpdate = ex.getCellStringValue(row.getCell(col++));
          FieldConfig config = new FieldConfig();
          config.setName(buttonName);
          config.setBigType(Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER_BUTTON);
          config.setSort(sort += 10);
          ButtonFieldDto fieldDto = new ButtonFieldDto();
          fieldDto.setPage(page);
          fieldDto.setCondition(condition);
          fieldDto.setButtonName(buttonName);
          fieldDto.setIsChecked(StrUtil.blankToDefault(isChecked, Constants.STATE_NO));
          fieldDto.setIsUpdate(StrUtil.blankToDefault(isUpdate, Constants.STATE_NO));
          config.setExtJson(JSONObject.toJSONString(fieldDto));
          fieldConfigList.add(config);
          try {
            transactionTemplate.execute(status -> {
              // 先删后增
              fieldConfigRepository.deleteAllByBigTypeAndGroupTypeIn(
                  Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER_BUTTON,
                  Collections.singletonList(dict.getKey()));
              List<FieldConfig> collect = fieldConfigList.stream().map(con -> {
                FieldConfig curConfig = BeanUtil.copyProperties(con, FieldConfig.class);
                curConfig.setGroupType(dict.getKey());
                curConfig.setTitle(dict.getValue());
                return curConfig;
              }).collect(Collectors.toList());
              fieldConfigRepository.saveAll(collect);
              fieldConfigRepository.flush();
              return null;
            });
          } catch (Exception e) {
            log.error(dict.getValue() + "处理失败", e);
          }
        }
        log.info("采购订单按钮模版解析,解析完成,共{}行", fieldConfigList.size());
      }
      }
  }

  @Override
  public ButtonTypeConfigItemDto buttonFieldByOrg(String orgCode, String orderType) {
    // 货主编码
    TemplateFieldConfig config =
        repository.findFirstByOrganizationRoleLikeAndState(orgCode, Constants.STATE_OK,
            Constants_FieldConfig.BIG_TYPE_PURCHASE_ORDER_BUTTON);
    if (Objects.isNull(config)) {
      throw new CheckException("请联系管理员配置采购订单按钮模版");
    }
    ButtonTypeConfigDto typeConfigDto = JSONObject.parseObject(config.getText(),
        ButtonTypeConfigDto.class);

    return CollUtil.emptyIfNull(typeConfigDto.getTypeConfig()).stream()
        .filter(item->StrUtil.equals(item.getOrderTypeEnum(),orderType))
        .findFirst().orElseThrow(()-> new CheckException("当前采购订单类型未配置按钮模版,请联系管理员"));
  }

  private String convertNullString(String s) {
    if (StrUtil.isBlank(s)) {
      return null;
    }
    if (StrUtil.equals("null", s.toLowerCase())) {
      return null;
    }
    return s;
  }

  /**
   * @param boolStr TRUE\FALSE
   * @return 0-否 1-是 2-根据场景
   */
  private String getByBoolStr(String boolStr) {
    if (StrUtil.equals("TRUE", boolStr)) {
      return Constants.STATE_OK;
    } else if (StrUtil.equals("FALSE", boolStr)) {
      return Constants.STATE_NO;
    }
    return Constants_FieldConfig.CONDITION_SCENE;
  }
}
