package com.xhgj.srm.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.constants.ConstantsFinance;
import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.PaymentApplyTypeEnums;
import com.xhgj.srm.common.enums.PaymentAuditStateEnum;
import com.xhgj.srm.common.enums.PaymentFreezeStatusEnum;
import com.xhgj.srm.common.enums.VoucherPaymentStateEnum;
import com.xhgj.srm.common.enums.VoucherTypeEnum;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.dto.ApprovalResultsParam;
import com.xhgj.srm.factory.SapFactory;
import com.xhgj.srm.jpa.dao.GroupDao;
import com.xhgj.srm.jpa.dto.ThisAmountDTO;
import com.xhgj.srm.jpa.entity.FinancialVoucher;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.PaymentApplyDetail;
import com.xhgj.srm.jpa.entity.PaymentApplyRecord;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.FinancialVoucherRepository;
import com.xhgj.srm.jpa.repository.PaymentApplyDetailRepository;
import com.xhgj.srm.jpa.repository.PaymentApplyRecordRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.request.dto.hZero.process.PaymentApplyProcessParam;
import com.xhgj.srm.request.dto.hZero.process.PaymentApplyProcessParam.Detail;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyResult.Item;
import com.xhgj.srm.request.service.third.erp.sap.dto.AdvanceApplyResult.RETURNDTO;
import com.xhgj.srm.request.service.third.erp.sap.dto.SapDrawApplyParam;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.service.SharePaymentApplyDetailService;
import com.xhgj.srm.service.SharePaymentApplyService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.config.BootConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: fanghuanxu
 * @Date: 2025/5/22 14:49
 */
@Service
@Slf4j
public class SharePaymentApplyServiceImpl implements SharePaymentApplyService  {
  @Resource
  private PaymentApplyRecordRepository paymentApplyRecordRepository;
  @Autowired
  private PaymentApplyDetailRepository paymentApplyDetailRepository;
  @Autowired
  private FinancialVoucherRepository financialVoucherRepository;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private SAPService sapService;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;
  @Autowired
  private UserRepository userRepository;
  @Autowired
  private BootConfig bootConfig;
  @Resource
  private SapFactory sapFactory;
  @Autowired
  private GroupDao groupDao;
  @Resource
  private SharePaymentApplyDetailService sharePaymentApplyDetailService;

  @Override
  @Transactional
  public void syncSAP(String id) {
    PaymentApplyRecord paymentApplyRecord = paymentApplyRecordRepository.findById(id)
        .orElseThrow(() -> CheckException.noFindException(PaymentApplyRecord.class, id));
    if (!(StrUtil.equals(paymentApplyRecord.getApplyState(), Constants.PAYMENT_APPLY_PASS)
        && ObjectUtil.isNull(paymentApplyRecord.getSyncSapTime()))
        || StrUtil.isBlank(paymentApplyRecord.getReviewId())) {
      throw new CheckException("该付款申请单不支持重推SAP");
    }
    User user = Optional.ofNullable(
        userRepository.findFirstByRealNameAndState(paymentApplyRecord.getApplyMan(),
            Constants.STATE_OK)).orElseThrow(
        () -> CheckException.noFindException(User.class, paymentApplyRecord.getApplyMan()));
    List<PaymentApplyDetail> paymentApplyDetails =
        paymentApplyDetailRepository.findByPaymentApplyRecordId(paymentApplyRecord.getId());
    List<String> financialVoucherIds =
        paymentApplyDetails.stream().map(PaymentApplyDetail::getFinancialVouchersId)
            .map(ids-> StrUtil.split(ids, ','))
            .flatMap(List::stream)
            .collect(Collectors.toList());
    List<FinancialVoucher> financialVouchers =
        financialVoucherRepository.findAllById(financialVoucherIds);
    Set<String> supplierIds = financialVouchers.stream().map(FinancialVoucher::getSupplierId).collect(Collectors.toSet());
    Supplier supplier =
        supplierRepository.findAllById(supplierIds).stream().findFirst().orElse(null);
    if (supplier == null) {
      throw new CheckException("没有找到供应商信息");
    }
    String applyType = paymentApplyRecord.getApplyType();
    // MM066
    if (PaymentApplyTypeEnums.relationMM066.contains(applyType)) {
      resolveMM066(applyType, paymentApplyRecord, paymentApplyDetails, financialVouchers, supplier,
          user);
    }
    // MM034
    if (PaymentApplyTypeEnums.relationMM034.contains(applyType)) {
      resolveMM034(paymentApplyRecord, user);
    }
    paymentApplyRecord.setSyncSapTime(System.currentTimeMillis());
    paymentApplyRecordRepository.save(paymentApplyRecord);
  }

  /**
   * 调用sap接口 MM034
   */
  private void resolveMM034(PaymentApplyRecord paymentApplyRecord,
      User user) {
    // 构建参数区分 预付款\提款\冲销\退款
    AdvanceApplyParam advanceApplyParam = sapFactory.createMM034Param(paymentApplyRecord, user);
    AdvanceApplyResult advanceApplyResult =
        sapService.sapAdvanceApplyWithLockGroup(advanceApplyParam);
    // 使用sap回传信息
    ApprovalResultsParam param = new ApprovalResultsParam();
    param.setPaymentApplyNo(paymentApplyRecord.getPaymentApplyNo());
    RETURNDTO returnX = advanceApplyResult.getReturnX();
    param.setFinancialVoucherNo(returnX.getFinancialVoucherNo());
    param.setAccountingYear(returnX.getAccountingYear());
    param.setApprovalResults(paymentApplyRecord.getApplyState());
    param.setApprovalInstructions(paymentApplyRecord.getRejectReason());
    List<String> voucherLineItems =
        CollUtil.emptyIfNull(returnX.getItem()).stream().map(Item::getVoucherLineItem)
            .collect(Collectors.toList());
    List<String> paymentApplyLineNumber =
        CollUtil.emptyIfNull(returnX.getItem()).stream().map(Item::getPaymentApplyLineNumber)
            .collect(Collectors.toList());
    param.setVoucherLineItems(voucherLineItems);
    param.setPaymentApplyLineNumber(paymentApplyLineNumber);
    // 原有审核方法
    auditMethod(param, paymentApplyRecord, paymentApplyRecord.getApplyState());

  }

  /**
   * 调用sap接口 MM066
   */
  private void resolveMM066(String applyType, PaymentApplyRecord paymentApplyRecord,
      List<PaymentApplyDetail> paymentApplyDetails, List<FinancialVoucher> financialVouchers,
      Supplier supplier, User user) {
    String sapType  = PaymentApplyTypeEnums.key2SapEnum.get(applyType).getKey();
    SapDrawApplyParam instance =
        SapDrawApplyParam.getInstance(paymentApplyRecord, paymentApplyDetails, financialVouchers,
            supplier.getMdmCode(), user, sapType, new HashMap<>());
    sapService.sapDraw(instance);
    // 原有回传信息
    ApprovalResultsParam param = new ApprovalResultsParam();
    param.setPaymentApplyNo(paymentApplyRecord.getPaymentApplyNo());
    param.setApprovalResults(paymentApplyRecord.getApplyState());
    param.setApprovalInstructions(paymentApplyRecord.getRejectReason());
    // 原有审核方法
    auditMethod(param, paymentApplyRecord, paymentApplyRecord.getApplyState());
  }


  @Override
  @Transactional
  public void auditCallBack(ApprovalResult approvalResult, PaymentAuditStateEnum status) {
    PaymentApplyRecord paymentApplyRecord =
        paymentApplyRecordRepository.findFirstByReviewIdAndState(
            approvalResult.getProcessInstanceId(), Constants.STATE_OK).orElseThrow(
            () -> CheckException.noFindException(PaymentApplyRecord.class,
                approvalResult.getProcessInstanceId()));
    paymentApplyRecord.setApplyState(status.getKey());
    paymentApplyRecord.setAuditTime(approvalResult.getFinishTime());
    paymentApplyRecord.setRejectReason(approvalResult.getRemark());
    if (status == PaymentAuditStateEnum.REJECTED) {
      paymentApplyRecordRepository.saveAndFlush(paymentApplyRecord);
      return;
    }

    // 同步sap
    try {
      ShardingContext.load(VersionEnum.ALL, () -> {
        syncSAP(paymentApplyRecord.getId());
        return null;
      });
    } catch (Exception e) {
      log.error("调用SAP失败", e);
      String env = bootConfig.getEnv();
      String msg =
          StrUtil.format("【{}环境 {}】 【付款申请{}审核通过】调用SAP失败：异常信息：{} ，请及时处理！", env,
              bootConfig.getAppName(), paymentApplyRecord.getPaymentApplyNo(), e.getMessage());
      DingUtils.sendMsgByWarningRobot(msg, env);
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void auditMethod(ApprovalResultsParam params, PaymentApplyRecord paymentApplyRecord,
      String approvalResults) {
    List<PaymentApplyDetail> paymentApplyDetails =
        paymentApplyDetailRepository.findByPaymentApplyRecordId(paymentApplyRecord.getId());
    if (CollUtil.isEmpty(paymentApplyDetails)) {
      throwCheckException("申请单号错误");
    }
    /**
     * 通过情况：①不校验：延长申请、加急申请、冻结申请、解冻申请、预付申请冲销
     *          ②校验：预付申请、提款申请
     *          ③自动填充行项目与申请号：一次性供应商(预付款和提款申请通过)、退款申请
     *
     *不通过情况：完全不校验
     *
     */
    // 1. 获取总凭证数
    List<String> totalFinancialVouchersIds  =
        paymentApplyDetails.stream().map(PaymentApplyDetail::getFinancialVouchersIdList)
            .flatMap(List::stream).collect(Collectors.toList());
    // 1.1 状态是否为预付或提款
    boolean isAdvanceOrDrawType =
        ConstantsFinance.CHECK_VOUCHER_LINE_ITEMS.contains(paymentApplyRecord.getApplyType());
    // 1.2 状态是否通过
    boolean isApprovalPass = Objects.equals(Constants.PAYMENT_APPLY_PASS, approvalResults);
    // 1.4 是否为退款申请
    boolean isReturn = PaymentApplyTypeEnums.RETURN.getKey().equals(paymentApplyRecord.getApplyType());
    // 1.5 是否为一次性供应商
    boolean isDisposableSupplier = paymentApplyDetails.stream().anyMatch(paymentApplyDetail -> StrUtil.startWith(paymentApplyDetail.getSupplierName(), "一次性供应商"));
    // 2 条件判断
    // 2.1 先判断是否申请失败
    if (!isApprovalPass) {
      // 生成空的行号与项目号
      params.setPaymentApplyLineNumber(Collections.nCopies(totalFinancialVouchersIds.size(), ""));
      params.setVoucherLineItems(Collections.nCopies(totalFinancialVouchersIds.size(), ""));
    } else if (isAdvanceOrDrawType && isDisposableSupplier) {
      // 2.2 判断是否为 预付 + 提款 + 一次性供应商
      // 2.2.1 如果预付申请的SAP财务凭证号为空则设置默认
      if (StrUtil.isBlank(params.getFinancialVoucherNo())) {
        params.setFinancialVoucherNo("**********");
      }
      int defaultIndex = 0;
      List<String> defaultVoucherLineItems = new ArrayList<>();
      List<String> defaultApplyLineNumber = new ArrayList<>();
      for (String financialVouchersId : totalFinancialVouchersIds) {
        defaultIndex++;
        defaultVoucherLineItems.add(String.format("%03d", defaultIndex));
        defaultApplyLineNumber.add(String.format("%03d", defaultIndex));
      }
      params.setPaymentApplyLineNumber(defaultVoucherLineItems);
      params.setVoucherLineItems(defaultApplyLineNumber);
    } else if (isReturn) {
      // 2.3 判断是否为退款申请 + 通过
      // 2.3.1 如果退款申请的SAP财务凭证号为空则设置默认
      if (StrUtil.isBlank(params.getFinancialVoucherNo())) {
        params.setFinancialVoucherNo("**********");
      }
      int defaultIndex = 0;
      List<String> defaultVoucherLineItems = new ArrayList<>();
      List<String> defaultApplyLineNumber = new ArrayList<>();
      for (String financialVouchersId : totalFinancialVouchersIds) {
        defaultIndex++;
        defaultVoucherLineItems.add(String.format("%03d", defaultIndex));
        defaultApplyLineNumber.add(String.format("%03d", defaultIndex));
      }
      params.setPaymentApplyLineNumber(defaultVoucherLineItems);
      params.setVoucherLineItems(defaultApplyLineNumber);
    } else if (!isAdvanceOrDrawType) {
      // 2.4 判断是否为延长申请、加急申请、冻结申请、解冻申请、预付申请冲销
      params.setPaymentApplyLineNumber(Collections.nCopies(totalFinancialVouchersIds.size(), "01"));
      params.setVoucherLineItems(Collections.nCopies(totalFinancialVouchersIds.size(), "001"));
    }
    // 3 校验匹配
    int financialVouchersIndex = 0;
    for (PaymentApplyDetail paymentApplyDetail : paymentApplyDetails) {
      List<String> financialVouchersIds = paymentApplyDetail.getFinancialVouchersIdList();
      // 3.1 预付申请 + 提款申请
      if (isAdvanceOrDrawType && isApprovalPass) {
        // 3.2 是否申请的行号项目号一致
        boolean applyCommonSize = CollUtil.isNotEmpty(params.getVoucherLineItems())
            && CollUtil.isNotEmpty(params.getPaymentApplyLineNumber())
            && params.getVoucherLineItems().size() == params.getPaymentApplyLineNumber().size();
        // sap凭证行项目不为空
        if (!applyCommonSize || totalFinancialVouchersIds.size() != params.getVoucherLineItems().size()) {
          throw new CheckException("传入的凭证行项目不匹配");
        }
        if (CollUtil.isEmpty(params.getPaymentApplyLineNumber())) {
          throwCheckException("审批结果为通过时，请填写付款申请行号");
        }
        if (CollUtil.isEmpty(params.getVoucherLineItems())) {
          throwCheckException("审批结果为通过时，请填写凭证行项目");
        }
      }
      // 统一调用 processFinancialVoucher 方法
      for (String financialVouchersId : financialVouchersIds) {
        List<String> voucherLineItems = params.getVoucherLineItems();
        List<String> paymentApplyLineNumbers = params.getPaymentApplyLineNumber();
        processFinancialVoucher(
            financialVouchersId,
            voucherLineItems.get(financialVouchersIndex),
            paymentApplyLineNumbers.get(financialVouchersIndex), approvalResults,
            paymentApplyRecord.getApplyType(),
            paymentApplyDetail, params
        );
        financialVouchersIndex++;
      }
    }
  }

  @Override
  public PaymentApplyProcessParam buildPaymentApplyProcessParam(PaymentApplyRecord record,
      List<PaymentApplyDetail> details) {
    List<String> financialVoucherIds =
        details.stream().map(PaymentApplyDetail::getFinancialVouchersId)
            .map(ids-> StrUtil.split(ids, ','))
            .flatMap(List::stream)
            .collect(Collectors.toList());
    List<FinancialVoucher> financialVouchers =
        financialVoucherRepository.findAllById(financialVoucherIds);
    Set<String> supplierIds =
        financialVouchers.stream().map(FinancialVoucher::getSupplierId).collect(Collectors.toSet());
    Supplier supplier =
        supplierRepository.findAllById(supplierIds).stream().findFirst().orElse(null);
    if (supplier == null) {
      throw new CheckException("没有找到供应商信息");
    }
    List<String> codes =
        financialVouchers.stream().map(FinancialVoucher::getPurchaseOrderNo).distinct()
            .collect(Collectors.toList());
    codes.add("-1");
    List<SupplierOrder> supplierOrders =
        supplierOrderRepository.findAllByCodeInAndState(codes, Constants.STATE_OK);
    SupplierOrder supplierOrder = supplierOrders.stream().findFirst().orElseThrow(
        () -> CheckException.noFindException(SupplierOrder.class, codes.toString()));
    Group group = groupDao.getOrgByErpCode(record.getGroupCode());
    String applyType = record.getApplyType();
    String applyMan = record.getApplyMan();
    User applyUser = userRepository.findFirstByRealNameAndState(applyMan, Constants.STATE_OK);
    BigDecimal applyAmount = sharePaymentApplyDetailService.getApplyAmount(details, applyType);
    String supplierNames =
        details.stream().map(PaymentApplyDetail::getSupplierName).filter(StrUtil::isNotBlank)
            .distinct().collect(Collectors.joining(","));
    PaymentApplyDetail paymentApplyDetail =
        details.stream().findFirst().orElseThrow(() -> new CheckException("没有找到付款申请明细"));
    // 期望/预计付款日期
    Long expectedTime;
    if (StrUtil.equalsAny(applyType, PaymentApplyTypeEnums.DRAW.getKey(),
        PaymentApplyTypeEnums.ADVANCE.getKey(), PaymentApplyTypeEnums.RETURN.getKey())) {
      expectedTime = paymentApplyDetail.getAdvanceDate();
    } else {
      expectedTime = null;
    }
    PaymentApplyProcessParam docJson = new PaymentApplyProcessParam();
    docJson.setPaymentApplyNo(record.getPaymentApplyNo());
    docJson.setApplyType(Optional.ofNullable(PaymentApplyTypeEnums.fromKey(applyType))
        .map(PaymentApplyTypeEnums::getName).orElse(StrUtil.EMPTY));
    docJson.setGroupName(Optional.ofNullable(group.getName()).orElse(StrUtil.EMPTY));
    docJson.setPurchaseDepartment(supplierOrder.getPurchaseDept());
    docJson.setApplyTime(DateUtils.formatTimeStampToNormalDateTime(record.getApplyTime()));
    docJson.setTotalAmount(applyAmount.stripTrailingZeros().toPlainString());
    docJson.setCurrency(supplierOrder.getMoneyCode());
    docJson.setPurchaseMan(applyMan);
    docJson.setPurchaseCode(applyUser.getCode());
    docJson.setSupplierName(supplierNames);
    if (StrUtil.equalsAny(applyType, PaymentApplyTypeEnums.ADVANCE.getKey(),
        PaymentApplyTypeEnums.DRAW.getKey())) {
      docJson.setPaymentTermsStr(supplierOrder.getPaymentTermsStr());
    }
    docJson.setBankAccount(paymentApplyDetail.getBankAccount());
    docJson.setBank(paymentApplyDetail.getBank());
    docJson.setBankCode(paymentApplyDetail.getBankCode());
    docJson.setAccountName(paymentApplyDetail.getAccountName());
    docJson.setRemark(paymentApplyDetail.getRemark());
    docJson.setPurchaseAccount(paymentApplyDetail.getPurchaseAccount());
    docJson.setOrderNo(paymentApplyDetail.getOrderNo());
    docJson.setProductName(paymentApplyDetail.getProductName());
    docJson.setGroupCode(record.getGroupCode());
    // detail
    List<Detail> collect = financialVouchers.stream().map(financialVoucher -> {
      Detail processDetail = new Detail();
      processDetail.setSupplierOrderNo(financialVoucher.getPurchaseOrderNo());
      SupplierOrder supplierOrder1 =
          supplierOrderRepository.findFirstByCodeAndStateIn(financialVoucher.getPurchaseOrderNo(),
              CollUtil.toList(Constants.STATE_OK, Constants.STATE_LOCKED));
      processDetail.setOrderSettlePrice(NumberUtil.null2Zero(
              NumberUtil.sub(supplierOrder.getPrice(), supplierOrder.getCancelReturnPrice()))
          .stripTrailingZeros().toPlainString());
      processDetail.setThisAmount(
          financialVoucher.getRelatedAmount().stripTrailingZeros().toPlainString());
      processDetail.setDesireDate(DateUtils.formatTimeStampToNormalDateTime(
          ObjectUtil.defaultIfNull(expectedTime, financialVoucher.getExpectedPaymentDate())));
      processDetail.setPayType(financialVoucher.getPaymentType());
      if (StrUtil.equalsAny(applyType, PaymentApplyTypeEnums.DRAW.getKey())) {
        processDetail.setInvoiceOrderNo(financialVoucher.getInvoiceOrderNo());
        processDetail.setVoucherPrice(
            financialVoucher.getVoucherPrice().stripTrailingZeros().toPlainString());
        processDetail.setRelatedAmount(
            BigDecimalUtil.formatForStandard(financialVoucher.getRelatedAmount()).toPlainString());
      }
      if (!StrUtil.equalsAny(applyType, PaymentApplyTypeEnums.DRAW.getKey(),
          PaymentApplyTypeEnums.ADVANCE.getKey())) {
        processDetail.setFinancialVoucherNo(financialVoucher.getFinancialVoucherNo());
        processDetail.setAccountingYear(financialVoucher.getAccountingYear());
        processDetail.setVoucherType(
            VoucherTypeEnum.getDescByKey(financialVoucher.getVoucherType()));
      }
      return processDetail;
    }).collect(Collectors.toList());
    docJson.setDetailList(collect);
    return docJson;
  }

  /**
   * 处理财务凭证
   * @param financialVouchersId 财务凭证ID
   * @param voucherLineItem 凭证行项目
   * @param paymentApplyLineNumber 付款申请行号
   * @param approvalResults 审批结果
   * @param applyType 申请类型
   * @param paymentApplyDetail 付款申请明细
   * @param params 审批结果参数
   */
  private void processFinancialVoucher(String financialVouchersId, String voucherLineItem, String paymentApplyLineNumber,
      String approvalResults, String applyType,
      PaymentApplyDetail paymentApplyDetail, ApprovalResultsParam params) {
    FinancialVoucher financialVoucher = financialVoucherRepository
        .findById(financialVouchersId)
        .orElseThrow(() -> CheckException.noFindException(FinancialVoucher.class, financialVouchersId));

    if (Constants.PAYMENT_APPLY_PASS.equals(approvalResults)) {
      doApprovedByReview(applyType, financialVoucher, paymentApplyDetail, params, voucherLineItem, paymentApplyLineNumber);
    } else if (Constants.PAYMENT_APPLY_UNPASS.equals(approvalResults)) {
      //（提款申请、退款申请）如果是驳回状态，需要释放此次申请的金额
      updateApprovedReleaseAmount(applyType, financialVoucher, paymentApplyDetail);
    }
  }

  private void doApprovedByReview(String applyType, FinancialVoucher financialVoucher,
      PaymentApplyDetail paymentApplyDetail, ApprovalResultsParam params, String voucherLineItem,
      String paymentApplyLineNumber) {
    // 只有提款、预付款、退款才需要写入财务凭证号和凭证年度
    if (Objects.equals(Constants.PAYMENT_TYPE_ADVANCE, applyType)) {
      updatePaymentApplyDetail(paymentApplyDetail, params.getAccountingYear(), params.getFinancialVoucherNo());
      doApprovedByReviewByAdvance(financialVoucher, params,voucherLineItem,paymentApplyLineNumber);
    }
    if (Objects.equals(Constants.PAYMENT_TYPE_FREEZE, applyType)) {
      doApprovedByReviewByFreeze(financialVoucher, params, voucherLineItem, paymentApplyLineNumber);
    }
    if (Objects.equals(Constants.PAYMENT_TYPE_URGENT, applyType)) {
      doApprovedByReviewByUrgent(financialVoucher, paymentApplyDetail, params,voucherLineItem, paymentApplyLineNumber);
    }
    if (Objects.equals(Constants.PAYMENT_TYPE_EXTEND, applyType)) {
      doApprovedByReviewByExtend(financialVoucher, paymentApplyDetail, params,voucherLineItem, paymentApplyLineNumber);
    }
    if (Objects.equals(Constants.PAYMENT_TYPE_DRAW, applyType)) {
      updatePaymentApplyDetail(paymentApplyDetail, params.getAccountingYear(), params.getFinancialVoucherNo());
      updateNewFinancialVoucherNo(paymentApplyDetail, params, applyType);
      doApprovedByReviewByDraw(financialVoucher, paymentApplyDetail);
    }
    if (Objects.equals(Constants.PAYMENT_TYPE_RETURN, applyType)) {
      updatePaymentApplyDetail(paymentApplyDetail, params.getAccountingYear(), params.getFinancialVoucherNo());
      updateNewFinancialVoucherNo(paymentApplyDetail, params, applyType);
      doApprovedByReviewByReturn(financialVoucher, paymentApplyDetail);
    }
    if (Objects.equals(Constants.PAYMENT_TYPE_THAW, applyType)) {
      doApprovedByReviewByThaw(financialVoucher);
    }
    if (Objects.equals(PaymentApplyTypeEnums.ADVANCE_REVERSAL.getKey(), applyType)) {
      doApprovedByPrepaidApplicationReversal(financialVoucher);
    }
  }

  private void doApprovedByReviewByReturn(FinancialVoucher financialVoucher,
      PaymentApplyDetail paymentApplyDetail) {
    financialVoucher.setState(Constants.STATE_OK);
    List<ThisAmountDTO> amountDTO = thisAmountConvertObject(paymentApplyDetail.getThisAmount());
    ThisAmountDTO thisAmount = getThisAmount(financialVoucher.getId(), amountDTO);
    financialVoucher.setRefundAmount(NumberUtil.add(financialVoucher.getRefundAmount(),
        thisAmount.getAmount()));
    financialVoucher.setRemainingRefundableAmount(financialVoucher.getRemainingRefundableAmount() == null
        ? financialVoucher.getVoucherPrice().subtract(thisAmount.getAmount())
        : financialVoucher.getRemainingRefundableAmount().subtract(thisAmount.getAmount()));
    financialVoucherRepository.save(financialVoucher);
    // 这里将发票过账凭证的退款金额给释放掉
    String invoiceOrderNo = financialVoucher.getInvoiceOrderNo();
    String purchaseOrderNo = financialVoucher.getPurchaseOrderNo();
    // 期初处理
    if (Boolean.TRUE.equals(financialVoucher.getInitialOrder())) {
      financialVoucherRepository.getFirstByPurchaseOrderNoAndVoucherTypeAndStateAndSupplierId(purchaseOrderNo,
          VoucherTypeEnum.INVOICE_POSTING.getKey(), Constants.STATE_OK, financialVoucher.getSupplierId()).ifPresent(financialVoucher1 -> {
        financialVoucher1.setRefundAmount(NumberUtil.add(financialVoucher1.getRefundAmount(),
            thisAmount.getAmount()));
        financialVoucherRepository.save(financialVoucher);
      });
    }
    if (StrUtil.isAllNotBlank(invoiceOrderNo,purchaseOrderNo)) {
      financialVoucherRepository.getFirstByInvoiceOrderNoAndPurchaseOrderNoAndVoucherTypeAndState(invoiceOrderNo,purchaseOrderNo,
          VoucherTypeEnum.INVOICE_POSTING.getKey(),Constants.STATE_OK).ifPresent(financialVoucher1 -> {
        financialVoucher1.setRefundAmount(NumberUtil.add(financialVoucher1.getRefundAmount(),
            thisAmount.getAmount()));
        financialVoucherRepository.save(financialVoucher);
      });
    }
  }

  private void doApprovedByPrepaidApplicationReversal(FinancialVoucher financialVoucher) {
    financialVoucher.setPrepaidOffsetStatus(Constants.STATE_OK);
    financialVoucherRepository.save(financialVoucher);
  }

  private void doApprovedByReviewByThaw(FinancialVoucher financialVoucher) {
    financialVoucher.setPaymentFreezeStatus(PaymentFreezeStatusEnum.THAWED.getKey());
    financialVoucherRepository.save(financialVoucher);
  }

  //提款和退款需求
  private void updateNewFinancialVoucherNo(PaymentApplyDetail paymentApplyDetail,
      ApprovalResultsParam params,String applyType) {
    if (StrUtil.isBlank(paymentApplyDetail.getNewFinancialVouchersId())) {
      return;
    }
    List<String> financialVouchersIds =
        CollUtil.toList(paymentApplyDetail.getNewFinancialVouchersId().split(","));
    if (CollUtil.isNotEmpty(params.getVoucherLineItems()) && CollUtil.isNotEmpty(params.getPaymentApplyLineNumber())) {
      updateVouchers(financialVouchersIds, params, true, applyType);
    } else {
      updateVouchers(financialVouchersIds, params, false, applyType);
    }
  }

  private void updatePaymentApplyDetail(PaymentApplyDetail paymentApplyDetail,
      String accountingYear, String financialVoucherNo) {
    paymentApplyDetail.setAccountingYear(accountingYear);
    paymentApplyDetail.setFinancialVouchers(financialVoucherNo);
    paymentApplyDetailRepository.save(paymentApplyDetail);
  }

  private void doApprovedByReviewByAdvance(FinancialVoucher financialVoucher,
      ApprovalResultsParam params, String voucherLineItem, String paymentApplyLineNumber) {
    financialVoucher.setState(Constants.STATE_OK);
    financialVoucher.setBaseDate(System.currentTimeMillis());
    financialVoucher.setPaymentFreezeStatus(PaymentFreezeStatusEnum.THAWED.getKey());
    financialVoucher.setVoucherLineItems(voucherLineItem);
    financialVoucher.setFinancialVoucherNo(StrUtil.blankToDefault(params.getFinancialVoucherNo(), "**********"));
    financialVoucher.setAccountingYear(params.getAccountingYear());
    financialVoucher.setPaymentApplyLineNumber(paymentApplyLineNumber);
    // 预付款/应付款--付款方式不是银行转账，付款状态默认取支付成功
    if (!PayTypeSAPEnums.TRANSFER.getName().equals(financialVoucher.getPaymentType())) {
      financialVoucher.setVoucherPaymentState(VoucherPaymentStateEnum.PAYMENT_SUCCESSFUL.getKey());
    }
    financialVoucherRepository.save(financialVoucher);
  }

  private void doApprovedByReviewByFreeze(FinancialVoucher financialVoucher,
      ApprovalResultsParam params,String voucherLineItem, String paymentApplyLineNumber) {
    financialVoucher.setPaymentFreezeStatus(PaymentFreezeStatusEnum.FROZEN.getKey());
    financialVoucher.setVoucherLineItems(voucherLineItem);
    //    financialVoucher.setFinancialVoucherNo(params.getFinancialVoucherNo());
    //    financialVoucher.setAccountingYear(params.getAccountingYear());
    financialVoucher.setPaymentApplyLineNumber(paymentApplyLineNumber);
    financialVoucherRepository.save(financialVoucher);
  }

  private void doApprovedByReviewByUrgent(FinancialVoucher financialVoucher,
      PaymentApplyDetail paymentApplyDetail, ApprovalResultsParam params, String voucherLineItem,
      String paymentApplyLineNumber) {
    financialVoucher.setExpectedPaymentDate(paymentApplyDetail.getUpdateAdvanceDate());
    financialVoucher.setAccountPeriod(paymentApplyDetail.getUpdatePeriod());
    financialVoucher.setVoucherLineItems(voucherLineItem);
    //    financialVoucher.setFinancialVoucherNo(params.getFinancialVoucherNo());
    //    financialVoucher.setAccountingYear(params.getAccountingYear());
    financialVoucher.setPaymentApplyLineNumber(paymentApplyLineNumber);
    financialVoucherRepository.save(financialVoucher);
  }

  private void doApprovedByReviewByExtend(FinancialVoucher financialVoucher,
      PaymentApplyDetail paymentApplyDetail, ApprovalResultsParam params, String voucherLineItem,
      String paymentApplyLineNumber) {
    financialVoucher.setExpectedPaymentDate(paymentApplyDetail.getUpdateAdvanceDate());
    financialVoucher.setAccountPeriod(paymentApplyDetail.getUpdatePeriod());
    financialVoucher.setVoucherLineItems(voucherLineItem);
    //    financialVoucher.setFinancialVoucherNo(params.getFinancialVoucherNo());
    //    financialVoucher.setAccountingYear(params.getAccountingYear());
    financialVoucher.setPaymentApplyLineNumber(paymentApplyLineNumber);
    financialVoucherRepository.save(financialVoucher);
  }

  private void doApprovedByReviewByDraw(FinancialVoucher financialVoucher,
      PaymentApplyDetail paymentApplyDetail) {
    financialVoucher.setPaymentFreezeStatus(PaymentFreezeStatusEnum.THAWED.getKey());
    financialVoucher.setState(Constants.STATE_OK);
    List<ThisAmountDTO> amountDTO = thisAmountConvertObject(paymentApplyDetail.getThisAmount());
    ThisAmountDTO thisAmount = getThisAmount(financialVoucher.getId(), amountDTO);
    financialVoucher.setWithdrawnAmount(NumberUtil.add(financialVoucher.getWithdrawnAmount(),
        thisAmount.getAmount()));
    // 2024年10月31日17:09:02 通过不需要再扣一次剩余可提款金额
    //    financialVoucher.setRemainingWithdrawableAmount(NumberUtil.sub(financialVoucher.getRemainingWithdrawableAmount(),
    //        thisAmount.getAmount()));
    // 预付款/应付款--付款方式不是银行转账，付款状态默认取支付成功
    if (!PayTypeSAPEnums.TRANSFER.getName().equals(financialVoucher.getPaymentType())) {
      financialVoucher.setVoucherPaymentState(VoucherPaymentStateEnum.PAYMENT_SUCCESSFUL.getKey());
    }
    financialVoucherRepository.save(financialVoucher);
  }

  private void updateApprovedReleaseAmount(String applyType, FinancialVoucher financialVoucher, PaymentApplyDetail paymentApplyDetail) {
    if (Constants.PAYMENT_TYPE_DRAW.equals(applyType) || Constants.PAYMENT_TYPE_RETURN.equals(applyType)) {
      List<ThisAmountDTO> amountDTO = thisAmountConvertObject(paymentApplyDetail.getThisAmount());
      ThisAmountDTO thisAmount = getThisAmount(financialVoucher.getId(), amountDTO);
      BigDecimal amount = thisAmount.getAmount();
      updateFinancialVoucherFields(financialVoucher, amount, applyType);
      financialVoucher.setState(Constants.STATE_OK);
      financialVoucherRepository.save(financialVoucher);
    }
  }

  private List<ThisAmountDTO> thisAmountConvertObject(String thisAmount) {
    return JSON.parseObject(thisAmount, new TypeReference<List<ThisAmountDTO>>() {});
  }

  private void updateFinancialVoucherFields(FinancialVoucher financialVoucher, BigDecimal amount, String applyType) {
    if (Constants.PAYMENT_TYPE_DRAW.equals(applyType)) {
      financialVoucher.setRemainingWithdrawableAmount(
          financialVoucher.getRemainingWithdrawableAmount() == null ? amount
              : NumberUtil.add(financialVoucher.getRemainingWithdrawableAmount(), amount));
    } else if (Constants.PAYMENT_TYPE_RETURN.equals(applyType)) {
      financialVoucher.setRemainingRefundableAmount(
          financialVoucher.getRemainingRefundableAmount() == null ? amount
              : NumberUtil.add(financialVoucher.getRemainingRefundableAmount(), amount));
    }
  }

  private void updateVouchers(List<String> financialVouchersIds, ApprovalResultsParam params,
      boolean isDetailed,String applyType) {
    financialVouchersIds.forEach(id -> {
      FinancialVoucher financialVoucher = financialVoucherRepository.findById(id)
          .orElseThrow(() -> CheckException.noFindException(FinancialVoucher.class, id));
      if (StrUtil.equals(Constants.PAYMENT_TYPE_DRAW, applyType)) {
        financialVoucher.setFinancialVoucherNo(
            StrUtil.blankToDefault(params.getFinancialVoucherNo(), "**********"));
      } else {
        financialVoucher.setFinancialVoucherNo(
            StrUtil.blankToDefault(params.getFinancialVoucherNo(), "**********"));
      }
      financialVoucher.setAccountingYear(params.getAccountingYear());
      financialVoucher.setState(Constants.STATE_OK);
      int index = financialVouchersIds.indexOf(id);
      if (isDetailed) {
        financialVoucher.setVoucherLineItems(params.getVoucherLineItems().get(index));
        financialVoucher.setPaymentApplyLineNumber(params.getPaymentApplyLineNumber().get(index));
      } else {
        // 格式化索引，例如：001, 002
        String defaultIndex = String.format("%03d", index + 1);
        financialVoucher.setVoucherLineItems(defaultIndex);
        financialVoucher.setPaymentApplyLineNumber(defaultIndex);
      }
      // 预付款/应付款--付款方式不是银行转账，付款状态默认取支付成功
      if (!PayTypeSAPEnums.TRANSFER.getName().equals(financialVoucher.getPaymentType())) {
        financialVoucher.setVoucherPaymentState(VoucherPaymentStateEnum.PAYMENT_SUCCESSFUL.getKey());
      }
      financialVoucher.setBaseDate(System.currentTimeMillis());
      financialVoucherRepository.save(financialVoucher);
    });
  }

  private ThisAmountDTO getThisAmount(String financialVoucherId,
      List<ThisAmountDTO> thisAmountDTOS) {
    return thisAmountDTOS.stream()
        .filter(thisAmountDTO -> Objects.equals(thisAmountDTO.getId(), financialVoucherId))
        .findFirst().orElseThrow(() -> new CheckException("数据异常"));
  }

  private void throwCheckException(String message) {
    throw new CheckException(message);
  }
}
