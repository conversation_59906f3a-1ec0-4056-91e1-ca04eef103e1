package com.xhgj.srm.dto.order;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.entity.OrderReturn;
import com.xhgj.srm.jpa.entity.OrderReturnDetail;
import com.xhgj.srm.util.PlatformUtil;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OrderReturnDetailExportDTO
 */
@Data
@NoArgsConstructor
public class OrderReturnDetailExportDTO {

  @ApiModelProperty("客户订单号")
  private String orderNo;

  @ApiModelProperty("订单状态")
  private String orderState;

  @ApiModelProperty("下单平台")
  private String platform;

  @ApiModelProperty("供应商名称")
  private String enterpriseName;

  @ApiModelProperty("退货编号")
  private String returnNo;

  @ApiModelProperty("退货时间")
  private String returnTime;

  @ApiModelProperty("退货金额")
  private String returnPrice;

  @ApiModelProperty("退货状态(中文)")
  private String stateStr;

  @ApiModelProperty(" ERP 采购退料单号")
  private String erpNo;

  @ApiModelProperty("退货单采购订单号")
  private String purchaseOrderNo;

  @ApiModelProperty("序号")
  private String rowNum;

  @ApiModelProperty("物料编码")
  private String productCode;

  @ApiModelProperty("品牌")
  private String brand;

  @ApiModelProperty("商品名称")
  private String productName;

  @ApiModelProperty("规格型号")
  private String manuCode;

  @ApiModelProperty("退货数量")
  private String returnNum;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("单价")
  private String productPrice;

  @ApiModelProperty("发货明细id")
  private String deliveryDetailId;

  public OrderReturnDetailExportDTO(Order order) {
    this.orderNo = order.getOrderNo();
    this.platform = StrUtil.emptyIfNull(PlatformUtil.getPlatformNameByCode(order.getType()));
    this.enterpriseName = order.getSupplier() != null ? StrUtil.emptyIfNull(order.getSupplier().getEnterpriseName()) : StrUtil.EMPTY;
    this.orderState = !StringUtils.isNullOrEmpty(order.getOrderState()) ? Constants_order.ORDER_STATE_MAP.get(order.getOrderState()) : "待履约";
  }

  public OrderReturnDetailExportDTO(Order order, OrderReturn orderReturn) {
    this(order);
    this.returnNo = orderReturn.getReturnNo();
    this.returnTime = orderReturn.getCreateTime() > 0 ? DateUtils.formatTimeStampToNormalDateTime(orderReturn.getCreateTime()) : StrUtil.EMPTY;
    this.returnPrice = BigDecimalUtil.formatForStandard(orderReturn.getPrice()).toPlainString();
    this.stateStr = StrUtil.isNotBlank(orderReturn.getReturnState())
        ? Constants_order.ORDER_RETURN_STATE_MAP.get(orderReturn.getReturnState()) : StrUtil.EMPTY;
    this.erpNo = StrUtil.emptyIfNull(orderReturn.getErpNo());
    this.purchaseOrderNo = StrUtil.emptyIfNull(orderReturn.getPurchaseOrderNo());

  }
  public OrderReturnDetailExportDTO(Order order, OrderReturn orderReturn, OrderReturnDetail orderReturnDetail, int index) {
    this(order, orderReturn);
    this.rowNum = String.valueOf(index);
    this.productCode = StrUtil.emptyIfNull(orderReturnDetail.getCode());
    this.brand = StrUtil.emptyIfNull(orderReturnDetail.getBrand());
    this.productName = StrUtil.emptyIfNull(orderReturnDetail.getName());
    this.manuCode = StrUtil.emptyIfNull(orderReturnDetail.getModel());
    this.returnNum = BigDecimalUtil.formatForStandard(orderReturnDetail.getReturnNum()).toPlainString();
    this.unit = StrUtil.emptyIfNull(orderReturnDetail.getUnit());
    this.productPrice = BigDecimalUtil.formatForStandard(orderReturnDetail.getPrice()).toPlainString();
    this.deliveryDetailId = orderReturnDetail.getDeliveryDetailId();
  }
}