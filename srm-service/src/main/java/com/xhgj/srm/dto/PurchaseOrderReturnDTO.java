package com.xhgj.srm.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.jpa.dto.RetreatWarehousePageDTO;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Optional;
import lombok.Data;

@Data
public class PurchaseOrderReturnDTO {

  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("code")
  private String code;

  @ApiModelProperty("退库时间")
  private Long returnTime;

  @ApiModelProperty("退库原因")
  private String retreatReason;

  @ApiModelProperty("退库仓库")
  private String retreatWarehouse;

  @ApiModelProperty(value = "仓库执行状态")
  private String warehouseExecuteStatus;

  @ApiModelProperty("物流公司")
  private String logisticsCompany;

  @ApiModelProperty("快递单号")
  private String trackNum;

  @ApiModelProperty("收件人")
  private String recipient;

  @ApiModelProperty("收件人地址")
  private String recipientAddress;

  @ApiModelProperty("是否需要开红票")
  private Boolean needRedInvoice;

  @ApiModelProperty("SAP物料凭证号")
  private String productVoucherNo;

  @ApiModelProperty("SAP退库单采购订单号")
  private String sapPurchaseOrderNo;

  @ApiModelProperty("SAP退库单物料凭证行项目")
  private String sapProductVoucherLineItem;

  @ApiModelProperty("物料编码")
  private String productCode;

  @ApiModelProperty("品牌")
  private String brand;

  @ApiModelProperty("名称")
  private String productName;

  @ApiModelProperty("规格型号")
  private String manuCode;

  @ApiModelProperty("退库数量")
  private BigDecimal stockOutputQty;

  @ApiModelProperty("单价")
  private BigDecimal productPrice;

  @ApiModelProperty("已开红票数量")
  private BigDecimal redInvoiceQty;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("是否冲销")
  private Boolean reversal;

  @ApiModelProperty("冲销状态")
  private String reversalStatus;

  @ApiModelProperty("批号")
  private String batchNumber;

  @ApiModelProperty("关联发票号")
  private String purchaseOrderInvoiceRelationList;

  @ApiModelProperty("物料序号")
  private Integer productSort;

  @ApiModelProperty("退库金额")
  private BigDecimal returnAmount;

  @ApiModelProperty("退库单价")
  private BigDecimal returnPrice;

  @ApiModelProperty("采购订单物料行id")
  private String sapRwoId;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("采购员")
  private String purchaseMan;

  @ApiModelProperty("采购部门")
  private String purchaseDept;

  @ApiModelProperty("入库数量")
  private String stockInputQty;

  @ApiModelProperty("采购部门编码")
  private String purchaseDeptCode;
  /** 退库单id */
  private String orderToFormId;

  public PurchaseOrderReturnDTO(RetreatWarehousePageDTO warehousingDTO) {
    this.id = warehousingDTO.getId();
    this.code = warehousingDTO.getCode();
    this.returnTime = warehousingDTO.getTime();
    this.retreatReason = warehousingDTO.getReason();
    this.retreatWarehouse = warehousingDTO.getWarehouse();
    this.warehouseExecuteStatus = warehousingDTO.getExecutionStatus();
    this.logisticsCompany = warehousingDTO.getLogisticsCompany();
    this.trackNum = warehousingDTO.getTrackNum();
    this.recipient = warehousingDTO.getConsignee();
    this.recipientAddress = warehousingDTO.getReceiveAddress();
    this.needRedInvoice = Constants.YES.equals(warehousingDTO.getNeedRedTicket());
    this.productVoucherNo = warehousingDTO.getProductVoucher();
    this.sapPurchaseOrderNo = warehousingDTO.getSapReturnNumber();
    this.sapProductVoucherLineItem = warehousingDTO.getSapRowId();
    this.productCode = warehousingDTO.getProductCode();
    this.brand = warehousingDTO.getBrand();
    this.productName = warehousingDTO.getName();
    this.manuCode = warehousingDTO.getManuCode();
    this.stockOutputQty = warehousingDTO.getStockOutPutQty();
    this.productPrice = warehousingDTO.getPrice();
    this.redInvoiceQty = warehousingDTO.getInvoicedNum();
    this.unit = warehousingDTO.getUnit();
    this.reversal = SupplierOrderFormStatus.REVERSAL.getKey().equals(warehousingDTO.getStatus());
    if (SupplierOrderFormStatus.REVERSAL.getKey().equals(warehousingDTO.getStatus())) {
      this.reversalStatus = SupplierOrderFormStatus.REVERSAL.getDesc();
    } else if (SupplierOrderFormStatus.REVERSAL_IN_PROGRESS
        .getKey()
        .equals(warehousingDTO.getStatus())) {
      this.reversalStatus = SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getDesc();
    } else {
      this.reversalStatus = "未冲销";
    }
    this.batchNumber = warehousingDTO.getBatchNo();
    this.productSort = warehousingDTO.getRowId();
    this.returnAmount = warehousingDTO.getReturnAmount();
    this.returnPrice = warehousingDTO.getReturnPrice();
    this.sapRwoId = warehousingDTO.getDetailId();
    this.supplierName = warehousingDTO.getSupplierName();
    this.purchaseMan = warehousingDTO.getPurchaseMan();
    this.purchaseDept = warehousingDTO.getPurchaseDept();
    this.stockInputQty =
        Optional.ofNullable(warehousingDTO.getStockInputQty())
            .orElse(BigDecimal.ZERO)
            .stripTrailingZeros()
            .toPlainString();
    this.purchaseDeptCode = warehousingDTO.getPurchaseDeptCode();
    this.orderToFormId = warehousingDTO.getOrderToFormId();
  }
}
