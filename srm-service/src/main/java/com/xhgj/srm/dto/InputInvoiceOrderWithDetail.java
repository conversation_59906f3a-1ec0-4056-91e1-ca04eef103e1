package com.xhgj.srm.dto;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.SupplierInvoiceToDetail;
import lombok.Data;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class InputInvoiceOrderWithDetail {

  /**
   * 进项票
   */
  InputInvoiceOrder inputInvoiceOrder;

  /**
   * 供应商发票明细
   */
  List<SupplierInvoiceToDetail> supplierInvoiceToDetails;

  public List<String> getDistinctDetailIds() {
    if (CollUtil.isEmpty(supplierInvoiceToDetails)) {
      return CollUtil.newArrayList();
    }
    return supplierInvoiceToDetails.stream().map(SupplierInvoiceToDetail::getDetailId).distinct()
        .collect(Collectors.toList());
  }
}
