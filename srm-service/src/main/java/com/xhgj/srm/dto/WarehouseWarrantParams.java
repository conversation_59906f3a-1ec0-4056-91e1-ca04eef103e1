package com.xhgj.srm.dto;

import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class WarehouseWarrantParams implements Serializable {
  @ApiModelProperty("勾选的数据行")
  private List<String> ids;

  @ApiModelProperty("搜索方案 id")
  private String schemeId;

  @ApiModelProperty(value = "采购订单号")
  private String orderCode;

  @ApiModelProperty(value = "SAP物料凭证号")
  private String sapProductVoucherNo;

  @ApiModelProperty(value = "批号")
  private String batchNo;

  @ApiModelProperty(value = "已开票数量")
  private Integer invoiceQuantity;

  @ApiModelProperty(value = "已开票数量操作符")
  private LogicalOperatorsEnums invoiceQuantityOperator;

  @ApiModelProperty(value = "关联发票号")
  private String invoiceNo;

  @ApiModelProperty(value = "入库时间起")
  private Long startTime;

  @ApiModelProperty(value = "入库时间止")
  private Long endTime;

  @ApiModelProperty(value = "快递单号")
  private String expressNo;

  @ApiModelProperty(value = "物流公司")
  private String expressCompany;

  @ApiModelProperty(value = "物料编码")
  private String productCode;

  @ApiModelProperty(value = "品牌")
  private String brand;

  @ApiModelProperty(value = "物料名称")
  private String productName;

  @ApiModelProperty(value = "规格型号")
  private String specification;

  @ApiModelProperty(value = "单价")
  private BigDecimal unitPrice;

  @ApiModelProperty(value = "单价操作符")
  private LogicalOperatorsEnums unitPriceOperators;

  @ApiModelProperty(value = "退库数量")
  private BigDecimal returnQuantity;

  @ApiModelProperty(value = "单退库数量操作符")
  private LogicalOperatorsEnums returnQuantityOperator;

  @ApiModelProperty(value = "冲销状态")
  private String reversalStatus;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("采购员")
  private String purchaseMan;

  @ApiModelProperty("采购部门")
  private String purchaseDept;

  @ApiModelProperty("未开票数量")
  private BigDecimal unInvoiceNum;

  @ApiModelProperty(value = "未开票数量操作符")
  private LogicalOperatorsEnums unInvoiceNumOperator;

  @ApiModelProperty(value = "仓库")
  private String warehouse;

  @ApiModelProperty(value = "订单类型")
  private String orderType;

  @ApiModelProperty("含税金额")
  private BigDecimal totalPriceAndTax;
  @ApiModelProperty(value = "含税金额操作符")
  private LogicalOperatorsEnums totalPriceAndTaxOperator;
  @ApiModelProperty("已开票金额")
  private BigDecimal invoicedAmount;
  @ApiModelProperty(value = "已开票金额操作符")
  private LogicalOperatorsEnums invoicedAmountOperator;
  @ApiModelProperty("未开票金额")
  private BigDecimal unInvoicedAmount;
  @ApiModelProperty(value = "未开票金额操作符")
  private LogicalOperatorsEnums unInvoicedAmountOperator;
  @ApiModelProperty("采购申请单号")
  private String purchaseApplyCode;
  @ApiModelProperty("项目编码")
  private String projectNo;
  @ApiModelProperty("业务员")
  private String salesman;
  @ApiModelProperty("销售订单号")
  private String saleOrderNo;
}
