package com.xhgj.srm.dto;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormReviewStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.dto.WarehousingDTO;
import com.xhgj.srm.v2.dto.WarehousingV2DTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

@Data
public class WarehouseWarrantV2DTO {

  @ApiModelProperty("id")
  private String id;

  @ApiModelProperty("code")
  private String code;

  @ApiModelProperty("入库时间")
  private Long warehousingTime;

  @ApiModelProperty("来源")
  private String source;

  @ApiModelProperty("物流公司")
  private String logisticsCompany;

  @ApiModelProperty("快递单号")
  private String trackNum;

  @ApiModelProperty("SAP物料凭证号")
  private String productVoucherNo;

  @ApiModelProperty("SAP退库单物料凭证行项目")
  private String sapProductVoucherLineItem;

  @ApiModelProperty("是否冲销")
  private Boolean reversal;

  @ApiModelProperty("冲销状态")
  private String reversalStatus;

  @ApiModelProperty("单价")
  private String productPrice;

  @ApiModelProperty("批号")
  private String batchNumber;

  @ApiModelProperty("入库数量")
  private String stockInputQty;

  @ApiModelProperty("退库数量")
  private String stockOutputQty;

  @ApiModelProperty("已开票数量")
  private String invoiceNum;

  @ApiModelProperty("物料编码")
  private String productCode;

  @ApiModelProperty("品牌")
  private String brand;

  @ApiModelProperty("名称")
  private String productName;

  @ApiModelProperty("物料行id")
  private Integer rowId;

  @ApiModelProperty("规格型号")
  private String manuCode;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("物料序号")
  private Integer productSort;

  @ApiModelProperty("关联发票号")
  private String purchaseOrderInvoiceRelationList;

  @ApiModelProperty("物料行 id ")
  private String sapRwoId;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("采购员")
  private String purchaseMan;

  @ApiModelProperty("采购部门")
  private String purchaseDept;

  @ApiModelProperty("未开票数量")
  private String unInvoiceNum;

  @ApiModelProperty("税率")
  private String taxRate;

  @ApiModelProperty(value = "仓库")
  private String warehouse = StrUtil.EMPTY;

  @ApiModelProperty(value = "订单类型")
  private String orderType;

  @ApiModelProperty(value = "采购部门编码")
  private String purchaseDeptCode;

  /** 发货单id */
  private String orderToFormId;

  @ApiModelProperty("含税金额：价税合计")
  private BigDecimal totalPriceAndTax;

  @ApiModelProperty("未税金额")
  private BigDecimal nakedTotalPrice;

  @ApiModelProperty("已开票金额")
  private BigDecimal invoicedAmount;

  @ApiModelProperty("未开票金额")
  private BigDecimal unInvoicedAmount;

  @ApiModelProperty("采购申请单号")
  private String purchaseApplyCode;

  @ApiModelProperty("项目编码")
  private String projectNo;

  @ApiModelProperty("业务员")
  private String salesman;

  @ApiModelProperty("销售订单号")
  private String saleOrderNo;

  /**
   * 可开票数量
   */
  @ApiModelProperty("可开票数量")
  private String invoiceAbleNum;

  /**
   * 结算单价
   */
  @ApiModelProperty("结算单价")
  private String settlementPrice;
  /**
   * 入库单号
   */
  @ApiModelProperty("入库单号")
  private String warehouseCode;
  @ApiModelProperty("审批状态")
  private String assessStatus;

  @ApiModelProperty("过账日期")
  private Long postDate;

  public WarehouseWarrantV2DTO(WarehousingV2DTO warehousingDTO, String purchaseOrderInvoiceRelationList){
    this.id = warehousingDTO.getId();
    this.code = warehousingDTO.getCode();
    this.warehousingTime = warehousingDTO.getCreateTime();
    this.source = warehousingDTO.getSource();
    this.logisticsCompany = warehousingDTO.getLogisticsCompany();
    this.trackNum = warehousingDTO.getTrackNum();
    this.productVoucherNo = warehousingDTO.getProductVoucherNo();
    this.reversal =
        SupplierOrderFormStatus.REVERSAL.getKey().equals(warehousingDTO.getFormStatus());
    if (SupplierOrderFormStatus.REVERSAL.getKey().equals(warehousingDTO.getFormStatus())) {
      this.reversalStatus = SupplierOrderFormStatus.REVERSAL.getDesc();
    } else if (SupplierOrderFormStatus.REVERSAL_IN_PROGRESS
        .getKey()
        .equals(warehousingDTO.getFormStatus())) {
      this.reversalStatus = SupplierOrderFormStatus.REVERSAL_IN_PROGRESS.getDesc();
    } else {
      this.reversalStatus = "未冲销";
    }
    this.productPrice =
        Optional.ofNullable(warehousingDTO.getProductPrice())
            .orElse(BigDecimal.ZERO)
            .stripTrailingZeros()
            .toPlainString();
    this.batchNumber = warehousingDTO.getBatchNo();
    this.stockInputQty =
        Optional.ofNullable(warehousingDTO.getStockInputQty())
            .orElse(BigDecimal.ZERO)
            .stripTrailingZeros()
            .toPlainString();
    this.stockOutputQty =
        Optional.ofNullable(warehousingDTO.getStockOutputQty())
            .orElse(BigDecimal.ZERO)
            .stripTrailingZeros()
            .toPlainString();
    this.invoiceNum =
        Optional.ofNullable(warehousingDTO.getInvoicedNum())
            .orElse(BigDecimal.ZERO)
            .stripTrailingZeros()
            .toPlainString();
    this.productCode = warehousingDTO.getProductCode();
    this.brand = warehousingDTO.getBrand();
    this.productName = warehousingDTO.getProductName();
    this.manuCode = warehousingDTO.getManuCode();
    this.unit = warehousingDTO.getUnit();
    this.sapProductVoucherLineItem = warehousingDTO.getSapRowId();
    this.productSort = warehousingDTO.getProductSort();
    this.sapRwoId = warehousingDTO.getDetailId();
    this.supplierName = warehousingDTO.getSupplierName();
    this.purchaseMan = warehousingDTO.getPurchaseMan();
    this.purchaseDept = warehousingDTO.getPurchaseDept();
    // 未开票数量：入库数量-退库数量-已开票数量
    this.unInvoiceNum =
        NumberUtil.sub(
                warehousingDTO.getStockInputQty(),
                warehousingDTO.getStockOutputQty(),
                warehousingDTO.getInvoicedNum())
            .max(BigDecimal.ZERO)
            .stripTrailingZeros()
            .toPlainString();
    this.taxRate =
        Optional.ofNullable(warehousingDTO.getTaxRate())
            .orElse(BigDecimal.ZERO)
            .stripTrailingZeros()
            .toPlainString();
    PurchaseOrderTypeEnum orderTypeEnum =
        PurchaseOrderTypeEnum.fromKey(warehousingDTO.getOrderType());
    this.orderType = orderTypeEnum == null ? StrUtil.EMPTY : orderTypeEnum.getValue();
    this.purchaseDeptCode = warehousingDTO.getPurchaseDeptCode();
    this.warehouse = ObjectUtil.defaultIfNull(warehousingDTO.getWarehouseName(), StrUtil.EMPTY);
    this.orderToFormId = warehousingDTO.getOrderToFormId();
    this.totalPriceAndTax = BigDecimalUtil.formatForStandard(warehousingDTO.getTotalPriceAndTax());
    this.invoicedAmount = BigDecimalUtil.formatForStandard(warehousingDTO.getInvoicedAmount());
    this.unInvoicedAmount = BigDecimalUtil.formatForStandard(warehousingDTO.getUnInvoicedAmount());
    this.purchaseApplyCode = warehousingDTO.getPurchaseApplyCode();
    this.projectNo = warehousingDTO.getProjectNo();
    this.salesman = warehousingDTO.getSalesman();
    this.saleOrderNo = warehousingDTO.getSaleOrderNo();
    //未税总额：去税单价*入库数量，物料明细上的去税单价*该行物料入库数量
    this.nakedTotalPrice = BigDecimalUtil.setScaleBigDecimalHalfUp(NumberUtil.mul(warehousingDTO.getStockInputQty(),
        getNakedPrice(warehousingDTO.getProductPrice(), warehousingDTO.getTaxRate())), 2);
    this.invoiceAbleNum =
        BigDecimalUtil.formatForStandard(warehousingDTO.getInvoiceAbleNum()).stripTrailingZeros().toPlainString();
    this.settlementPrice =
        BigDecimalUtil.formatForStandard(warehousingDTO.getSettlementPrice()).stripTrailingZeros().toPlainString();
    this.purchaseOrderInvoiceRelationList = purchaseOrderInvoiceRelationList;
    this.warehouseCode = warehousingDTO.getFormCode();
    this.assessStatus = SupplierOrderFormReviewStatus.getDescByCode(warehousingDTO.getAuditStatus());
    this.postDate = warehousingDTO.getPostingDate();
  }

  /**
   * @description: 获取去税单价
   */
  private BigDecimal getNakedPrice(BigDecimal price, BigDecimal taxRate) {
    if (price == null || taxRate == null) {
      return BigDecimal.ZERO;
    }
    return price.divide(BigDecimal.ONE.add(taxRate), 10, RoundingMode.HALF_UP);
  }
}
