package com.xhgj.srm.dto.product.externalLink;/**
 * @since 2025/1/7 15:49
 */

import lombok.Data;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/1/7 15:49:11
 *@description
 */
@Data
public class ExternalLinkSaveForm {

  private String id;


  /**
   *外部链接地址
   */
  private String externalLink;

  /**
   *外部链接类型
   * @see com.xhgj.srm.common.enums.product.ProductExternalLinkType
   */
  private String linkType;



  /**
   * 平台类型
   * @see com.xhgj.srm.common.enums.product.ProductExternalPlatform
   */
  private String platformType;

  /**
   * 店铺类型
   * @see com.xhgj.srm.common.enums.product.ProductExternalStore
   */
  private String storeType;

  /**
   * 外链数量，计价单位转换
   */
  private BigDecimal externalNum;

  /**
   * 咸亨数量，计价单位转换
   */
  private BigDecimal internalNum;

  /**
   * 参考价格
   */
  private BigDecimal externalPrice;

  /**
   * 佐证文件
   */
  private String externalFile;

  /**
   * 类型，0默认为外部链接 1为价格佐证
   * @see com.xhgj.srm.common.enums.product.ProductExternalType
   */
  private Byte type;

}
