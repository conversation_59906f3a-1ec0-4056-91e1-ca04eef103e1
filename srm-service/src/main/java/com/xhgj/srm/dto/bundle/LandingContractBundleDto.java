package com.xhgj.srm.dto.bundle;/**
 * @since 2024/12/4 10:13
 */

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.landingContract.BundleType;
import lombok.Data;
@Data
public class LandingContractBundleDto {

  /**
   * id
   */
  private String id;

  /**
   * 关联合同id
   */
  private String landingContractId;

  /**
   * 绑品类型 (0品牌绑定  1区域绑定  2客户单位绑定)
   * 详见 {@link com.xhgj.srm.common.enums.landingContract.BundleType}
   */
  private Byte bundleType;

  /**
   * 绑品类型 (0品牌绑定  1区域绑定  2客户单位绑定)
   * 详见 {@link com.xhgj.srm.common.enums.landingContract.BundleType}
   */
  private String bundleTypeStr;

  /**
   * 绑品值 (品牌值 区域值 客户单位值)
   */
  private String bundleValue;

  /**
   * 绑品区域(品牌区域 绑品区域)
   */
  private String bundleArea;

  /**
   * 绑品区域名称
   */
  private String bundleAreaName;

  /**
   * 绑品区域路径
   */
  private String bundleAreaPath;

  /**
   * 绑品品牌
   */
  private String bundleBrand;

  /**
   * 绑品品牌名称
   */
  private String bundleBrandName;

  /**
   * 绑品客户单位
   */
  private String bundleCustomer;

  /**
   * 绑品客户单位名称
   */
  private String bundleCustomerName;

  /**
   * 创建时间
   */
  private Long createTime;

  /**
   * 更新时间
   */
  private Long updateTime;

  /**
   * 创建人
   */
  private String createUser;

  /**
   * 更新人
   */
  private String updateUser;

  public String getBundleTypeStr() {
    if (bundleType == null) {
      return null;
    }
    BundleType bundleType = BundleType.getEnum(this.bundleType);
    return bundleType == null ? null : bundleType.getDesc();
  }

  public String getBundleValue() {
    StringBuilder bundleValue = new StringBuilder();
    if (StrUtil.isNotBlank(this.bundleBrand)) {
      bundleValue.append(this.bundleBrandName).append("    ");
    }
    if (StrUtil.isNotBlank(this.bundleArea)) {
      bundleValue.append(this.bundleAreaName).append("    ");
    }
    if (StrUtil.isNotBlank(this.bundleCustomer)) {
      bundleValue.append(this.bundleCustomerName).append("    ");
    }
    // 如果没有绑品值，设置为-
    if (bundleValue.length() == 0) {
      bundleValue.append("-");
    }
    // 去除末尾的所有空格
    return bundleValue.toString().trim();
  }
}
