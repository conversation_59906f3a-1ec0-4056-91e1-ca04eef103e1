package com.xhgj.srm.filter;

import org.apache.commons.lang3.StringUtils;
import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/23 10:57:34
 * @description 处理request 在Form表单添加请求参数   JSON中请求添加参数
 */
public class SrmHttpServletRequestFormWrapper extends HttpServletRequestWrapper {

    // 用于存储请求参数
    private Map<String , String[]> params = new HashMap<String, String[]>();

    private String queryString = new String();

    private  byte[] body; //用于保存读取body中数据

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request the {@link HttpServletRequest} to be wrapped.
     * @throws IllegalArgumentException if the request is null
     */
    public SrmHttpServletRequestFormWrapper(HttpServletRequest request) throws IOException {
        super(request);
        body = binaryReader(request);
        Map<String, String[]> parameterMap = request.getParameterMap();
        this.setParameterMap(request.getParameterMap());
        request.getQueryString();
        this.queryString = request.getQueryString();

    }

    byte[] binaryReader(HttpServletRequest request) throws IOException {
        InputStream inStream = request.getInputStream();
        BufferedReader reader = new BufferedReader(new InputStreamReader(inStream));
        try (StringWriter writer = new StringWriter()) {
            int read;
            char[] buf = new char[1024 * 8];
            while ((read = reader.read(buf)) != -1) {
                writer.write(buf, 0, read);
            }
            return writer.getBuffer().toString().getBytes();
        }
    }

    /**
     * 获取body中的数据
     * @return
     */
    public byte[] getBody() {
        return body;
    }

    /**
     * 把处理后的参数放到body里面
     * @param body
     */
    public void setBody(byte[] body) {
        this.body = body;
    }

    /**
     * 覆盖（重写）父类的方法
     * @return
     * @throws IOException
     */
    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream(), StandardCharsets.UTF_8));
    }

    /**
     * 覆盖（重写）父类的方法
     * @return
     * @throws IOException
     */
    @Override
    public ServletInputStream getInputStream() throws IOException {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(body);
        return new ServletInputStream() {

            @Override
            public int read() throws IOException {
                return byteArrayInputStream.read();
            }

            @Override
            public void setReadListener(ReadListener listener) {
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public boolean isFinished() {
                return false;
            }
        };
    }

    /**
     * 添加参数到map中
     * @param extraParams
     */
    public void setParameterMap(Map<String, String[]> extraParams) {
        for (String s : extraParams.keySet()) {
            this.setParameter(s, extraParams.get(s));
        }
    }

    @Override
    public String getQueryString() {
        return this.queryString;
    }

    /**
     * 添加参数到map中
     * @param name
     * @param value
     */
    public void setParameter(String name, Object value) {
        if (value != null) {
            if (value instanceof String[]) {
                params.put(name, (String[]) value);
            } else if (value instanceof String) {
                params.put(name, new String[]{(String) value});
            } else {
                params.put(name, new String[]{String.valueOf(value)});
            }
        }
    }

    /**
     * appendQueryString
     * @param name
     * @param value
     */
    public void appendQueryString(String name, Object value) {
        if (StringUtils.isEmpty(queryString)) {
            queryString = name + "=" + value;
        }else{
            queryString += "&" + name + "=" + value;
        }
    }



    /**
     * 重写getParameter，代表参数从当前类中的map获取
     * @param name
     * @return
     */
    @Override
    public String getParameter(String name) {
        String[]values = params.get(name);
        if(values == null || values.length == 0) {
            return null;
        }
        return values[0];
    }



    @Override
    public Enumeration<String> getParameterNames() {
        return Collections.enumeration(params.keySet());
    }



    /**
     * 重写getParameterValues方法，从当前类的 map中取值
     * @param name
     * @return
     */
    @Override
    public String[] getParameterValues(String name) {
        return params.get(name);
    }
}
