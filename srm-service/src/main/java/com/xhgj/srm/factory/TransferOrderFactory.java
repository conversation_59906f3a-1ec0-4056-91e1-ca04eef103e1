package com.xhgj.srm.factory;/**
 * @since 2025/2/25 16:37
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.common.enums.transferOrder.TransferOrderStatus;
import com.xhgj.srm.common.enums.transferOrder.TransferOrderType;
import com.xhgj.srm.common.utils.transferOrder.TransferOrderCleanerAndGenerator;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.TransferOrder;
import com.xhgj.srm.jpa.entity.TransferOrderItem;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.TransferOrderItemRepository;
import com.xhgj.srm.jpa.repository.TransferOrderRepository;
import com.xhgj.srm.request.dto.sap.SapInventoryQueryForm;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.request.vo.sap.SapInventoryVO;
import com.xhgj.srm.dto.transferOrder.TransferOrderSaveForm;
import com.xhgj.srm.dto.transferOrder.TransferOrderSaveForm.TransferOrderItemSaveForm;
import com.xhgj.srm.vo.transferOrder.TransferOrderItemVO;
import com.xhgj.srm.vo.transferOrder.TransferOrderVO;
import com.xhiot.boot.core.common.exception.CheckException;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/2/25 16:37:41
 *@description
 */
@Component
public class TransferOrderFactory {

  @Resource
  private TransferOrderRepository transferOrderRepository;
  @Resource
  private GroupRepository groupRepository;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private TransferOrderItemRepository transferOrderItemRepository;
  @Resource
  private SAPService sapService;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;



  /**
   * #check 校验寄售调拨单时，供应商id是否填写
   * @param saveForm
   */
  public void checkSupplier(TransferOrderSaveForm saveForm) {
    if (TransferOrderType.CONSIGNMENT.getCode().equals(saveForm.getType())) {
      if (StrUtil.isBlank(saveForm.getSupplierId())) {
        throw new CheckException("寄售调拨单供应商不能为空");
      }
    }
  }

  /**
   * #check 校验暂存时,原单状态
   */
  public void checkTemporary(TransferOrderSaveForm saveForm, TransferOrder origin) {
    if (saveForm.getSaveType() == 1) {
      // 驳回状态 和 暂存状态 可以暂存
      if (!TransferOrderStatus.REJECT.getCode().equals(origin.getStatus())
          && !TransferOrderStatus.TEMPORARY.getCode().equals(origin.getStatus())) {
        throw new CheckException("只有驳回状态和暂存状态的单据可以暂存");
      }
    }
  }

  /**
   * #check 校验调拨单物料库存
   */
  public void checkStock(List<TransferOrderItemSaveForm> items, TransferOrder transferOrder) {
    if (CollUtil.isEmpty(items)) {
      return;
    }
    String warehouseOut = items.get(0).getWarehouseOut();
    // 获取调出仓库
    InventoryLocation inventoryLocation = inventoryLocationRepository.findById(warehouseOut)
        .orElseThrow(() -> new CheckException("调出仓库不存在"));
    items.parallelStream().forEach(item -> {
      SapInventoryQueryForm form = new SapInventoryQueryForm();
      form.setUserGroup(transferOrder.getGroupCode());
      form.setProductCode(item.getProductCode());
      form.setWarehouse(inventoryLocation.getWarehouse());
      form.setBatchNo(item.getBatchNo());
      List<SapInventoryVO> res = sapService.sapInventoryQuery(form);
      if (CollUtil.isEmpty(res)) {
        throw new CheckException("物料库存不存在");
      }
      // 获取第一条记录
      SapInventoryVO sapInventoryVO = res.get(0);
      // 标准时 --校验调拨数量小于等于 库存总数（不含寄售数量）
      // 寄售时 -- 校验调拨数量小于等于 寄售库存
      if (TransferOrderType.STANDARD.getCode().equals(item.getStockAttr())) {
        if (item.getNum().compareTo(sapInventoryVO.getTotalStock()) > 0) {
          throw new CheckException("物料库存不足");
        }
      } else if (TransferOrderType.CONSIGNMENT.getCode().equals(item.getStockAttr())) {
        if (item.getNum().compareTo(sapInventoryVO.getConsignmentStock()) > 0) {
          throw new CheckException("物料库存不足");
        }
      }
    });
  }

  /**
   * #check 成品库 调入 直销库 判断收件信息是否填写
   * @param locationOut
   * @param locationIn
   * @param saveForm
   */
  public void checkReceiveInfo(InventoryLocation locationOut, InventoryLocation locationIn, TransferOrderSaveForm saveForm) {
    String warehouseInCode = locationIn.getWarehouse();
    String warehouseOutCode = locationOut.getWarehouse();
    // 成品库 调入 直销库
    boolean isFinished =
        WarehouseEnum.FINISHED_PRODUCTS.getCode().equals(warehouseOutCode) || WarehouseEnum.HAI_NING_FINISHED_PRODUCTS.getCode().equals(warehouseInCode);
    boolean isDirect = WarehouseEnum.HAI_NING_DIRECT_SALES.getCode().equals(warehouseInCode);
    if (isFinished && isDirect) {
      if (StrUtil.isBlank(saveForm.getConsignee())) {
        throw new CheckException("收件人不能为空");
      }
      if (StrUtil.isBlank(saveForm.getReceiveMobile())) {
        throw new CheckException("收件人电话不能为空");
      }
      if (StrUtil.isBlank(saveForm.getReceiveAddress())) {
        throw new CheckException("收件人地址不能为空");
      }
    }
  }

  /**
   * #check 校验调拨单物料中库位id每行是否一致
   * @param saveForm
   */
  public void checkItemsWarehouse(TransferOrderSaveForm saveForm) {
    List<TransferOrderItemSaveForm> items = saveForm.getItems();
    if (CollUtil.isNotEmpty(items)) {
      return;
    }
    String warehouseOut = items.get(0).getWarehouseOut();
    String warehouseIn = items.get(0).getWarehouseIn();
    // 比对每行是否一致
    for (TransferOrderItemSaveForm item : items) {
      if (!StrUtil.equals(warehouseOut, item.getWarehouseOut())
          || !StrUtil.equals(warehouseIn, item.getWarehouseIn())) {
        throw new CheckException("调拨单物料中库位数据不一致");
      }
    }
  }

  /**
   * 创建调拨单
   * @param saveForm
   * @return
   */
  public TransferOrder createTransferOrder(TransferOrderSaveForm saveForm) {
    // 查询group
    Group group =
        groupRepository.findFirstByErpCodeAndState(saveForm.getGroupCode(), Constants.STATE_OK);
    if (group == null) {
      throw new CheckException("创建组织不存在");
    }
    // 查询supplier
    Supplier supplier = null;
    if (StrUtil.isNotBlank(saveForm.getSupplierId())) {
      supplier = supplierRepository.findById(saveForm.getSupplierId()).orElseThrow(() -> new CheckException("供应商不存在"));
    }
    // 查询部门
    Group purchaseDept = groupRepository.findById(saveForm.getDeptId())
        .orElseThrow(() -> new CheckException("创建部门不存在"));
    TransferOrder transferOrder = null;
    if (StrUtil.isBlank(saveForm.getId())) {
      transferOrder = new TransferOrder();
      transferOrder.setCode(TransferOrderCleanerAndGenerator.INSTANCE.generate());
      transferOrder.setCreateTime(System.currentTimeMillis());
      transferOrder.setUpdateTime(System.currentTimeMillis());
      transferOrder.setState(Constants.STATE_OK);
      transferOrder.setCreateMan(saveForm.getCreateMan());
      transferOrder.setCreateManCode(saveForm.getCreateManCode());
      transferOrder.setCreateManName(saveForm.getCreateManName());
      // 新增
    } else {
      // 修改
      transferOrder = transferOrderRepository.findById(saveForm.getId())
          .orElseThrow(() -> new CheckException("调拨单不存在"));
      transferOrder.setUpdateTime(System.currentTimeMillis());
      // #check 是否可以暂存
      this.checkTemporary(saveForm, transferOrder);
    }
    transferOrder.setType(saveForm.getType());
    transferOrder.setGroupCode(group.getCode());
    transferOrder.setGroupName(group.getName());
    transferOrder.setDeptId(purchaseDept.getId());
    transferOrder.setDeptCode(purchaseDept.getErpCode());
    transferOrder.setDeptName(purchaseDept.getName());
    transferOrder.setStatus(TransferOrderStatus.AUDITING.getCode());
    transferOrder.setReason(saveForm.getReason());
    transferOrder.setConsignee(saveForm.getConsignee());
    transferOrder.setReceiveMobile(saveForm.getReceiveMobile());
    transferOrder.setReceiveAddress(saveForm.getReceiveAddress());
    transferOrder.setSupplierId(saveForm.getSupplierId());
    transferOrder.setSupplierName(supplier != null ? supplier.getEnterpriseName() : null);
    if (saveForm.getSaveType() == 1) {
      transferOrder.setStatus(TransferOrderStatus.TEMPORARY.getCode());
    }
    // 订单状态为审核中时清空审核人等信息
    if (TransferOrderStatus.AUDITING.getCode().equals(transferOrder.getStatus())) {
      transferOrder.setReviewId(null);
      transferOrder.setReviewer(null);
      transferOrder.setReviewTime(null);
    }
    // 设置库位的业务类型
    if (CollUtil.isNotEmpty(saveForm.getItems())) {
      TransferOrderItemSaveForm transferOrderItemSaveForm = saveForm.getItems().get(0);
      InventoryLocation locationOut =
          inventoryLocationRepository.findById(transferOrderItemSaveForm.getWarehouseOut())
              .orElseThrow(() -> new CheckException("调出仓库不存在"));
      InventoryLocation locationIn =
          inventoryLocationRepository.findById(transferOrderItemSaveForm.getWarehouseIn())
              .orElseThrow(() -> new CheckException("调入仓库不存在"));
      // #check 成品库 调入 直销库 判断收件信息是否填写
      this.checkReceiveInfo(locationOut, locationIn, saveForm);
      String businessType1 = locationOut.getBusinessType();
      String businessType2 = locationIn.getBusinessType();
      Set<String> businessTypes = new HashSet<>();
      if (StrUtil.isNotBlank(businessType1)) {
        businessTypes.addAll(CollUtil.toList(businessType1.split(",")));
      }
      if (StrUtil.isNotBlank(businessType2)) {
        businessTypes.addAll(CollUtil.toList(businessType2.split(",")));
      }
      transferOrder.setBusinessType(StrUtil.join(",", businessTypes));
    }
    return transferOrder;
  }

  public List<TransferOrderItem> createTransferOrderItems(TransferOrderSaveForm saveForm, TransferOrder transferOrder) {
    List<TransferOrderItem> res = new ArrayList<>();
    List<TransferOrderItemSaveForm> items = saveForm.getItems();
    List<TransferOrderItem> origins =
        transferOrderItemRepository.findAllByTransferOrderIdAndState(transferOrder.getId(),
            Constants.STATE_OK);
    Map<String, TransferOrderItem> originMap = origins.stream()
        .collect(Collectors.toMap(TransferOrderItem::getId, item -> item));
    // patch更新
    // 过滤出新增的
    List<TransferOrderItemSaveForm> addItems = items.stream().filter(item -> StrUtil.isBlank(item.getId()))
        .collect(Collectors.toList());
    // 过滤出更新的
    List<TransferOrderItemSaveForm> updateItems = items.stream().filter(item -> StrUtil.isNotBlank(item.getId()))
        .collect(Collectors.toList());
    // 过滤出删除的
    List<TransferOrderItem> deleteItems = origins.stream()
        .filter(origin -> items.stream().noneMatch(item -> StrUtil.equals(origin.getId(), item.getId())))
        .collect(Collectors.toList());
    // 处理新增
    if (CollUtil.isNotEmpty(addItems)) {
      List<TransferOrderItem> addNewOnes = addItems.stream().map(item -> {
        TransferOrderItem transferOrderItem = MapStructFactory.INSTANCE.toTransferOrderItem(item);
        transferOrderItem.setCreateTime(System.currentTimeMillis());
        transferOrderItem.setUpdateTime(System.currentTimeMillis());
        transferOrderItem.setState(Constants.STATE_OK);
        transferOrderItem.setTransferOrderId(transferOrder.getId());
        return transferOrderItem;
      }).collect(Collectors.toList());
      res.addAll(addNewOnes);
    }
    // 处理更新
    if (CollUtil.isNotEmpty(updateItems)) {
      List<TransferOrderItem> updateOnes = updateItems.stream().map(item -> {
        TransferOrderItem origin = originMap.get(item.getId());
        if (origin == null) {
          throw new CheckException("调拨单物料不存在");
        }
        TransferOrderItem transferOrderItem = MapStructFactory.INSTANCE.toTransferOrderItem(item);
        transferOrderItem.setCreateTime(origin.getCreateTime());
        transferOrderItem.setUpdateTime(System.currentTimeMillis());
        transferOrderItem.setState(origin.getState());
        transferOrderItem.setTransferOrderId(transferOrder.getId());
        return transferOrderItem;
      }).collect(Collectors.toList());
      res.addAll(updateOnes);
    }
    // #check 校验库存数量
    this.checkStock(items, transferOrder);
    // 处理删除
    if (CollUtil.isNotEmpty(deleteItems)) {
      deleteItems.forEach(item -> {
        item.setState(Constants.STATE_DELETE);
        item.setUpdateTime(System.currentTimeMillis());
      });
      res.addAll(deleteItems);
    }
    return res;
  }

  public TransferOrderVO buildVO(TransferOrder transferOrder, List<TransferOrderItem> transferOrderItems) {
    TransferOrderVO transferOrderVO = MapStructFactory.INSTANCE.toTransferOrderVO(transferOrder);
    if (CollUtil.isEmpty(transferOrderItems)) {
      return transferOrderVO;
    }
    // 获取第一条的调出仓库和调入仓库
    TransferOrderItem transferOrderItem = transferOrderItems.get(0);
    InventoryLocation locationOut =
        inventoryLocationRepository.findById(transferOrderItem.getWarehouseOut())
            .orElseThrow(() -> new CheckException("调出仓库不存在"));
    InventoryLocation locationIn =
        inventoryLocationRepository.findById(transferOrderItem.getWarehouseIn())
            .orElseThrow(() -> new CheckException("调入仓库不存在"));
    List<TransferOrderItemVO> transferOrderItemVOS = transferOrderItems.stream().map(item -> {
      TransferOrderItemVO itemVO = MapStructFactory.INSTANCE.toTransferOrderItemVO(item);
      itemVO.setWarehouseOutName(locationOut.getWarehouseName());
      itemVO.setWarehouseOutCode(locationOut.getWarehouse());
      itemVO.setWarehouseInName(locationIn.getWarehouseName());
      itemVO.setWarehouseInCode(locationIn.getWarehouse());
      return itemVO;
    }).collect(Collectors.toList());
    transferOrderVO.setItems(transferOrderItemVOS);
    // 供应商主数据编码
    if (StrUtil.isNotBlank(transferOrderVO.getSupplierId())) {
      Supplier supplier = supplierRepository.findById(transferOrderVO.getSupplierId())
          .orElseThrow(() -> new CheckException("供应商不存在"));
      transferOrderVO.setSupplierCode(supplier.getMdmCode());
    }
    transferOrderVO.setHasMovement(transferOrder.hasMovement());
    return transferOrderVO;
  }
}
