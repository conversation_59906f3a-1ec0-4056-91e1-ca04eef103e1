package com.xhgj.srm.common.enums.supplierUser;/**
 * @since 2024/12/4 17:30
 */

/**
 *<AUTHOR>
 *@date 2024/12/4 17:30:30
 *@description 供应商账号模块变更日志枚举
 */
public enum SupplierChangeLogEnum {
  // 类型(0 折扣比例修改、1电商供应商权限修改、2平台生效状态修改、3供应商权限修改、4供应商接单时限修改)'
  DISCOUNT_RATIO((byte) 0, "折扣比例修改", "当前折扣比例"),
  E_COMMERCE_SUPPLIER_PERMISSION((byte) 1, "电商供应商权限修改", "电商供应商权限"),
  PLATFORM_EFFECTIVE_STATUS((byte) 2, "平台生效状态修改", "平台生效状态"),
  SUPPLIER_PERMISSION((byte) 3, "供应商权限修改", "供应商权限"),
  SUPPLIER_ORDER_TIME_LIMIT((byte) 4, "供应商接单时限修改", "供应商接单时限天")
  ;

  private Byte type;
  private String desc;
  private String updateField;

  SupplierChangeLogEnum(Byte type, String desc, String updateField) {
    this.type = type;
    this.updateField = updateField;
    this.desc = desc;
  }

  public static SupplierChangeLogEnum getEnum(Byte type) {
    for (SupplierChangeLogEnum value : SupplierChangeLogEnum.values()) {
      if (value.getType().equals(type)) {
        return value;
      }
    }
    return null;
  }

  public Byte getType() {
    return type;
  }

  public String getDesc() {
    return desc;
  }

  public String getUpdateField() {
    return updateField;
  }
}
