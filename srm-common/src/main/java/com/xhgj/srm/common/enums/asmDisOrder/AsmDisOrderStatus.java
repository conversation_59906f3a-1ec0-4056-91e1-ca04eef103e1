package com.xhgj.srm.common.enums.asmDisOrder;/**
 * @since 2025/2/25 11:14
 */

/**
 *<AUTHOR>
 *@date 2025/2/25 11:14:35
 *@description
 */
public enum AsmDisOrderStatus {
  /**
   * 暂存
   */
  TEMPORARY((byte) 1, "暂存"),
  /**
   * 审核中
   */
  AUDITING((byte) 2, "审核中"),
  /**
   * 驳回
   */
  REJECT((byte) -1, "驳回"),
  /**
   * 待仓库执行
   */
  WAITING_WAREHOUSE((byte) 3, "待仓库执行"),
  /**
   * 已完成
   */
  FINISHED((byte) 4, "已完成"),
  ;

  private Byte code;

  private String name;

  AsmDisOrderStatus(Byte code, String name) {
    this.code = code;
    this.name = name;
  }

  public static String getNameByCode(Byte code) {
    for (AsmDisOrderStatus value : AsmDisOrderStatus.values()) {
      if (value.getCode().equals(code)) {
        return value.getName();
      }
    }
    return null;
  }

  public Byte getCode() {
    return code;
  }

  public String getName() {
    return name;
  }
}
