package com.xhgj.srm.common.enums.order;/**
 * @since 2025/3/19 16:36
 */

/**
 *<AUTHOR>
 *@date 2025/3/19 16:36:05
 *@description
 */
public enum OrderCancelStatus {
  // 取消失败
  CANCEL_FAIL((byte)1, "取消失败"),
  // 取消完成
  CANCEL_COMPLETE((byte)2, "取消完成"),
  ;

  /**
   * 编码
   */
  private Byte code;

  /**
   * 描述
   */
  private String desc;

  OrderCancelStatus(Byte code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  public Byte getCode() {
    return code;
  }

  public String getDesc() {
    return desc;
  }

  public static String getDescByCode(Byte code) {
    for (OrderCancelStatus status : OrderCancelStatus.values()) {
      if (status.getCode().equals(code)) {
        return status.getDesc();
      }
    }
    return null;
  }
}
