package com.xhgj.srm.common.dto.invoice;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/9/10
 */
@Data
public final class BatchUpdateSupplierInvoiceParam {

  @NotEmpty(message = "参数异常")
  private List<UpdateSupplierInvoiceParam> params;

  @Data
  public static class UpdateSupplierInvoiceParam {
    @ApiModelProperty("发票id")
    private String invoiceId;
    @ApiModelProperty("验真类型")
    private String verificationType;
  }
}
