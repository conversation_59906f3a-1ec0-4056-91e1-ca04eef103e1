package com.xhgj.srm.common.enums;

import com.xhiot.boot.core.common.util.dict.BootDictEnum;

public enum SupplierBlockRangeEnum implements BootDictEnum<String, String> {

  PROHIBIT_ACCOUNTING("1", "不允许做账"), PROHIBIT_MAKING_ORDERS("2", "不允许做单"),
  PROHIBIT_ACCOUNTING_AND_ORDER("3", "不允许做账和做单");

  private final String type;

  /**
   * 描述
   */
  private final String desc;

  SupplierBlockRangeEnum(String type, String desc) {
    this.type = type;
    this.desc = desc;
  }

  public static SupplierBlockRangeEnum fromKey(String key) {
    for (SupplierBlockRangeEnum provision : SupplierBlockRangeEnum.values()) {
      if (provision.getKey().equals(key)) {
        return provision;
      }
    }
    return null;
  }

  @Override
  public String getKey() {
    return type;
  }

  @Override
  public String getValue() {
    return desc;
  }
}
