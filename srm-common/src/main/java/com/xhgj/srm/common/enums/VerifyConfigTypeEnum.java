package com.xhgj.srm.common.enums;

import lombok.Getter;

/**
 * @Author: fanghuanxu
 * @Date: 2025/3/20 13:18
 * @Description: 配置中心-配置类型枚举
 */
@Getter
public enum VerifyConfigTypeEnum {


  SUPPLIER_ORDER_SUBMIT("1", "国内潜在供应商做单校验","采购订单提交校验配置"),

  SUPPLIER_ORDER_SUBMIT_2("2", "国内供应商做单校验2","采购订单提交校验配置"),
  /**
   * 工作台2.0 可用组织配置
   */
  WORKBENCH_TWO_ZERO_AVAILABLE_ORG("3", "SRM2.0可用组织配置","工作台2.0可用组织配置"),
  /**
   * 2.0 采购申请配置
   */
  PURCHASE_TWO_ZERO_APPLY_UPDATE_CONFIG("4", "2.0采购申请修改配置"," 采购申请修改配置"),
  /**
   * 2.0 采购申请导出配置
   */
  PURCHASE_TWO_ZERO_APPLY_EXPORT_CONFIG("5", "2.0采购申请导出配置","采购申请导出配置");




  private final String code;

  private final String name;

  private final String type;
  VerifyConfigTypeEnum(String code, String name,String type) {
    this.code = code;
    this.name = name;
    this.type = type;
  }


  public static String getNameByCode(String code) {
    for (VerifyConfigTypeEnum type : VerifyConfigTypeEnum.values()) {
      if (type.getCode().equals(code)) {
        return type.getName();
      }
    }
    return null;
  }
}
