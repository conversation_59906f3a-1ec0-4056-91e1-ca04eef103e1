package com.xhgj.srm.common.enums.purchase.order;

public enum PurchaseApplyOperationPermissionsEnum {

  /**
   * 采购员，key为 "PURCHASER"
   */
  PURCHASER("1", "为采购员"),
  /**
   * 所在部门，key为 "DEPARTMENT"
   */
  DEPARTMENT("2", "所在部门"),
  /**
   * 所在部门及下级部门，key为 "DEPARTMENT_AND_SUBORDINATES"
   */
  DEPARTMENT_AND_SUBORDINATES("3", "所在部门及下级部门"),
  /**
   * 所在组织，key为 "ORGANIZATION"
   */
  ORGANIZATION("4", "所在组织"),
  NOT_ALLOW("5", "不允许操作");

  private String key;
  private String description;

  PurchaseApplyOperationPermissionsEnum(String key, String description) {
    this.key = key;
    this.description = description;
  }

  public String getKey() {
    return key;
  }

  public String getDescription() {
    return description;
  }

  public static PurchaseApplyOperationPermissionsEnum fromKey(String key) {
    for (PurchaseApplyOperationPermissionsEnum value : values()) {
      if (value.getKey().equals(key)) {
        return value;
      }
    }
    return null;
  }
}
