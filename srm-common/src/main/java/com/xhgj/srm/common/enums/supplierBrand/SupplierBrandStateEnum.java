package com.xhgj.srm.common.enums.supplierBrand;

/**
 * <AUTHOR>
 * 供应商品牌状态枚举
 */
public enum SupplierBrandStateEnum {
  /** 审核状态 1-审核中 **/
  AUDITING("1", "审核中"),
  /** 审核状态 2-审核驳回 **/
  REJECTED("2", "审核驳回"),
  /** 审核状态 3-审核通过 **/
  PASSED("3", "审核通过"),
  /** 审核状态 -1-已放弃 **/
  GIVE_UP("-1", "已放弃");

  /**
   * 供应商品牌状态类型
   */
  private final String type;

  /**
   * 供应商品牌状态描述
   */
  private final String desc;

  SupplierBrandStateEnum(String type, String desc) {
    this.type = type;
    this.desc = desc;
  }

  public String getType() {
    return type;
  }

  public String getDesc() {
    return desc;
  }
}
