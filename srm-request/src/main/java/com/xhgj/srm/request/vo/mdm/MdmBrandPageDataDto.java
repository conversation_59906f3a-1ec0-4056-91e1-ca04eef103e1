package com.xhgj.srm.request.vo.mdm;/**
 * @since 2024/11/29 9:43
 */

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *<AUTHOR>
 *@date 2024/11/29 09:43:01
 *@description
 */
@Data
public class MdmBrandPageDataDto {
  @ApiModelProperty("品牌id")
  @JSONField(name = "mdmId")
  private String brandMdmId;

  @ApiModelProperty("品牌英文名")
  @JSONField(name = "enName")
  private String brandNameEn;

  @ApiModelProperty("品牌中文名")
  @JSONField(name = "cnName")
  private String brandNameCn;

  @ApiModelProperty("品牌名-中文/英文")
  @JSONField(name = "fullName")
  private String fullName;

  @ApiModelProperty("品牌logo")
  @JSONField(name = "logoUrl")
  private String logoUrl;

  @ApiModelProperty("是否可选")
  private boolean isChoose;

  @ApiModelProperty("品牌编码")
  @JSONField(name = "brandCode")
  private String brandCode;
}
