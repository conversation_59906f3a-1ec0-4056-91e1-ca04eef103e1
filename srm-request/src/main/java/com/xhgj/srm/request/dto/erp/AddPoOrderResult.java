package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-06-06 17:04
 */
@NoArgsConstructor
@Data
public class AddPoOrderResult {

  @JSONField(name = "Result")
  private ResultDTO result;

  @NoArgsConstructor
  @Data
  public static class ResultDTO {

    @JSONField(name = "ResponseStatus")
    private ResponseStatusDTO responseStatus;

    @NoArgsConstructor
    @Data
    public static class ResponseStatusDTO {

      @JSONField(name = "IsSuccess")
      private Boolean isSuccess;
      @JSONField(name = "Errors")
      private List<ErrorsDTO> errors;
      @JSONField(name = "SuccessEntitys")
      private List<SuccessEntitysDTO> successEntitys;
      @JSONField(name = "SuccessMessages")
      private List<Object> successMessages;
      @JSONField(name = "MsgCode")
      private Integer msgCode;

      @NoArgsConstructor
      @Data
      public static class ErrorsDTO {

        @JSONField(name = "FieldName")
        private Object fieldName;
        @JSONField(name = "Message")
        private String message;
        @JSONField(name = "DIndex")
        private Integer dIndex;
      }

      @NoArgsConstructor
      @Data
      public static class SuccessEntitysDTO {

        @JSONField(name = "Id")
        private String id;
        @JSONField(name = "Number")
        private String number;
        @JSONField(name = "DIndex")
        private String dIndex;
        @JSONField(name = "FPOOrderEntry")
        private List<FPOOrderEntryDTO> fPOOrderEntry;

        @NoArgsConstructor
        @Data
        public static class FPOOrderEntryDTO {

          @JSONField(name = "FEntryId")
          private String fEntryId;
          @JSONField(name = "FNumber")
          private String fNumber;
        }
      }
    }
  }
}
