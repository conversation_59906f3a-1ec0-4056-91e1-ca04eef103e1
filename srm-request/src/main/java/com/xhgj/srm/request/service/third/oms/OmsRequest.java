package com.xhgj.srm.request.service.third.oms;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.utils.RequestUtil;
import com.xhgj.srm.request.dto.mpm.UnitResult;
import com.xhgj.srm.request.dto.oms.PurchaseApplyDetailResult;
import com.xhgj.srm.request.dto.oms.SalesOrderListDTO;
import com.xhgj.srm.request.service.third.api.OMSApiPlatformApi;
import com.xhgj.srm.request.service.third.xhgj.XhgjMPMRequest;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023-03-07 13:11
 */
@Component
@Slf4j
public class OmsRequest {

  @Autowired
  private SrmConfig srmConfig;
  @Resource
  private XhgjMPMRequest xhgjMPMRequest;
  @Resource
  private OMSApiPlatformApi omsApiPlatformApi;

  /**
   * 通过物流公司名称获取 ERP 承运商编码
   */
  private static final String GET_ERP_EXPRESS_CODE_BY_NAME = "/oms/admin/expressManage/getErpExpressCodeByName";
  private static final String GET_PURCHASE_APPLY_DETAIL = "/oms-api-platform/inter/order"
      + "/getPurchaseApplyDetail";
  /**
   * oms订单详情分页查询接口
   */
  private static final String GET_SALES_ORDER_DETAIL_PAGE = "/oms-api-platform/inter/salesOrder"
      + "/getSalesOrderDetailList";

  /**
   * oms订单详情分页查询接口
   * @param salesOrgCode 销售组织
   * @param salesOrderNo 销售订单号
   * @param projectNo 大票项目号
   */
  public PageResult<SalesOrderListDTO> getSalesOrderDetail(String salesOrgCode, String salesOrderNo,
                                                           String projectNo, int pageNo, int pageSize) {
    ForestResponse<ResultBean<PageResult<SalesOrderListDTO>>> salesOrderDetailList =
        omsApiPlatformApi.getSalesOrderDetailList(pageNo, pageSize, projectNo, salesOrderNo,
            salesOrgCode);
    ResultBean<PageResult<SalesOrderListDTO>> result = salesOrderDetailList.getResult();
    if (result.getCode() != ResultBean.SUCCESS) {
      throw new CheckException("调用【oms订单详情分页查询接口】异常：" + result.getMsg());
    }
    for (SalesOrderListDTO salesOrderListDTO : result.getData().getContent()) {
      if (StrUtil.isNotBlank(salesOrderListDTO.getProductUnit())) {
        UnitResult unitByCodeOrName =
            xhgjMPMRequest.findUnitByCodeOrName(null, salesOrderListDTO.getProductUnit());
        salesOrderListDTO.setProductUnitCode(unitByCodeOrName.getUnit());
      }
    }
    return result.getData();
    //    String url = srmConfig.getOmsPlatformUrl() + GET_SALES_ORDER_DETAIL_PAGE;
//    Map<String, Object> param = MapUtil.of(new Pair<String, Object>("salesOrgCode", salesOrgCode),
//        new Pair<String, Object>("orderNo", salesOrderNo),
//        new Pair<String, Object>("projectNo", projectNo),
//        new Pair<String, Object>("pageNo", pageNo), new Pair<String, Object>("pageSize", pageSize));
//    try {
//      String result = HttpUtil.get(url, param);
//      PageResult<SalesOrderListDTO> pageResult =
//          RequestUtil.extractData(JSON.parseObject(result),
//              new TypeReference<PageResult<SalesOrderListDTO>>() {});
//      for (SalesOrderListDTO salesOrderListDTO : pageResult.getContent()) {
//        if (StrUtil.isNotBlank(salesOrderListDTO.getProductUnit())) {
//          UnitResult unitByCodeOrName =
//              xhgjMPMRequest.findUnitByCodeOrName(null, salesOrderListDTO.getProductUnit());
//          salesOrderListDTO.setProductUnitCode(unitByCodeOrName.getUnit());
//        }
//      }
//      return pageResult;
//    } catch (RuntimeException e) {
//      log.info("调用【oms订单详情分页查询接口】入参：" + JSON.toJSONString(param));
//      log.info("调用【oms订单详情分页查询接口】异常：" + e);
//      throw new RuntimeException("调用【oms订单详情分页查询接口】异常", e);
//    }
  }
  /**
   * 通过物流公司名称获取 ERP 承运商编码
   * @param name 物流公司名称 必传
   */
  public String getErpExpressCodeByName(String name){
    Assert.notBlank(name);
    String urlString = buildFullUrl(GET_ERP_EXPRESS_CODE_BY_NAME);
    log.info("调用【通过物流公司名称获取 ERP 承运商编码】请求路径【" + urlString +"】入参："+ name);
    String result = HttpUtil.get(urlString, MapUtil.of("name",name));
    log.info("调用【通过物流公司名称获取 ERP 承运商编码】出参：" + name);
    return resolveResult(
        result,new TypeReference<ResultBean<String>>(){}).orElse(StrUtil.EMPTY);
  }

  /**
   * 查询采购申请单信息
   * @param applyNo 申请单号
   */
  public PurchaseApplyDetailResult getPurchaseApplyDetail(String applyNo) {
    String url = srmConfig.getOmsPlatformUrl() + GET_PURCHASE_APPLY_DETAIL;
    log.info("调用【获取采购申请详情】请求路径【" + url +"】入参："+ applyNo);
    try {
      String result = HttpUtil.get(url, MapUtil.of("applyNo", applyNo));
      log.info("调用【获取采购申请详情】出参：" + result);
      PurchaseApplyDetailResult purchaseApplyDetailResult =
          JSON.parseObject(result, new TypeReference<PurchaseApplyDetailResult>() {});
      return purchaseApplyDetailResult;
    } catch (Exception e) {
      log.info("调用【获取采购申请详情】异常：" + e);
      throw new RuntimeException("调用【获取采购申请详情】异常", e);
    }
  }




  /**
   * 构建完整接口路径
   *
   * @param url 接口路径（半路径）
   */
   String buildFullUrl(String url) {
    String serviceUrl = srmConfig.getOmsAdminUrl();
    if (StrUtil.isBlank(serviceUrl)) {
      throw new CheckException("OMS-ADMIN 地址未配置，请联系管理员处理！");
    }
    return StrUtil.removeSuffix(serviceUrl, "/") + StrUtil.addPrefixIfNot(url, "/");
  }

  /**
   * 解析 MDM 接口响应
   *
   * @param json json 字符串
   */
  <T> Optional<T> resolveResult(String json, TypeReference<ResultBean<T>> typeReference) {
    try {
      ResultBean<T> resultBean = JSON.parseObject(json, typeReference);
      if (resultBean.getCode() == ResultBean.SUCCESS) {
        return Optional.ofNullable(resultBean.getData());
      } else {
        throw new CheckException("【OMS-ADMIN】接口提示：" + resultBean.getMsg());
      }
    } catch (Exception e) {
      if (e instanceof CheckException) {
        throw e;
      } else {
        log.error(ExceptionUtil.stacktraceToString(e, -1));
        throw new CheckException("解析【OMS-ADMIN】接口响应异常：" + e);
      }
    }
  }
}
