package com.xhgj.srm.request.service.third.api;

import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;
import com.dtflys.forest.annotation.Retry;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.common.dto.OrderLargeTicketProjectNo;
import com.xhgj.srm.common.dto.PaymentStatus;
import com.xhgj.srm.common.dto.PlatformLargeTicketProjectDTO;
import com.xhgj.srm.request.dto.oms.OMSCustomerFilingSheetAddParam;
import com.xhgj.srm.request.dto.oms.OMSFilingSheetAddParam;
import com.xhgj.srm.request.dto.oms.PlatformLargeTicketParam;
import com.xhgj.srm.request.dto.supplierRate.SupplierRateBatchAddParam;
import com.xhgj.srm.request.service.third.api.interceptor.OMSPlatformInterceptor;
import com.xhgj.srm.request.vo.BaseXhgjRes;
import com.xhgj.srm.request.vo.SupplierRateDetailVO;
import com.xhiot.boot.forest.annotation.BaseRequest;
import com.xhiot.boot.mvc.base.ResultBean;
import java.util.List;


@BaseRequest(interceptor = OMSPlatformInterceptor.class)
public interface OMSPlatformApi {
  /**
   * 履约报备单新增(商品报备单）
   */
  @Post( url = "/dock/SrmOrder/filingSheetAdd",
      contentType = "application/json",
      dataType = "json",
      headers = {"Api-Name: 报备单派单至履约"}
  )
  ForestResponse<BaseXhgjRes<String>> filingSheetAdd(@Body OMSFilingSheetAddParam form);
  /**
   * 履约供应商折扣比例新增
   */
  @Post( url = "/dock/SrmOrder/customerFilingSheetAdd",
      contentType = "application/json",
      dataType = "json",
      headers = {"Api-Name: 客户订单号报备单派单至履约"}

  )
  ForestResponse<BaseXhgjRes<String>> customerFilingSheetAdd(@Body OMSCustomerFilingSheetAddParam body);

  /**
   * 履约同步供应商折扣比例
   */
  @Post( url = "/supplier/batchSetOrderRate",
      contentType = "application/json",
      dataType = "json",
      headers = {"Api-Name: 履约同步供应商折扣比例"}
  )
  ForestResponse<BaseXhgjRes<String>> supplierRateAdd(@Body SupplierRateBatchAddParam body);
  /**
   * 获取履约供应商比例
   */
  @Get( url = "/supplier/getSupplierRate",
      contentType = "application/json",
      dataType = "json",
      headers = {"Api-Name: 获取履约供应商比例"}
  )
  ForestResponse<BaseXhgjRes<String>> getSupplierRate(@Query("supplierId") String supplierId,
      @Query("price") String price, @Query("platform") String platform);
  /**
   * 获取履约供应商比例详情
   */
  @Get( url = "/supplier/getSupplierRateDetail",
      contentType = "application/json",
      dataType = "json",
      headers = {"Api-Name: 获取履约供应商比例详情"}
  )
  @Retry(maxRetryCount = "0")
  ForestResponse<BaseXhgjRes<SupplierRateDetailVO>> getSupplierRateDetail(@Query("id") String id, @Query("platform") String platform);

  /**
   * 通过订单编号获取订单的大票
   */
  @Get( url = "/order/getOrderLargeTicketProjectNo",
      contentType = "application/json",
      dataType = "json",
      headers = {"Api-Name: 通过订单编号获取订单的大票"}
  )
  ForestResponse<ResultBean<List<OrderLargeTicketProjectNo>>> getOrderLargeTicketProjectNo(
      @Query("dockingOrderNo") String orderNo,
      @Query("dockingOrderType") String dockingOrderType);

  /**
   * 从履约平台查询订单回款状态信息
   * @param orderNo
   * @param platformCode
   * @param supplierOrderId
   * @return
   */
  @Get( url = "/order/getPayStatusByOrderNo",
      contentType = "application/json",
      dataType = "json",
      headers = {"Api-Name: 从履约平台查询订单回款状态信息"}
  )
  ForestResponse<ResultBean<List<PaymentStatus>>> getOrderCustomerReturnStatus(
      @Query("dockingOrderNo") String orderNo,
      @Query("dockingOrderType") String platformCode,
      @Query("supplierOrderId") String supplierOrderId);

  @Post( url = "/dock/SrmOrder/getLargeTicketInfoByProjectNoList",
      contentType = "application/json",
      dataType = "json",
      headers = {"Api-Name: 履约同步供应商折扣比例"}
  )
  ForestResponse<BaseXhgjRes<List<PlatformLargeTicketProjectDTO>>> getLargeTicketInfoByProjectNoList(@Body
  PlatformLargeTicketParam body);
}
