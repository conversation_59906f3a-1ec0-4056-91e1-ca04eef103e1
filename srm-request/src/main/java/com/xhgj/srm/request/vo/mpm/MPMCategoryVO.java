package com.xhgj.srm.request.vo.mpm;

import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * 类目数据
 */
@Data
public class MPMCategoryVO implements Serializable {

  /**
   * fourCode : 33010101
   * fourName : hjx自测3
   * id :
   * oneCode : 33
   * oneName : hjx自测
   * threeCode : 330101
   * threeName : hjx自测2
   * twoCode : 3301
   * twoName : hjx自测1
   */

  /**
   * 四级类目编码
   */
  private String fourCode;

  /**
   * 四级类目名称
   */
  private String fourName;

  /**
   * 一级类目编码
   */
  private String oneCode;

  /**
   * 一级类目名称
   */
  private String oneName;

  /**
   * 三级类目编码
   */
  private String threeCode;

  /**
   * 三级类目名称
   */
  private String threeName;

  /**
   * 二级类目编码
   */
  private String twoCode;

  /**
   * 二级类目名称
   */
  private String twoName;
}
