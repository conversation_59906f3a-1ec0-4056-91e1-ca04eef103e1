package com.xhgj.srm.request.service.third.erp.sap.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON>g Shy on 2023/12/12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PurchaseApplyForOrderUpdateParam {

  /**
   * 申请单号
   */
  @JSONField(name = "BANFN")
  private String applyForNo;
  /**
   * 行id
   */
  @JSONField(name = "BNFPO")
  private String productRowId;
  /**
   * 采购员id
   */
  @JSONField(name = "BEDNR")
  private String purchaseManId;
  /**
   * 采购员
   */
  @JSONField(name = "AFNAM")
  private String purchaseMan;
  /**
   * 取消/反取消
   */
  @JSONField(name = "EBAKZ")
  private String cancelState;

  /**
   * 采购部门erp编码
   */
  @JSONField(name = "EKGRP")
  private String PurchaseDepartmentErpCode;

  /**
   * 申请数量
   */
  @JSONField(name = "MENGE")
  private String applyForCount;

  /**
   * 申请单备注
   */
  @JSONField(name = "ZBZ_HEAD")
  private String applyForRemark;

  /**
   * 审批结果  S通过 E不通过
   */
  @JSONField(name = "ZSPJG")
  private Boolean applyForResult;
  /**
   * 审批意见
   */
  @JSONField(name = "ZSPSM")
  private String applyForOpinion;

  public String getApplyForResult() {
    if (applyForResult == null) {
      return null;
    }
    return applyForResult ? "S" : "E";
  }
}
