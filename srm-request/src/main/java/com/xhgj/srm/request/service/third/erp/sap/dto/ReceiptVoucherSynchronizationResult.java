package com.xhgj.srm.request.service.third.erp.sap.dto;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.xhgj.srm.common.constants.Constants_Sap;
import java.util.List;
import lombok.Data;

@Data
public class ReceiptVoucherSynchronizationResult {

  @JSONField(name = "RETURN")
  private List<ReturnMessage> returnMessages;

  @Data
  public static class ReturnMessage {

    /**
     * 物料凭证号
     */
    @JSONField(name = "BELNR")
    private String documentNumber;
    /**
     * 物料凭证行项目
     */
    @JSONField(name = "BUZEI")
    private String lineItem;
    /**
     * 批号
     */
    @JSONField(name = "CHARG")
    private String charge;

    /**
     * 物料凭证年度
     */
    @JSONField(name = "GJAHR")
    private String year;

    /**
     * 采购订单号
     */
    @JSONField(name = "EBELN")
    private String purchaseOrderNo;

    /** 采购订单行项目 */
    @JSONField(name = "EBELP")
    private String purchaseOrderLineItems;
    /**
     * 消息类型
     */
    @JSONField(name = "TYPE")
    private String type;
    @JSONField(name = "MSG")
    private String message;
  }

  public boolean isSuccess() {
    if (CollUtil.isEmpty(returnMessages)) {
      return false;
    }
    for (ReturnMessage message : returnMessages) {
      if (Constants_Sap.ERROR_TYPE.equals(message.getType())) {
        return false;
      }
    }
    return true;
  }
}
