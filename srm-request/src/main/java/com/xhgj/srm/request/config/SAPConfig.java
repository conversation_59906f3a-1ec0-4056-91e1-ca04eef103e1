package com.xhgj.srm.request.config;

import com.xhgj.srm.request.enums.SAPMethod;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023-12-22 13:43
 */
@RefreshScope
@Data
@Configuration
@ConfigurationProperties(prefix = "third.sap")
public class SAPConfig {

  /** 请求路径 */
  private String baseUrl;

  /** sap 客户端 */
  private String sapClient;

  /** sap 用户 */
  private String userName;

  /** sap 密码 */
  private String password;

  /** 禁用方法列表 */
  private List<SAPMethod> disableMethodList;
}
