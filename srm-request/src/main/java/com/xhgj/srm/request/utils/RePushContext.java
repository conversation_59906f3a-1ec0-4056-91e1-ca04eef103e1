package com.xhgj.srm.request.utils;/**
 * @since 2025/5/7 16:23
 */

import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.jpa.entity.SupplierOrderSync;

/**
 *<AUTHOR>
 *@date 2025/5/7 16:23:05
 *@description
 */
public class RePushContext {

  /** 只用一个 ThreadLocal 保存所有数据 */
  private static final ThreadLocal<ContextHolder> LOCAL = ThreadLocal.withInitial(ContextHolder::new);

  /** 动态设置是否要记录，通常在调用 ForestClient 之前调用 */
  public static void setRecordFlag(boolean flag) {
    LOCAL.get().setRecordFlag(flag);
  }

  public static boolean isRecord() {
    return LOCAL.get().recordFlag;
  }

  public static ForestRequest getForestRequest() {
    return LOCAL.get().request;
  }

  // ==== ForestRequest 相关 ====
  public static void setForestRequest(ForestRequest request) {
    if (isRecord()) {                   // 根据开关决定是否记录
      LOCAL.get().setRequest(request);
    }
  }

  public static ForestResponse<?> getForestResponse() {
    return LOCAL.get().response;
  }

  // ==== ForestResponse 相关 ====
  public static void setForestResponse(ForestResponse<?> response) {
    if (isRecord()) {
      LOCAL.get().setResponse(response);
    }
  }

  public static SupplierOrderSync getRePushContext() {
    return LOCAL.get().rePushContext;
  }

  // ==== 重推上下文 相关 ====
  public static void setRePushContext(SupplierOrderSync ctx) {
    if (isRecord()) {
      LOCAL.get().setRePushContext(ctx);
    }
  }

  /** 清理所有线程变量，防止内存泄漏 */
  public static void clear() {
    LOCAL.remove();
  }

  /**
   * 承载真正变量的类
   */
  private static class ContextHolder {
    private ForestRequest request;
    private ForestResponse<?> response;
    private SupplierOrderSync rePushContext;
    /** 是否记录的开关，默认为 false */
    private boolean recordFlag = false;
    // 下面都是链式 setter，方便一次性配置
    ContextHolder setRequest(ForestRequest req) {
      this.request = req;
      return this;
    }
    ContextHolder setResponse(ForestResponse<?> resp) {
      this.response = resp;
      return this;
    }
    ContextHolder setRePushContext(SupplierOrderSync ctx) {
      this.rePushContext = ctx;
      return this;
    }
    ContextHolder setRecordFlag(boolean flag) {
      this.recordFlag = flag;
      return this;
    }
  }
}
