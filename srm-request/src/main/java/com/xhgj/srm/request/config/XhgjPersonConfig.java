package com.xhgj.srm.request.config;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created by Geng Shy on 2023/8/31
 */
@Configuration
@ConfigurationProperties(prefix = "third.xhgj.person")
@Data
@EqualsAndHashCode(callSuper = true)
public class XhgjPersonConfig extends AbstractXhgjConfig{
}
