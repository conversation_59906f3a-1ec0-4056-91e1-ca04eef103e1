package com.xhgj.srm.request.dto.mpm.externalLink;/**
 * @since 2025/1/15 18:00
 */

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/1/15 18:00:26
 *@description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductPriceEvidenceDTO {
  /**
   * id
   */
  private String id;


  /**
   * 价格
   */
  private BigDecimal price;

  /**
   * 类型
   */
  private String type;

  /**
   * 价格佐证列表
   */
  private List<ProductPriceEvidenceFileDTO> fileList;

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class ProductPriceEvidenceFileDTO {

    /**
     * 文件名
     */
    private String name;

    /**
     * 文件路径（半路径）
     */
    private String url;

    /**
     * 文件路径前缀
     */
    private String baseUrl;
  }
}
