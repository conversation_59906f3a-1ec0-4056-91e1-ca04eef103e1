package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

/** <AUTHOR> @ClassName QueryBILLParam */
@Data
@Builder
public class QueryBillParam{
  /** 数据中心 id */
  @JSONField(name = "DbId")
  public String dbId;
  /** 登录名 */
  @JSONField(name = "PassWord")
  public String passWord;
  /** 密码 */
  @JSONField(name = "UserName")
  public String userName;
  @JSONField(name = "Model")
  public ModelDTO data;

  @Data
  @Builder
  public static class ModelDTO {

    /** 付款单号,多单用“,”隔开 */
    @JSONField(name = "FBillNo")
    private String billNo;
  }
}
