package com.xhgj.srm.request.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: fanghuanxu
 * @Date: 2025/2/25 11:37
 * @Description: h0流程平台配置
 */
@Configuration
@ConfigurationProperties(prefix = "third.hand.process")
@Data
public class HZeroProcessConfig {

  /**
   * 调拨单流程编码
   */
  private String transferOrderFlowKey;

  /**
   * 组装拆卸单流程编码
   */
  private String asmDisOrderFlowKey;

  /**
   * 入驻报备流程编码
   */
  private String entryOrderFlowKey;

  /**
   * 采购申请单流程编码
   */
  private String purchaseApplyOrderFlowKey;

  /**
   * 采购订单流程编码
   */
  private String purchaseOrderFlowKey;

  /**
   * 采购订单入库单流程编码
   */
  private String purchaseOrderWarehouseFlowKey;

  /**
   * 采购订单退货单流程编码
   */
  private String purchaseOrderReturnFlowKey;
  /**
   * 付款申请流程编码
   */
  private String paymentApplyFlowKey;
}
