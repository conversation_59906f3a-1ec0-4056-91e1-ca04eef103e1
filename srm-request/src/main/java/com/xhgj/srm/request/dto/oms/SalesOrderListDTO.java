package com.xhgj.srm.request.dto.oms;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class SalesOrderListDTO {

  /**
   * 不做业务字段使用,仅作为前端选中所需的唯一标识
   */
  @ApiModelProperty("订单详情id")
  private String orderDetailId;

  @ApiModelProperty("物料行号")
  private String rowNo;

  @ApiModelProperty("订单编码")
  private String orderNo;

  @ApiModelProperty("物料编码")
  private String productCode;

  @ApiModelProperty("品牌")
  private String productBrandName;

  @ApiModelProperty("物料名称")
  private String productName;

  @ApiModelProperty("规格型号")
  private String productModel;

  @ApiModelProperty("单位")
  private String productUnit;

  @ApiModelProperty("单位编码")
  private String productUnitCode;

  @ApiModelProperty("可订货数量")
  private String waitPushNum;

  @ApiModelProperty("数量")
  private String num;

  @ApiModelProperty("MPM参考结算价")
  private String mpmReferencePrice;

  @ApiModelProperty("业务员名称")
  private String businessManName;

  @ApiModelProperty("业务员编码")
  private String businessManCode;

  @ApiModelProperty("售达方名称")
  private String finalCustomerName;

  @ApiModelProperty("售达方编码")
  private String finalCustomerCode;

  @ApiModelProperty("项目编码")
  private String projectNo;
  @ApiModelProperty("发货方式")
  private String openWay;

  @ApiModelProperty("跟单员姓名")
  private String followUpPersonName;

  @ApiModelProperty("业务员所在公司名称")
  private String businessCompanyName;

  @ApiModelProperty("制单员名称")
  private String makeManName;

  /**
   * projectName
   */
  @ApiModelProperty("项目名称")
  private String projectName;
}
