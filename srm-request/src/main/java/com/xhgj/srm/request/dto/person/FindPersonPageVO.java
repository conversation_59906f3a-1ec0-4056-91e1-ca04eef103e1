package com.xhgj.srm.request.dto.person;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Created by <PERSON>g Shy on 2023/12/12
 */
@Data
public class FindPersonPageVO {

  /**
   * 钉钉头像
   */
  @JsonProperty("dingAvatar")
  private String dingAvatar;

  /**
   * 钉钉ID
   */
  @JsonProperty("dingId")
  private String dingId;

  /**
   * 钉钉员工企业标识 id
   */
  @JsonProperty("dingUnionId")
  private String dingUnionId;

  /**
   * 邮箱地址
   */
  @JsonProperty("email")
  private String email;

  /**
   * 员工管理员ID
   */
  @JsonProperty("empAdminId")
  private String empAdminId;

  /**
   * 员工状态
   */
  @JsonProperty("empStatus")
  private String empStatus;

  /**
   * 是否启用
   */
  @JsonProperty("enabled")
  private boolean enabled;

  /**
   * MDM ID
   */
  @JsonProperty("id")
  private String id;

  /**
   * 工作职位
   */
  @JsonProperty("job")
  private String job;

  /**
   * 工号
   */
  @JsonProperty("jobNumber")
  private String jobNumber;

  /**
   * 手机号码
   */
  @JsonProperty("mobile")
  private String mobile;

  /**
   * 姓名
   */
  @JsonProperty("name")
  private String name;

  /**
   * OA ID
   */
  @JsonProperty("oaId")
  private String oaId;

  /**
   * 办公电话
   */
  @JsonProperty("officeTel")
  private String officeTel;

  /**
   * 组织ID
   */
  @JsonProperty("organizationId")
  private String organizationId;

  /**
   * 所有父级 oa id，包含部门和组织的 id
   */
  @JsonProperty("parentIds")
  private String parentIds;

  /**
   * 所有父级名称，包含部门和组织的名称
   */
  @JsonProperty("parentNames")
  private String parentNames;

  /**
   * 性别
   */
  @JsonProperty("sex")
  private String sex;

  /**
   * 北森用户ID
   */
  @JsonProperty("userId")
  private String userId;

  /**
   * 启用权限:1.开启，0：关闭，2：临时开启
   */
  @JsonProperty("onState")
  private String onState;
}
