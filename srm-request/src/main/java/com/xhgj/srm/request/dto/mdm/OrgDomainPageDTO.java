package com.xhgj.srm.request.dto.mdm;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class OrgDomainPageDTO {

  @ApiModelProperty("内容")
  private List<OrgDomain> content;

  @JSONField(name = "pageNo")
  private Integer pageNo;

  @JSONField(name = "pageSize")
  private Integer pageSize;

  @JSONField(name = "totalCount")
  private Integer totalCount;

  @JSONField(name = "totalPages")
  private Integer totalPages;
}
