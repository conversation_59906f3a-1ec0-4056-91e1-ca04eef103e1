package com.xhgj.srm.request.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/12/14 16:11
 */
@Getter
public enum ERPUrl {

  /** ERP采购订单关闭/反关闭 */
  OPEN_OR_CLOSE_ORDER("/SRMWebApi/OpenOrCloseOrder", "ERP采购订单关闭/反关闭"),
  /** ERP采购订单退货（下推采购退货） */
  PUSH_PUR_MRB("/SRMWebApi/PushPur_MRB", "ERP采购订单退货（下推采购退货）"),
  /** ERP采购退货单修改数量 */
  UPDATE_PUR_MRB("/SRMWebApi/UpdatePur_MRB", "ERP采购退货单修改数量"),
  /** ERP采购订单下推采购入库单 */
  ADD_BILL("/SRMWebApiPURInStock/AddBILL", "ERP采购订单下推采购入库单"),
  /** ERP采购订单物流信息链接修改 */
  UPDATE_LOGISTICS("/SRMWebApi/UpdatePur_MRBLogisitca", "ERP采购订单物流信息链接修改"),
  /** 应收单查询对接 */
  RECEIVABLE_QUERY("/ReceivableQuery.ashx", "应收单查询对接"),
  /** 回款单查询对接 */
  RECEIVABLE_BILL("/ReceBillQuery.ashx", "回款单查询对接"),
   /** 付款单查询 */
   QUERY_BILL("/SRMWebApiQueryPayBill/QueryBILL", "付款单查询"),
  AP_PAYBILL("/Form/AP_PAYBILL", "新增付款单"),
  /** 落地商订单新增采购订单 */
  ADD_POORDER("/AddPoorder.ashx", "落地商订单新增采购订单"),
  ADD_PAYABLE("/STKInStock/PushAPPayable", "新增应付单"),

  GET_ORDER_PAY_AMOUNT("/GetOrderPayAmount.ashx", "查询应付金额"),

  GET_AMOUNT_PAYABLE("/APPayDetailReport/Search", "查询供应商应付总金额"),

  UPDATE_ERP_ORDER_RATE("/OrderUnAduitAndEdit.ashx", "采购订单反审核修改");

  /** 请求接口路径 */
  private final String url;
  /** 接口描述 */
  private final String desc;

  ERPUrl(String url, String desc) {
    this.url = url;
    this.desc = desc;
  }
}
