package com.xhgj.srm.request.service.third.api;/**
 * @since 2025/2/25 18:05
 */

import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Query;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.srm.request.dto.hZero.process.StartProcessParam;
import com.xhgj.srm.request.dto.hZero.process.StartProcessVo;
import com.xhgj.srm.request.dto.hZero.process.SupplierOrderWarehouseApplyForm;
import com.xhgj.srm.request.service.third.api.interceptor.FeiDaInterceptor;
import com.xhiot.boot.forest.annotation.BaseRequest;
import com.xhiot.boot.forest.annotation.HandUrl;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/2/25 18:05:22
 *@description
 */
@BaseRequest(interceptor = FeiDaInterceptor.class)
public interface FeiDaApi {

  /**
   * 飞搭启动流程
   */
  @Post(
      value = "/",
      contentType = "application/json"
  )
  @HandUrl("/hitf/v2p/rest/invoke/bo-start-without-file")
  ForestResponse<StartProcessVo> startProcessWithoutFile(
      @Body StartProcessParam param,
      @Query("description") String description
  );

  /**
   * 采购订单质检入库申请单生成
   */
  @Post(
      value = "/",
      contentType = "application/json"
  )
  @HandUrl("/hitf/v2p/public/rest/invoke/SFpFUk86UFAwMTA6aHplcm8tbW9kZWxlci5mbG93LWV4ZWN1dG9yLW1hbmFnZS1zaXRlLWxldmVsLmZsb3dFeGVjdXRl")
  ForestResponse<String> purchaseOrderWarehouseApply(@Body List<SupplierOrderWarehouseApplyForm> forms
  );

}
