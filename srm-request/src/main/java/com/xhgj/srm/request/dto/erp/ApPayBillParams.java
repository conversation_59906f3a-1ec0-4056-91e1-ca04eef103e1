package com.xhgj.srm.request.dto.erp;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-04-19 9:38
 */
@NoArgsConstructor
@Data
public class ApPayBillParams {

  /** 套账标识 */
  @JSONField(name = "DbId")
  private String dbId;
  /** 金蝶登录用户 */
  @JSONField(name = "UserName")
  private String userName;
  @JSONField(name = "PassWord")
  /** 金蝶登录用户密码 */
  private String passWord;
  /** 单据类型 */
  @JSONField(name = "billType")
  private String billType;
  /** 业务日期 */
  @JSONField(name = "date")
  private String date;
  @JSONField(name = "qwdate")
  /** 期望付款日期 */
  private String qwdate;
  @JSONField(name = "contactUnit")
  /** 往来单位 */
  private String contactUnit;
  /** 收款单位 */
  @JSONField(name = "rectUnit")
  private String rectUnit;
  /** 币别编码 */
  @JSONField(name = "currency")
  private String currency;
  /** 付款用途 */
  @JSONField(name = "fkyt")
  private String fkyt;
  /** 付款组织 */
  @JSONField(name = "fkOrgNo")
  private String fkOrgNo;
  /** 结算组织 */
  @JSONField(name = "jsOrgNo")
  private String jsOrgNo;
  /** 采购组织 */
  @JSONField(name = "cgOrgNo")
  private String cgOrgNo;
  /** 备注 */
  @JSONField(name = "remark")
  private String remark;
  /** 采购员 */
  @JSONField(name = "purchaser")
  private String purchaser;
  /** 付款申请人 */
  @JSONField(name = "payApply")
  private String payApply;
  /** 是否为补录单据 */
  @JSONField(name = "isbulu")
  private String isbulu;
  /** 原单类型 */
  @JSONField(name = "ydlx")
  private Integer ydlx;
  /** 明细信息 */
  @JSONField(name = "details")
  private List<DetailsDTO> details;

  @NoArgsConstructor
  @Data
  public static class DetailsDTO {
    /** 结算方式 */
    @JSONField(name = "jsfs")
    private String jsfs;
    /** 付款用途 */
    @JSONField(name = "fkyt")
    private String fkyt;
    /** 采购订单 */
    @JSONField(name = "purorderNo")
    private String purorderNo;
    /** 实付金额 */
    @JSONField(name = "realpayAmount")
    private BigDecimal realpayAmount;
    /** 应付金额 */
    @JSONField(name = "needpayAmount")
    private BigDecimal needpayAmount;
  }
}
