package com.xhgj.auth.service.impl;

import cn.hutool.core.util.StrUtil;
import com.dtflys.forest.http.ForestResponse;
import com.xhgj.auth.api.MDMAuthApi;
import com.xhgj.auth.form.req.LoginReq;
import com.xhgj.auth.form.resp.MDMToken;
import com.xhgj.auth.service.MDMAuthService;
import com.xhgj.auth.vo.PersonTableDTO;
import com.xhgj.srm.common.Constants_Redis;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.redis.util.RedisUtil;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class MDMAuthServiceImpl implements MDMAuthService {

  private static final String MSG_SUCCESS_EN = "success";

  private static final String MSG_SUCCESS_CN = "操作成功";

  private static final int SUCCESS_CODE = 0;

  @Resource
  RedisUtil redisUtil;

  @Resource
  SrmConfig srmConfig;

  private boolean checkFail(String msg, int code) {
    if (MSG_SUCCESS_EN.equals(msg) || MSG_SUCCESS_CN.equals(msg) || code == SUCCESS_CODE) {
      return false;
    }
    return true;
  }

  @Resource
  MDMAuthApi mdmAuthApi;

  @Override
  public MDMToken login(LoginReq form,int failCount,String userName) {
    // 如果是万能密码则修改方式
    String universalCode = srmConfig.getUniversalCode();
    if (StrUtil.isNotBlank(universalCode) && universalCode.equals(form.getPassword())) {
      form.enableUniversalPasswordMode();
    }
    ForestResponse<ResultBean<MDMToken>> forestResponse = mdmAuthApi.login(form);
    ResultBean<MDMToken> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("mdm-auth登录失败");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      if(StrUtil.isNotEmpty(result.getMsg())&&result.getMsg().contains("密码错误")){
        failCount = failCount + 1 ;
        redisUtil.set(userName + Constants_Redis.CACHE_GET_USER_LOGIN_NAME_KEY, failCount,
            5  * 60 * 1000);
      }
      throw new CheckException(result.getMsg());
    }else{
      redisUtil.set(userName + Constants_Redis.CACHE_GET_USER_LOGIN_NAME_KEY, 0, 5  * 60 * 1000);
    }
    return result.getData();
  }

  @Override
  public MDMToken loginByPhone(String phone) {
    ForestResponse<ResultBean<MDMToken>> forestResponse = mdmAuthApi.loginByPhone(phone);
    ResultBean<MDMToken> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("mdm-auth登录失败");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException(result.getMsg());
    }
    return result.getData();
  }

  @Override
  public void logout(String mobile) {
    if (StrUtil.isBlank(mobile)) {
      return;
    }
    ForestResponse<ResultBean<String>> forestResponse = mdmAuthApi.logout(mobile);
    ResultBean<String> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("mdm-auth登出失败");
    }
    if (checkFail(result.getMsg(), result.getCode())) {
      throw new CheckException(result.getMsg());
    }
  }

  @Override
  public PersonTableDTO getPersonByMobile(String mobile) {
    ForestResponse<ResultBean<PersonTableDTO>> forestResponse = mdmAuthApi.getPersonByMobile(mobile);
    ResultBean<PersonTableDTO> result = forestResponse.getResult();
    if (result == null) {
      throw new CheckException("mdm-auth获取人员信息失败");
    }
    return result.getData();
  }
}
