package com.xhgj.srm.open.vo.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * 开发接口登录VO
 */
@ApiModel(description = "开发接口登录VO")
@Data
public class OpenLoginVO {
  /**
   * token
   */
  @ApiModelProperty("token")
  private String token;

  /**
   * 用户名
   */
  @ApiModelProperty("用户名")
  private String name;

  /**
   * 账号用户名
   */
  @ApiModelProperty("账号用户名")
  private String realName;

  /**
   * 用户id
   */
  @ApiModelProperty("用户id")
  private String userId;

  public OpenLoginVO(String token, String name, String realName, String userId) {
    this.token = token;
    this.name = name;
    this.realName = realName;
    this.userId = userId;
  }
}
