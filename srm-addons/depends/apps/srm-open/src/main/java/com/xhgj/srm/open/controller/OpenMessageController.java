package com.xhgj.srm.open.controller;

import com.xhgj.srm.open.form.message.OpenMessageQueryForm;
import com.xhgj.srm.open.service.OpenMessageService;
import com.xhgj.srm.open.vo.message.BaseOpenMessageVO;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 流转消息接口
 */
@RestController
@RequestMapping("/open/message")
@Slf4j
public class OpenMessageController {

  @Resource
  OpenMessageService openMessageService;

  /**
   * 消息流转表 请求消息分页
   */
  @ApiOperation(value = "消息流转表 请求消息分页", notes = "消息流转表 请求消息分页")
  @GetMapping(value = "")
  public ResultBean<PageResult<? extends BaseOpenMessageVO>> getPageList(@Valid @ModelAttribute OpenMessageQueryForm form) {
    return new ResultBean<>(openMessageService.getOpenMessagePage(form));
  }

  /**
   * 消息流转表 删除消息
   */
  @ApiOperation(value = "消息流转表 删除消息", notes = "消息流转表 删除消息")
  @ApiImplicitParams(
      @ApiImplicitParam(name = "messageId", value = "消息id", required = true, dataType = "string", paramType = "path")
  )
  @DeleteMapping(value = "/{messageId}")
  public ResultBean<Boolean> deleteMessage(@PathVariable("messageId") String messageId) {
    openMessageService.deleteOpenMessage(messageId);
    return new ResultBean<>(true);
  }

}
