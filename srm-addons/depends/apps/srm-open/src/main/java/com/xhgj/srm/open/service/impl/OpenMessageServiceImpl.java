package com.xhgj.srm.open.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.open.dao.OpenMessageDao;
import com.xhgj.srm.open.domain.OpenUserDetails;
import com.xhgj.srm.open.entity.OpenMessage;
import com.xhgj.srm.open.factory.OpenMessageFactory;
import com.xhgj.srm.open.form.message.OpenMessageQueryForm;
import com.xhgj.srm.open.form.message.OpenMessageSaveForm;
import com.xhgj.srm.open.repository.OpenMessageRepository;
import com.xhgj.srm.open.service.OpenMessageService;
import com.xhgj.srm.open.utils.JudgePermissionUtil;
import com.xhgj.srm.open.utils.OpenSecurityUtil;
import com.xhgj.srm.open.vo.message.BaseOpenMessageVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OpenMessageServiceImpl implements OpenMessageService {

  @Resource
  private OpenMessageRepository openMessageRepository;
  @Resource
  private OpenMessageFactory openMessageFactory;
  @Resource
  private OpenMessageDao openMessageDao;
  @Resource
  private OpenSecurityUtil openSecurityUtil;
  @Resource
  private JudgePermissionUtil judgePermissionUtil;

  @Override
  public PageResult<? extends BaseOpenMessageVO> getOpenMessagePage(OpenMessageQueryForm form) {
    SupplierUser supplierUser = openSecurityUtil.getOpenUserDetails().supplierUser();
    Page<OpenMessage> page = openMessageDao.getPage(form.toQueryMap(supplierUser.getSupplierId()));
    List<OpenMessage> content = page.getContent();
    if (CollUtil.isEmpty(content)) {
      return PageResult.empty(form.getPageNo(), form.getPageSize());
    }
    List<BaseOpenMessageVO> result =
        content.stream().map(openMessageFactory::buildVo).collect(Collectors.toList());
    return new PageResult<>(result, page.getTotalElements(), page.getTotalPages(),
            form.getPageNo(), form.getPageSize());
  }

  @Override
  public BaseOpenMessageVO getOpenMessageDetail(String id) {
    OpenMessage openMessage =
        openMessageRepository.findById(id).orElseThrow(() -> new CheckException("消息不存在"));
    if (Constants.STATE_NO.equals(openMessage.getState())) {
      throw new CheckException("消息已删除");
    }
    return openMessageFactory.buildVo(openMessage);
  }

  @Override
  public void deleteOpenMessage(String id) {
    OpenMessage openMessage = openMessageRepository.findById(id).orElse(null);
    if (openMessage != null) {
      judgePermissionUtil.judgeSupplierUserPermission(openMessage.getSupplierId(), "无权限删除此消息");
      openMessage.setState(Constants.STATE_NO);
      openMessage.setUpdateTime(System.currentTimeMillis());
      openMessageRepository.save(openMessage);
    }
  }

  @Override
  public void saveOpenMessage(OpenMessageSaveForm form) {
    OpenMessage openMessage;
    if (form.getId() == null) {
      openMessage = openMessageFactory.create(form);
    } else {
      openMessage = openMessageFactory.update(form);
    }
    openMessageRepository.save(openMessage);
  }
}

