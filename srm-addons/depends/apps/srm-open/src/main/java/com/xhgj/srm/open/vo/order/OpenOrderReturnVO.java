package com.xhgj.srm.open.vo.order;

import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.open.dto.order.OpenOrderReturnDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 订单退货VO对象
 */
@Data
@NoArgsConstructor
public class OpenOrderReturnVO {
  @ApiModelProperty("退货单 id")
  private String returnId;

  @ApiModelProperty("退货编号")
  private String returnNo;

  @ApiModelProperty("退货时间")
  private Long returnTime;

  @ApiModelProperty("退货金额")
  private BigDecimal returnPrice;

  @ApiModelProperty("退货状态")
  private String state;

  @ApiModelProperty("退货状态(中文)")
  private String stateStr;

  @ApiModelProperty(" ERP 采购退料单号")
  private String erpNo;

  @ApiModelProperty("退货单采购订单号")
  private String purchaseOrderNo;

  @ApiModelProperty("退货明细")
  private List<OpenOrderReturnDetailVO> returnDetailList;

  public OpenOrderReturnVO(String returnId, String returnNo, Long returnTime,
      BigDecimal returnPrice, String state, String stateStr, String erpNo, String purchaseOrderNo) {
    this.returnId = returnId;
    this.returnNo = returnNo;
    this.returnTime = returnTime;
    this.returnPrice = returnPrice;
    this.state = state;
    this.stateStr = stateStr;
    this.erpNo = erpNo;
    this.purchaseOrderNo = purchaseOrderNo;
  }

  public OpenOrderReturnVO(OpenOrderReturnDto openOrderReturnDto) {
    this.returnId = openOrderReturnDto.getId();
    this.returnNo = openOrderReturnDto.getReturnNo();
    this.returnTime = openOrderReturnDto.getCreateTime();
    this.returnPrice = openOrderReturnDto.getPrice();
    this.state = openOrderReturnDto.getReturnState();
    this.stateStr = Constants_order.ORDER_RETURN_STATE_MAP.get(this.state);
    this.erpNo = openOrderReturnDto.getErpNo();
    this.purchaseOrderNo = openOrderReturnDto.getPurchaseOrderNo();
    this.returnDetailList = openOrderReturnDto.getOrderReturnDetails().stream()
        .map(OpenOrderReturnDetailVO::new)
        .collect(Collectors.toList());
  }
}
