package com.xhgj.srm.v2.form;/**
 * @since 2025/4/17 18:32
 */

import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class PurchaseApplyForOrderV2QueryForm implements BaseDefaultSearchSchemeForm {

  /**
   * 用户id
   */
  @ApiModelProperty("用户id")
  private String userId;

  /**
   * 方案id
   */
  @ApiModelProperty("方案id")
  private String schemeId;

  /**
   * 页码
   */
  @ApiModelProperty("页码")
  private Integer pageNo;

  /**
   * 页数
   */
  @ApiModelProperty("页数")
  private Integer pageSize;

  /**
   * 申请单号
   */
  @ApiModelProperty("申请单号")
  private String applyForOrderNo;


  @ApiModelProperty("修改记录：1是，0否")
  private String modifyRecord;

  @ApiModelProperty("客户资料：1是，0否")
  private String customerProfile;

  @ApiModelProperty("组件清单：1是，0否")
  private String componentManifest;

  /**
   * 业务员
   */
  @ApiModelProperty("业务员：多个用英文逗号")
  private String salesman;

  /**
   * 跟单员
   */
  @ApiModelProperty("跟单员：多个用英文逗号")
  private String merchandiser;

  /**
   * 采购员
   */
  @ApiModelProperty("采购员：多个用英文逗号")
  private String purchaseMan;

  /**
   * 采购部门名称
   */
  @ApiModelProperty("采购部门名称：多个用英文逗号")
  private String purchaseDepartment;

  /**
   * 采购申请类型
   */
  @ApiModelProperty("采购申请类型：多个用英文逗号")
  private String applyForType;

  /**
   * 订货状态OrderGoodsStateV2Enum
   * {@link com.xhgj.srm.common.enums.OrderGoodsStateV2Enum}
   */
  @ApiModelProperty("订货状态：1.可订货，2.订货完成，4.锁定（多个用英文逗号）")
  private String orderGoodsState;

  @ApiModelProperty("取消状态（0，1）")
  private String cancellationState;

  /**
   * 创建时间-开始
   */
  @ApiModelProperty("创建时间-开始")
  private Long startCreateTime;

  /**
   * 创建时间-结束
   */
  @ApiModelProperty("创建时间-结束")
  private Long endCreateTime;

  /**
   * 申请单备注
   */
  @ApiModelProperty("申请单备注：多个用英文逗号")
  private String applicationFormRemarks;

  /**
   * 是否急单:Y/N
   */
  @ApiModelProperty("是否急单:Y/N")
  private String isWorryOrder;

  /**
   * 是否直发：1是，0否
   */
  @ApiModelProperty("是否直发：1是，0否")
  private String directShipment;

  @ApiModelProperty("物料序号：多个用英文逗号")
  private String serialNumber;


  /**
   * 资料卡片:编码+名称
   */
  @ApiModelProperty("资料卡片:编码+名称")
  private String profileCard;

  /**
   * 物料编码
   */
  @ApiModelProperty("物料编码：多个用英文逗号")
  private String productCode;


  /**
   * 品牌
   */
  @ApiModelProperty("品牌：多个用英文逗号")
  private String brand;

  /**
   * 物料名称
   */
  @ApiModelProperty("物料名称：多个用英文逗号")
  private String purchaseProductName;


  /**
   * 物料描述
   */
  @ApiModelProperty("物料描述")
  private String materialDescription;

  /**
   * 物料备注
   */
  @ApiModelProperty("物料备注：多个用英文逗号")
  private String materialLineRemarks;

  /**
   * 规格
   */
  @ApiModelProperty("规格：多个用英文逗号")
  private String specification;

  /**
   * 型号（之前的规格型号）
   */
  @ApiModelProperty("型号：多个用英文逗号")
  private String model;


  /**
   * 单位
   */
  @ApiModelProperty("单位：多个用英文逗号")
  private String unit;

  /**
   * 申请数量操作符号
   */
  @ApiModelProperty("申请数量操作符号")
  private LogicalOperatorsEnums applyForNumberOperators;

  /**
   * 申请数量
   */
  @ApiModelProperty("申请数量")
  private BigDecimal applyForNumber;

  /**
   * 已订货数量操作符号
   */
  @ApiModelProperty("已订货数量操作符号")
  private LogicalOperatorsEnums orderGoodsNumberOperators;

  /**
   * 已订货数量
   */
  @ApiModelProperty("已订货数量")
  private BigDecimal orderGoodsNumber;

  /**
   * 未订货数量操作符号
   */
  @ApiModelProperty("未订货数量操作符号")
  private LogicalOperatorsEnums unorderedQuantityOperators;

  /**
   * 未订货数量
   */
  @ApiModelProperty("未订货数量")
  private BigDecimal unorderedQuantity;


  /**
   * 销售单价操作符号
   */
  @ApiModelProperty("销售单价操作符号")
  private LogicalOperatorsEnums salesUnitPriceOperators;

  /**
   * 销售单价
   */
  @ApiModelProperty("销售单价")
  private BigDecimal salesUnitPrice;

  /**
   * 销售需求数量操作符号
   */
  @ApiModelProperty("销售需求数量操作符号")
  private LogicalOperatorsEnums salesDemandQuantityOperators;

  /**
   * 销售需求数量
   */
  @ApiModelProperty("销售需求数量")
  private BigDecimal salesDemandQuantity;

  /**
   * MPM参考结算价
   */
  @ApiModelProperty("MPM参考结算价")
  private BigDecimal mpmReferenceSettlementPrice;

  /**
   * MPM参考结算价操作符号
   */
  @ApiModelProperty("MPM参考结算价操作符号")
  private LogicalOperatorsEnums mpmReferenceSettlementPriceOperators;


  /**
   * 计划需求日期-开始
   */
  @ApiModelProperty("计划需求日期-开始")
  private Long planDemandDateStart;

  /**
   * 计划需求日期-结束
   */
  @ApiModelProperty("计划需求日期-结束")
  private Long planDemandDateEnd;


  /**
   * 售达方
   */
  @ApiModelProperty("售达方")
  private String soldToParty;

  /**
   * 收件人
   */
  @ApiModelProperty("收件人")
  private String consignee;

  /**
   * 收件人联系方式
   */
  @ApiModelProperty("收件人联系方式")
  private String contactInformation;

  /**
   * 销售订单号
   */
  @ApiModelProperty("销售订单号")
  private String saleOrderNo;

  /**
   * 销售订单行项目
   */
  @ApiModelProperty("销售订单行项目")
  private String saleOrderProductRowId;

  /**
   * 项目编码
   */
  @ApiModelProperty("项目编码")
  private String projectNo;

  @ApiModelProperty("仓库")
  private String warehouse;

  /**
   * 科目分配类别:编码+名称
   */
  @ApiModelProperty("科目分配类别:编码+名称")
  private String assignmentCategory;

  /**
   * 科目分配类别编码
   */
  @ApiModelProperty("科目分配类别编码")
  private String assignmentCategoryCode;

  /**
   * 项目类别:编码+名称
   */
  @ApiModelProperty("项目类别:编码+名称")
  private String projectCategory;

  /**
   * 项目类别编码
   */
  @ApiModelProperty("项目类别编码")
  private String projectCategoryCode;

  /**
   * 总账科目:编码+名称
   */
  @ApiModelProperty("总账科目:编码+名称")
  private String ledgerSubject;


  /**
   * 成本中心:编码+名称
   */
  @ApiModelProperty("成本中心:编码+名称")
  private String costCenter;


  /**
   * 订单:编码+名称
   */
  @ApiModelProperty("订单:编码+名称")
  private String order;

  /**
   * 交货日期-开始
   */
  @ApiModelProperty("交货日期-开始")
  private Long deliverTimeStart;

  /**
   * 交货日期-结束
   */
  @ApiModelProperty("交货日期-结束")
  private Long deliverTimeEnd;

  /**
   * 物料组:编码+名称
   */
  @ApiModelProperty("物料组:编码+名称")
  private String itemGroup;

  /**
   * 物料组编码
   */
  @ApiModelProperty("物料组编码")
  private String itemGroupCode;

  /**
   * 固定的供应商
   */
  @ApiModelProperty("固定的供应商")
  private String fixedVendor;

  /**
   * 采购信息记录
   */
  @ApiModelProperty("采购信息记录")
  private String procurementRecord;

  /**
   * 用户组织
   */
  @ApiModelProperty("用户组织")
  private String userGroup;

  @ApiModelProperty("采购申请table状态：1：可订货，2：订货完成，3：全部")
  private String tableKey;

  @ApiModelProperty("是否取消申请（标签）")
  private Boolean isCancelApplication;

  @ApiModelProperty("是否有修改记录（标签）")
  private Boolean isReviseRecord;

  @ApiModelProperty("申请人")
  private String applicant;

  @ApiModelProperty("项目名称")
  private String largeTicketProjectName;

  @ApiModelProperty("收件地址")
  private String deliveryAddress;

  @ApiModelProperty("销售组织")
  private String salesOrganization;

  @ApiModelProperty("采购组织")
  private String purchasingOrganization;

  @ApiModelProperty("发货方式")
  private String deliveryType;

  @ApiModelProperty("客户订单号")
  private String customerOrderNumber;


  public Map<String, Object> toQueryMap(MergeUserPermission searchPermission) {
      Map<String, Object> map = new HashMap<>();
      map.put("userId", userId);
      map.put("schemeId", schemeId);
      map.put("pageNo", pageNo);
      map.put("pageSize", pageSize);
      map.put("applyForOrderNo", applyForOrderNo);
      map.put("applyForType", applyForType);
      map.put("orderGoodsState", orderGoodsState);
      map.put("cancellationState", cancellationState);
      map.put("startCreateTime", startCreateTime);
      map.put("endCreateTime", endCreateTime);
      map.put("productCode", productCode);
      map.put("warehouse", warehouse);
      map.put("brand", brand);
      map.put("purchaseProductName", purchaseProductName);
      map.put("planDemandDateStart", planDemandDateStart);
      map.put("planDemandDateEnd", planDemandDateEnd);
      map.put("purchaseMan", purchaseMan);
      map.put("soldToParty", soldToParty);
      map.put("saleOrderNo", saleOrderNo);
      map.put("purchaseDepartment", purchaseDepartment);
      map.put("merchandiser", merchandiser);
      map.put("model", model);
      map.put("unit", unit);
      map.put("applyForNumberOperators", applyForNumberOperators);
      map.put("applyForNumber", applyForNumber);
      map.put("orderGoodsNumberOperators", orderGoodsNumberOperators);
      map.put("orderGoodsNumber", orderGoodsNumber);
      map.put("unorderedQuantityOperators", unorderedQuantityOperators);
      map.put("unorderedQuantity", unorderedQuantity);
      map.put("salesUnitPriceOperators", salesUnitPriceOperators);
      map.put("salesUnitPrice", salesUnitPrice);
      map.put("salesDemandQuantityOperators", salesDemandQuantityOperators);
      map.put("salesDemandQuantity", salesDemandQuantity);
      map.put("mpmReferenceSettlementPriceOperators", mpmReferenceSettlementPriceOperators);
      map.put("mpmReferenceSettlementPrice", mpmReferenceSettlementPrice);
      map.put("consignee", consignee);
      map.put("contactInformation", contactInformation);
      map.put("applicationFormRemarks", applicationFormRemarks);
      map.put("materialLineRemarks", materialLineRemarks);
      map.put("materialDescription", materialDescription);
      map.put("salesman", salesman);
      map.put("saleOrderProductRowId", saleOrderProductRowId);
      map.put("isWorryOrder", isWorryOrder);
      map.put("directShipment", directShipment);
      map.put("userGroup", userGroup);
      map.put("serialNumber", serialNumber);
      map.put("profileCard", profileCard);
      map.put("specification", specification);
      map.put("projectNo", projectNo);
      map.put("assignmentCategory", assignmentCategory);
      map.put("assignmentCategoryCode", assignmentCategoryCode);
      map.put("projectCategory", projectCategory);
      map.put("projectCategoryCode", projectCategoryCode);
      map.put("ledgerSubject", ledgerSubject);
      map.put("costCenter", costCenter);
      map.put("order", order);
      map.put("deliverTimeStart", deliverTimeStart);
      map.put("deliverTimeEnd", deliverTimeEnd);
      map.put("itemGroup", itemGroup);
      map.put("itemGroupCode", itemGroupCode);
      map.put("fixedVendor", fixedVendor);
      map.put("procurementRecord", procurementRecord);
      map.put("searchPermission", searchPermission);
      map.put("tableKey", tableKey);
      map.put("isCancelApplication", isCancelApplication);
      map.put("isReviseRecord", isReviseRecord);
      map.put("modifyRecord", modifyRecord);
      map.put("componentManifest", componentManifest);
      map.put("customerProfile", customerProfile);
      map.put("applicant", applicant);
      map.put("largeTicketProjectName", largeTicketProjectName);
      map.put("deliveryAddress", deliveryAddress);
      map.put("salesOrganization", salesOrganization);
      map.put("purchasingOrganization", purchasingOrganization);
      map.put("deliveryType", deliveryType);
      map.put("customerOrderNumber", customerOrderNumber);
      return map;
  }


}
