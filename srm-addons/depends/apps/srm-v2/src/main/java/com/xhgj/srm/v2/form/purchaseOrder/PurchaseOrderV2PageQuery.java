package com.xhgj.srm.v2.form.purchaseOrder;
import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.map.TypeAwareMap;
import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import com.xhgj.srm.unified.dto.BaseUnifiedForm;
import com.xhgj.srm.unified.dto.UnifiedForm;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * PurchaseOrderV2PageQuery
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PurchaseOrderV2PageQuery extends BaseSupplierOrderV2DTO implements BaseDefaultSearchSchemeForm, BaseUnifiedForm {

  /**
   * UnifiedForm
   */
  @ApiModelProperty(hidden = true)
  UnifiedForm unifiedForm;
  @ApiModelProperty("订单状态")
  private String orderStates;
  @ApiModelProperty("搜索方案 id")
  private String schemeId;
  @ApiModelProperty("创建时间 【开始】")
  private Long startCreateTime;
  @ApiModelProperty("创建时间 【结束】")
  private Long endCreateTime;
  @ApiModelProperty("查询未签收的订单")
  private Boolean selectUnReceipt;
  @ApiModelProperty("是否挂起")
  private Boolean pending;
  @ApiModelProperty("是否暂存")
  private Boolean staging;
  @ApiModelProperty("是否审核中")
  private Boolean unaudited;
  @ApiModelProperty("是否驳回")
  private Boolean reject;
  @ApiModelProperty("采购部门")
  private String purchaseDept;
  @ApiModelProperty("采购员")
  private String purchaseMan;
  @ApiModelProperty("单子是否赠品订单")
  private Boolean freeState;
  @ApiModelProperty("单子是否自采")
  private Boolean selfState;
  @ApiModelProperty(value = "修改时间范围【开始】")
  private Long startUpdateTime;
  @ApiModelProperty(value = "修改时间范围【结束】")
  private Long endUpdateTime;
  @ApiModelProperty("创建人")
  private String createMan;
  @ApiModelProperty("修改人")
  private String updateMan;
  @ApiModelProperty(value = "审核时间范围【开始】")
  private Long startAuditTime;
  @ApiModelProperty(value = "审核时间范围【结束】")
  private Long endAuditTime;
  @ApiModelProperty("订货数量操作符号")
  private LogicalOperatorsEnums orderNumOperators;
  @ApiModelProperty("订货数量")
  private BigDecimal orderNum;
  @ApiModelProperty("订货金额操作符号")
  private LogicalOperatorsEnums orderPriceOperators;
  @ApiModelProperty("订货金额")
  private BigDecimal orderPrice;
  @ApiModelProperty("是否走scp(全选) 1选中")
  private String allScp;
  @ApiModelProperty(value = "订单类型")
  private String orderType;
  @ApiModelProperty("是否亏本订单")
  private Boolean loss;
  /**
   * 用户id
   */
  @ApiModelProperty("用户id")
  private String userId;
  /**
   * 用户组
   */
  private String userGroup;

  /**
   * 分页参数 pageNo
   */
  private Integer pageNo;

  /**
   * 分页参数 pageSize
   */
  private Integer pageSize;

  public Integer getPageNo() {
    if (pageNo == null || pageNo < 1) {
      return 1;
    }
    return pageNo;
  }

  public Integer getPageSize() {
    if (pageSize == null || pageSize < 1) {
      pageSize = 25;
    }
    return pageSize;
  }

  public Map<String, Object> toQueryMap(MergeUserPermission searchPermission) {
    Map<String, Object> map = new TypeAwareMap<>();
    // orderCode
    map.put("orderCode", this.getOrderCode());
    // writeOffState
    map.put("writeOffState", this.getWriteOffState());
    // orderState
    if (this.getOrderState() != null) {
      map.put("orderState", this.getOrderState());
    }
    // pending
    map.put("pending", this.getPending());
    // staging
    map.put("staging", this.getStaging());
    // unaudited
    map.put("unaudited", this.getUnaudited());
    // reject
    map.put("reject", this.getReject());
    // startCreateTime
    map.put("startCreateTime", this.getStartCreateTime());
    // endCreateTime
    map.put("endCreateTime", this.getEndCreateTime());
    // supplierName
    map.put("supplierName", this.getSupplierName());
    // purchaseDept
    map.put("purchaseDept", this.getPurchaseDept());
    // purchaseMan
    map.put("purchaseMan", this.getPurchaseMan());
    // directShipment
    map.put("directShipment", this.getDirectShipment());
    // confirmState
    map.put("confirmState", this.getConfirmState());
    // cancelState
    map.put("cancelState", this.getCancelState());
    // returnState
    map.put("returnState", this.getReturnState());
    // refuseState
    map.put("refuseState", this.getRefuseState());
    // freeState
    map.put("freeState", this.getFreeState());
    // selfState
    map.put("selfState", this.getSelfState());
    // shipWaitStock
    map.put("shipWaitStock", this.getShipWaitStock());
    // purchaseGroupName
    map.put("purchaseGroupName", this.getPurchaseGroupName());
    // receiveMan
    map.put("receiveMan", this.getReceiveMan());
    // supplierOrderFormType
    if (Boolean.TRUE.equals(selectUnReceipt)) {
      map.put("supplierOrderFormType", SupplierOrderFormType.DELIVER.getKey());
      map.put("supplierOrderFormStatus", SupplierOrderFormStatus.WAIT_RECEIPT.getKey());
    }
    // supplierOpenInvoiceState
    map.put("supplierOpenInvoiceState", this.getSupplierOpenInvoiceState());
    // createMan
    map.put("createMan", this.getCreateMan());
    // salesOrderNo
    map.put("salesOrderNo", this.getSalesOrderNo());
    // largeTicketProjectName
    map.put("largeTicketProjectName", this.getLargeTicketProjectName());
    // largeTicketProjectNumbers
    map.put("largeTicketProjectNumbers", this.getLargeTicketProjectNumbers());
    // updateMan
    map.put("updateMan", this.getUpdateMan());
    // startUpdateTime
    map.put("startUpdateTime", this.getStartUpdateTime());
    // endUpdateTime
    map.put("endUpdateTime", this.getEndUpdateTime());
    // startAuditTime
    map.put("startAuditTime", this.getStartAuditTime());
    // endAuditTime
    map.put("endAuditTime", this.getEndAuditTime());
    // numOperators
    map.put("orderNumOperators", this.getOrderNumOperators());
    // orderNum
    map.put("orderNum", this.getOrderNum());
    // priceOperators
    map.put("orderPriceOperators", this.getOrderPriceOperators());
    // price
    map.put("orderPrice", this.getOrderPrice());
    // allScp
    map.put("allScp", this.getAllScp());
    //scp
    map.put("scp", this.getScp());
    // orderType
    map.put("orderType", this.getOrderType());
    // loss
    map.put("loss", this.getLoss());
    // userGroup
    map.put("userGroup", userGroup);
    map.put("pageNo", pageNo);
    map.put("pageSize", pageSize);
    // 统一搜索方案
    map.put("unifiedForm", unifiedForm);
    map.put("orderStates", orderStates);
    map.put("searchPermission", searchPermission);
    map.put("isViewAllOrganization", this.getIsViewAllOrganization());
    return map;
  }
}
