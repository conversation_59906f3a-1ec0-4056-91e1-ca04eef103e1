package com.xhgj.srm.v2.service.impl.purchaseOrder;/**
 * @since 2025/4/28 9:40
 */

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.v2.factory.SapV2Factory;
import com.xhgj.srm.v2.repository.SupplierOrderToFormV2Repository;
import com.xhgj.srm.v2.repository.SupplierOrderV2Repository;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.common.utils.FileUtils;
import com.xhgj.srm.common.utils.MissionUtil;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import com.xhgj.srm.v2.service.purchaseOrder.PurchaseOrderV2AdminService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.upload.util.OssUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class PurchaseOrderV2AdminServiceImpl implements PurchaseOrderV2AdminService {
  @Resource
  private SapV2Factory sapV2Factory;
  @Resource
  private SupplierOrderV2Repository supplierOrderV2Repository;
  @Resource
  private SupplierOrderToFormV2Repository supplierOrderToFormV2Repository;
  @Resource
  private PlatformTransactionManager transactionManager;

  @Resource
  private UserRepository userRepository;
  @Resource
  private OssUtil ossUtil;
  @Resource
  private MissionRepository missionRepository;
  @Resource
  private MissionUtil missionUtil;
  @Resource
  private MissionDispatcher missionDispatcher;


  @Override
  public void importPurchaseOrder(MultipartFile file, String userId) {
    User user =
        userRepository.findById(userId).orElseThrow(() -> new CheckException("用户不存在"));
    String savePath = null;
    try {
      savePath = this.saveExcel(file);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    // 新增任务
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        MissionTypeEnum.BATCH_TASK_IMPORT_PURCHASE_ORDER_V2.getTypeName(),
        user.getId(),
        Constants.PLATFORM_TYPE_AFTER,
        null,
        ""
    );
    Map<String, Object> mapParam = new HashMap<>(3);
    mapParam.put("filePath", savePath);
    mapParam.put("fileName", file.getOriginalFilename());
    mapParam.put("version", ShardingContext.getVersion());
    missionRepository.saveAndFlush(mission);
    missionDispatcher.doDispatch(mission.getId(), JSON.toJSONString(mapParam),
        MissionTypeEnum.BATCH_TASK_IMPORT_PURCHASE_ORDER_V2);
  }

  @Override
  public void inboundDelivery(MultipartFile file, String userId) {
    User user =
        userRepository.findById(userId).orElseThrow(() -> new CheckException("用户不存在"));
    String savePath = null;
    try {
      savePath = this.saveExcel(file);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    // 新增任务
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        MissionTypeEnum.BATCH_TASK_IMPORT_INITIAL_PURCHASE_ORDER_V2.getTypeName(),
        user.getId(),
        Constants.PLATFORM_TYPE_AFTER,
        null,
        ""
    );
    Map<String, Object> mapParam = new HashMap<>(3);
    mapParam.put("filePath", savePath);
    mapParam.put("fileName", file.getOriginalFilename());
    mapParam.put("version", ShardingContext.getVersion());
    missionRepository.saveAndFlush(mission);
    missionDispatcher.doDispatch(mission.getId(), JSON.toJSONString(mapParam),
        MissionTypeEnum.BATCH_TASK_IMPORT_INITIAL_PURCHASE_ORDER_V2);
  }


  @Override
  public void deleteAnomalyOrder(List<String> supplierOrderIds) {
    for (String supplierOrderId : supplierOrderIds) {
      // 开启事务
      TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
      try {
        SupplierOrderV2 supplierOrder =
            supplierOrderV2Repository.findById(supplierOrderId).orElseThrow(() -> new CheckException("采购订单不存在"));
        transactionTemplate.execute(status -> {
          sapV2Factory.delSupplierOrderInfo(supplierOrder.getId());
          // 找出入库单/退库单/取消单
          List<SupplierOrderToFormV2> orderToFormV2s =
              supplierOrderToFormV2Repository.findBySupplierOrderIdAndTypeInAndState(supplierOrderId,
                  Arrays.asList(SupplierOrderFormType.CANCEL.getType(),
                      SupplierOrderFormType.RETURN.getType(),
                      SupplierOrderFormType.WAREHOUSING.getType()), Constants.STATE_OK);
          for (SupplierOrderToFormV2 orderToFormV2 : orderToFormV2s) {
            // 删除入库单/退库单/取消单
            orderToFormV2.setState(Constants.STATE_DELETE);
          }
          supplierOrderToFormV2Repository.saveAll(orderToFormV2s);
          supplierOrder.setState(Constants.STATE_DELETE);
          supplierOrderV2Repository.save(supplierOrder);
          return null;
        });
      } catch (Exception e) {
        throw e;
      }
    }
  }
  @Override
  public void outboundDelivery(MultipartFile file, String userId) {
    User user =
        userRepository.findById(userId).orElseThrow(() -> new CheckException("用户不存在"));
    String savePath = null;
    try {
      savePath = this.saveExcel(file);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    // 新增任务
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        MissionTypeEnum.BATCH_TASK_IMPORT_RETURN_STORAGE_ORDER_V2.getTypeName(),
        user.getId(),
        Constants.PLATFORM_TYPE_AFTER,
        null,
        ""
    );
    Map<String, Object> mapParam = new HashMap<>(3);
    mapParam.put("filePath", savePath);
    mapParam.put("fileName", file.getOriginalFilename());
    mapParam.put("version", ShardingContext.getVersion());
    missionRepository.saveAndFlush(mission);
    missionDispatcher.doDispatch(mission.getId(), JSON.toJSONString(mapParam),
        MissionTypeEnum.BATCH_TASK_IMPORT_RETURN_STORAGE_ORDER_V2);
  }

  /** 将需要导入的 excel 保存 */
  public String saveExcel(MultipartFile file) throws IOException {
    if (file != null) {
      String fileName = file.getOriginalFilename();
      if (fileName == null || fileName.isEmpty()) {
        throw new CheckException("文件异常,请查看文件");
      }
      // 导入文件上传至 OSS
      String fileUrl =
          ossUtil.putOneFile(
              file.getInputStream(),
              FileUtils.getFileNameWithInsertStr(
                  file.getOriginalFilename(), String.valueOf(System.currentTimeMillis())),
              Constants_Batch.UPLOAD_FILE_DIR_TASK_IMPORT
                  + DateUtils.formatTimeStampToPureDate(System.currentTimeMillis()));
      if (StringUtils.isNullOrEmpty(fileUrl)) {
        throw new CheckException("文件上传异常！");
      }
      return fileUrl;
    } else {
      throw new CheckException("无上传文件");
    }
  }
}
