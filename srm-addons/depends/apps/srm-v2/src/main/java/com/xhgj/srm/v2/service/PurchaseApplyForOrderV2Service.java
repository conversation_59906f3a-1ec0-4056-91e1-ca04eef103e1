package com.xhgj.srm.v2.service;/**
 * @since 2025/4/17 17:52
 */

import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.purchaseApplyForOrder.PurchaseApplyRecordStatus;
import com.xhgj.srm.jpa.dto.purchase.apply.PurchaseApplyStatistics;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.v2.dto.PurchaseApplyRecordDTO;
import com.xhgj.srm.v2.vo.ComponentDetailVo;
import com.xhgj.srm.v2.form.*;
import com.xhgj.srm.v2.vo.PurchaseApplyForOrderV2ListExtVO;
import com.xhgj.srm.v2.vo.PurchaseApplyForOrderV2ListVO;
import com.xhiot.boot.mvc.base.PageResult;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/4/17 17:52:30
 *@description
 */
public interface PurchaseApplyForOrderV2Service {
  /**
   * 分页查询采购申请订单
   * @param form 列表数据筛选对象
   */
  PageResult<PurchaseApplyForOrderV2ListVO> findPage(PurchaseApplyForOrderV2QueryForm form);

  /**
   * 采购申请单列表关联查询
   * @param form
   * @return
   */
  PageResult<PurchaseApplyForOrderV2ListExtVO> findPageLink(PurchaseApplyForOrderV2QueryLinkForm form);


  /**
   * 获取采购申请单详情
   * @param id 采购申请单id
   */
  PurchaseApplyForOrderV2ListVO findPurchaseApplyForOrderDetails(String id);

  /**
   * 导出采购申请
   * @param form
   * @param user
   */
  void exportPurchaseApplyForOrder(PurchaseApplyForOrderV2ExportForm form, User user);

  /**
   * 采购申请表头字段筛选
   * @param form
   * @return
   */
  List<String> getApplyListByTableHeader(PurchaseOrderTableHeaderV2Params form);

  /**
   * SAP新增/修改 采购申请订单
   * @param forms
   */
  void addPurchaseApplyForOrder(List<PurchaseApplyForOrderV2AddForm> forms);

  /**
   * SRM修改采购申请单
   */
  void updatePurchaseApplyForOrder(PurchaseApplyForOrderV2UpdateForm form);

  /**
   * 审核
   * @param approvalResult
   * @param status
   */
  void audit(ApprovalResult approvalResult, PurchaseApplyRecordStatus status);

  PurchaseApplyStatistics statistics(PurchaseApplyForOrderV2QueryForm param);

  byte[] downloadPurchaseApplyOrderAcceptTemp(PurchaseApplyOrderV2DownloadParams params);

  byte[] printOutPurchaseApplyOrderAcceptTemp(PurchaseApplyOrderV2DownloadParams params);

  void updatePrintsNumber(PurchaseApplyOrderV2DownloadParams params);

  void batchPrintOutPurchaseApplyOrderAcceptTemp(PurchaseApplyOrderV2DownloadParams params);

  PageResult<PurchaseApplyRecordDTO> getPurchaseApplyRecordDetail(String id,Integer pageNo,Integer pageSize);

  /**
   * 取消采购申请单
   * @param param
   * @return
   */
  Boolean cancelPurchaseApply(CancelPurchaseV2Param param);

  /**
   * 反取消采购申请单
   * @param param
   * @return
   */
  Boolean oppositeCancel(CancelPurchaseV2Param param);

  /**
   * 获取组件清单详情
   * @param id 采购申请单id
   * @return
   */
  ComponentDetailVo getComponentList(String id);

  /**
   * 校验当前用户的修改采购员权限
   * @param purchaseApplyOrderIds
   * @param user
   */
  void checkPurchaser(List<String> purchaseApplyOrderIds, User user);

  /**
   * 更新采购申请单 for admin
   * @param form
   * @param user
   */
  void updatePurchaseApplyForOrderAdmin(PurchaseApplyForOrderV2UpdateAdminForm form, User user);

  /**
   * 获取采购员列表
   * @param name
   * @param applyType
   * @return
   */
  List<String> getPurchaseMansByName(String name, @NotBlank String applyType);
}
