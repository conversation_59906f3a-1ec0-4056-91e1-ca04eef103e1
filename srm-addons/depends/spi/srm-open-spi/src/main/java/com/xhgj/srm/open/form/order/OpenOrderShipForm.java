package com.xhgj.srm.open.form.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class OpenOrderShipForm {
  /**
   * 订单id
   */
  @ApiModelProperty(value = "订单id", required = true)
  @NotBlank(message = "订单id不能为空")
  private String orderId;

  /**
   * 物流公司
   */
  @ApiModelProperty(value = "物流公司", required = true)
  @NotBlank(message = "物流公司不能为空！")
  private String expressCompany;

  /**
   * 物流公司编码
   */
  @ApiModelProperty(value = "物流公司编码", required = true)
  @NotBlank(message = "物流公司编码不能为空！")
  private String expressCode;

  /**
   * 物流单号
   */
  @ApiModelProperty(value = "物流单号")
  @NotBlank(message = "物流单号不能为空！")
  private String expressNo;

  /**
   * 发货详情
   */
  @ApiModelProperty(value = "发货详情")
  @Valid
  private List<OpenOrderShipFormDetail> shipFormDetails;

  @Data
  public static class OpenOrderShipFormDetail{
    @ApiModelProperty("物料编码")
    private String code;

    /**
     * 发货数量大于0
     */
    @ApiModelProperty("发货数量")
    @DecimalMin(value = "0.00", message = "发货数量必须大于0")
    private BigDecimal delCount;
  }
}
