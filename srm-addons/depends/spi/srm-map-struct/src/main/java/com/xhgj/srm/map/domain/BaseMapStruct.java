package com.xhgj.srm.map.domain;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 基础映射接口
 * 使用此工具需要注意：
 * 1.类需要有默认的空构造方法
 * 2.同名的都会尝试转换
 * 3.某些转换不通过的会直接编译报错，需要使用ignore进行忽略或其他办法处理
 */
public interface BaseMapStruct {

  /**
   * 默认转换类型复写 string -> long
   * @param str
   * @return
   */
  default Long stringToLong(String str) {
    // 可以返回 null 或者抛出异常
    if (StrUtil.isBlank(str)) {
      return null;
    }
    // 判断如果是数字类型则转换，否则返回 null
    if (NumberUtil.isNumber(str)) {
      return Convert.toLong(str);
    }
    return null;
  }

  /**
   * 默认转换类型复写 string -> BigDecimal
   * @param str
   * @return
   */
  default BigDecimal stringToBigdecimal(String str) {
    // 可以返回 null 或者抛出异常
    if (StrUtil.isBlank(str)) {
      return null;
    }
    // 判断如果是数字类型则转换，否则返回 null
    if (NumberUtil.isNumber(str)) {
      return Convert.toBigDecimal(str);
    }
    return null;
  }

  /**
   * 默认转换类型复写 string -> int
   */
  default Integer stringToInt(String str) {
    // 可以返回 null 或者抛出异常
    if (StrUtil.isBlank(str)) {
      return null;
    }
    // 判断如果是数字类型则转换，否则返回 null
    if (NumberUtil.isNumber(str)) {
      return Convert.toInt(str);
    }
    return null;
  }

  /**
   * 默认转换类型复写 string -> double
   */
  default Double stringToDouble(String str) {
    // 可以返回 null 或者抛出异常
    if (StrUtil.isBlank(str)) {
      return null;
    }
    // 判断如果是数字类型则转换，否则返回 null
    if (NumberUtil.isNumber(str)) {
      return Convert.toDouble(str);
    }
    return null;
  }

  /**
   * 默认转换类型复写 string -> float
   */
  default Float stringToFloat(String str) {
    // 可以返回 null 或者抛出异常
    if (StrUtil.isBlank(str)) {
      return null;
    }
    // 判断如果是数字类型则转换，否则返回 null
    if (NumberUtil.isNumber(str)) {
      return Convert.toFloat(str);
    }
    return null;
  }

  /**
   * 默认转换类型复写 string -> short
   */
  default Short stringToShort(String str) {
    // 可以返回 null 或者抛出异常
    if (StrUtil.isBlank(str)) {
      return null;
    }
    // 判断如果是数字类型则转换，否则返回 null
    if (NumberUtil.isNumber(str)) {
      return Convert.toShort(str);
    }
    return null;
  }

  /**
   * 默认转换类型复写 string -> byte
   */
  default Byte stringToByte(String str) {
    // 可以返回 null 或者抛出异常
    if (StrUtil.isBlank(str)) {
      return null;
    }
    // 判断如果是数字类型则转换，否则返回 null
    if (NumberUtil.isNumber(str)) {
      return Convert.toByte(str);
    }
    return null;
  }

  /**
   * 默认转换类型复写 string -> boolean
   */
  default Boolean stringToBoolean(String str) {
    if (str.equals("1") || str.equals("true")) {
      return true;
    }
    if (str.equals("0") || str.equals("false")) {
      return false;
    }
    return null;
  }

  default Date Long2Date(Long value) {
    if (value == null) {
      return null;
    }
    // 判断如果是数字类型则转换，否则返回 null
    return new Date(value);
  }
  /**
   * Date -> Long
   */
  default Long dateToLong(Date date) {
    return date != null ? date.getTime() : null;
  }
}
