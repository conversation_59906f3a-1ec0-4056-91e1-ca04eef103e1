<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>srm-addons</artifactId>
    <groupId>com.xhgj</groupId>
    <version>3.0.0-SNAPSHOT</version>
    <relativePath>../../../pom.xml</relativePath>
  </parent>
  <version>3.0.0-SNAPSHOT</version>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>srm-map-struct</artifactId>
  <properties>
    <mapstruct.version>1.5.5.Final</mapstruct.version>
  </properties>
  <dependencies>
    <!--  mapstruct-->
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
    </dependency>
    <!--    实现注解编译生成实现类 -->
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct-processor</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>
</project>