# 数据库初始化

## 表结构设计

## 初始化 sql

```mysql

create table t_apply (
    id varchar(32) not null, 
    c_relationId varchar(32), 
    c_userId varchar(32), 
    c_reason varchar(200), 
    c_description text, 
    c_operation varchar(50),
    c_operaType varchar(2),
    c_source varchar(2),
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);

create table t_brand (
    id varchar(32) not null, 
    supplierId varchar(32), 
    supplierFbId varchar(32), 
    c_brandMdmId varchar(32), 
    c_brandname_en varchar(50), 
    c_brandname_cn varchar(50), 
    c_logoUrl varchar(50), 
    c_checkState varchar(2), 
    c_remark text, 
    c_reason text, 
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);

create table t_check (
    id varchar(32) not null, 
    c_preRelationId varchar(32), 
    c_relationId varchar(32), 
    c_userId varchar(32), 
    c_description varchar(200), 
    c_operation varchar(50), 
    c_operaType varchar(2), 
    c_resource varchar(2), 
    c_agreeResource varchar(2), 
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);

create table t_contact (
    id varchar(32) not null, 
    supplierId varchar(32), 
    supplierFbId varchar(32), 
    c_createMan varchar(32), 
    c_name varchar(20), 
    c_phone varchar(20), 
    c_tel varchar(20), 
    c_sex varchar(2), 
    c_duty varchar(200), 
    c_mail varchar(50), 
    c_fax varchar(20), 
    c_area text, 
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);

create table t_contract (
    id varchar(32) not null, 
    supplierId varchar(32), 
    c_code varchar(100), 
    c_contractNum varchar(100), 
    c_supplierName varchar(100), 
    c_contractMoney varchar(100), 
    c_contractTime bigint, 
    c_isFile varchar(2), 
    c_uploadTime bigint, 
    c_createUser varchar(50), 
    c_createManId varchar(50), 
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);

create table t_expand (
    id varchar(32) not null, 
    productId varchar(32), 
    productFbId varchar(32), 
    c_attrid varchar(32), 
    c_attrkey varchar(100), 
    c_attrvalue varchar(100), 
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);

create table t_extrafile (
    id varchar(32) not null, 
    c_relationName varchar(50),
    c_relationId varchar(50), 
    c_name varchar(50), 
    c_url varchar(100), 
    c_type varchar(2), 
    c_createTime bigint, 
    c_state varchar(2), 
    c_description text, 
    primary key (id)
);

create table t_file (
    id varchar(32) not null, 
    c_relationType varchar(2), 
    c_relationId varchar(50), 
    c_name varchar(50), 
    c_url varchar(100), 
    c_type varchar(2), 
    c_createTime bigint, 
    c_sourceType varchar(2), 
    c_state varchar(2), 
    c_description text, 
    primary key (id)
);

create table t_inquiry (
    id varchar(32) not null, 
    supplierId varchar(32), 
    c_enterpriseName varchar(100), 
    c_name varchar(100), 
    c_brandname_en varchar(50), 
    c_brandname_cn varchar(50), 
    c_model varchar(50), 
    c_marketPrice varchar(50), 
    c_salesPrice varchar(50), 
    c_offererName varchar(20), 
    c_offererPhone varchar(20), 
    c_inquirerTime bigint, 
    c_inquirer varchar(20), 
    c_remark text, 
    c_des text, 
    c_createTime bigint, 
    c_state varchar(2), 
    c_transferPrice varchar(50), 
    c_salesMan varchar(50), 
    c_createMan varchar(32), 
    c_createCode varchar(100), 
    c_num varchar(20), 
    c_unit varchar(20), 
    primary key (id)
);

create table t_logininfo (
    id varchar(32) not null, 
    c_loginUserId varchar(32), 
    c_logintype varchar(2), 
    c_loginPlatform varchar(2), 
    c_ip varchar(20), 
    c_description varchar(200), 
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);

create table t_m_mobilecode (
    id varchar(32) not null, 
    c_mobile varchar(11), 
    c_mail varchar(50), 
    c_code varchar(10),
    c_codetime bigint, 
    c_type varchar(1), 
    c_des text, 
    c_createtime bigint, 
    c_sendtime bigint, 
    c_state varchar(2), 
    c_sendresult varchar(10), 
    primary key (id)
);

create table t_meeting (
    id varchar(32) not null, 
    supplierId varchar(32), 
    c_name varchar(100), 
    c_place varchar(50), 
    c_personnel text, 
    c_depart varchar(200), 
    c_starttime bigint, 
    c_endtime bigint, 
    c_meettime bigint, 
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);

create table t_product (
    id varchar(32) not null, 
    supplierId varchar(32), 
    c_productMdmId varchar(32), 
    c_brandMdmId varchar(32), 
    c_brandname_en varchar(50), 
    c_brandname_cn varchar(50), 
    c_firstCateMdmId varchar(32), 
    c_secondCateMdmId varchar(32), 
    c_thirdCateMdmId varchar(32), 
    c_fourthCateMdmId varchar(32), 
    c_fourthCateName varchar(100), 
    c_name varchar(100), 
    c_code varchar(20), 
    c_marketPrice double precision, 
    c_purchasePrice double precision, 
    c_model varchar(50), 
    c_basicUnit varchar(10), 
    c_barCode varchar(50), 
    c_grossWeight varchar(10), 
    c_netWeight varchar(10), 
    c_length varchar(10), 
    c_width varchar(10), 
    c_height varchar(10), 
    c_volume varchar(200), 
    c_info text, 
    c_resource varchar(2), 
    c_remark text, 
    c_createTime bigint, 
    c_checkState varchar(2), 
    c_applyState varchar(2), 
    c_state varchar(2), 
    c_checkId varchar(32), 
    primary key (id)
);

create table t_product_fb (
    id varchar(32) not null, 
    productId varchar(32), 
    c_productMdmId varchar(32), 
    c_brandMdmId varchar(32), 
    c_brandname_en varchar(50), 
    c_brandname_cn varchar(50), 
    c_firstCateMdmId varchar(32), 
    c_secondCateMdmId varchar(32), 
    c_thirdCateMdmId varchar(32), 
    c_fourthCateMdmId varchar(32), 
    c_fourthCateName varchar(100), 
    c_name varchar(100), 
    c_code varchar(20), 
    c_marketPrice double precision, 
    c_purchasePrice double precision, 
    c_model varchar(50), 
    c_basicUnit varchar(10), 
    c_barCode varchar(50), 
    c_grossWeight varchar(10), 
    c_netWeight varchar(10), 
    c_length varchar(10), 
    c_width varchar(10), 
    c_height varchar(10), 
    c_volume varchar(10), 
    c_info text, 
    c_resource varchar(2), 
    c_remark text, 
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);

create table t_supplier (
    id varchar(32) not null, 
    c_name varchar(50), 
    c_password varchar(100), 
    c_mobile varchar(50), 
    c_enterpriseName varchar(100), 
    c_enterpriseNature varchar(100), 
    c_enterpriseLevel varchar(2), 
    c_industry varchar(100), 
    c_createGroup varchar(100), 
    c_createCode varchar(100), 
    c_useGroup text, 
    c_useCode text, 
    c_erpCode varchar(100), 
    c_erpid varchar(100), 
    c_code varchar(100), 
    c_country varchar(32), 
    c_province varchar(32), 
    c_city varchar(32), 
    c_date bigint, 
    c_license varchar(32), 
    c_corporate varchar(32), 
    c_uscc varchar(32), 
    c_regCapital varchar(32), 
    c_regAddress varchar(100), 
    c_regNo varchar(32), 
    c_details text, 
    c_bankName varchar(50), 
    c_bankNum varchar(50), 
    c_bankAccount varchar(50), 
    c_bankCode varchar(50), 
    c_licenseUrl varchar(200), 
    c_integrity varchar(10), 
    c_shieldingPeople varchar(32), 
    c_reason text, c_remark text, 
    c_type varchar(2), 
    c_purchaserName varchar(50), 
    c_purchaserId varchar(32), 
    c_departName varchar(50), 
    c_departId varchar(32), 
    c_manageId varchar(32), 
    c_editManageId varchar(32), 
    c_createMan varchar(32), 
    c_createTime bigint, 
    c_auditState varchar(2), 
    c_applyState varchar(2), 
    c_agreeCheckState varchar(2), 
    c_agreeApplyState varchar(2), 
    c_agreeResource varchar(2), 
    c_agreeType varchar(2), 
    c_erpSuccess varchar(2), 
    c_supType varchar(2), 
    c_dutyParagraph varchar(2), 
    c_settleCurrency varchar(20), 
    c_taxNo varchar(50), 
    c_invoiceType varchar(2), 
    c_taxRate varchar(50), 
    c_swiftcode varchar(50), 
    c_editManId varchar(32), 
    c_iscancel varchar(32), 
    c_state varchar(2), 
    primary key (id)
);

create table t_supplier_fb (
    id varchar(32) not null, 
    supplierId varchar(32), 
    supplierFbId varchar(32), 
    c_enterpriseName varchar(100), 
    c_enterpriseNature varchar(100), 
    c_enterpriseLevel varchar(2), 
    c_mobile varchar(50), 
    c_industry varchar(100), 
    c_createGroup varchar(100), 
    c_createCode varchar(100), 
    c_useGroup text, c_useCode text, 
    c_erpCode varchar(100), 
    c_erpid varchar(100), 
    c_code varchar(100), 
    c_country varchar(32), 
    c_province varchar(32), 
    c_city varchar(32), 
    c_date bigint, 
    c_license varchar(32), 
    c_corporate varchar(32), 
    c_uscc  varchar(32), 
    c_regCapital varchar(32), 
    c_regAddress varchar(100), 
    c_regNo varchar(32), 
    c_details text, 
    c_bankName varchar(50), 
    c_bankNum varchar(50), 
    c_bankCode varchar(50), 
    c_bankAccount varchar(50), 
    c_licenseUrl varchar(200), 
    c_integrity varchar(10), 
    c_remark text, 
    c_settleCurrency varchar(20), 
    c_taxNo varchar(50), 
    c_invoiceType varchar(2), 
    c_taxRate varchar(50), 
    c_type varchar(2), 
    c_purchaserName varchar(50), 
    c_purchaserId varchar(32), 
    c_departName varchar(50), 
    c_departId varchar(32), 
    c_resource varchar(2), 
    c_agreeResource varchar(2), 
    c_agreeType varchar(2), 
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);

create table t_group (
    id varchar(32) not null, 
    c_name varchar(50), 
    c_mdmId varchar(50), 
    c_erpCode varchar(50), 
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);

create table t_user (
    id varchar(32) not null, 
    groupId varchar(32), 
    c_erpId varchar(32), 
    c_code varchar(50), 
    c_realname varchar(50), 
    c_mobile varchar(20), 
    c_duty varchar(2), 
    c_usergroup varchar(50), 
    c_groupErpCode varchar(32), 
    c_depart varchar(50), 
    c_departCode varchar(50), 
    c_departErpId varchar(32), 
    c_name varchar(20), 
    c_password varchar(50), 
    c_role varchar(2), 
    c_groupDistribute varchar(2), 
    c_isManageAbroad varchar(2), 
    c_createTime bigint, 
    c_state varchar(2), 
    primary key (id)
);


alter table t_brand add index FKA00987FC44A903C3 (supplierFbId), add constraint FKA00987FC44A903C3 foreign key (supplierFbId) references t_supplier_fb (id);
alter table t_brand add index FKA00987FC1561F12B (supplierId), add constraint FKA00987FC1561F12B foreign key (supplierId) references t_supplier (id);
alter table t_contact add index FKF449175544A903C3 (supplierFbId), add constraint FKF449175544A903C3 foreign key (supplierFbId) references t_supplier_fb (id);
alter table t_contact add index FKF44917551561F12B (supplierId), add constraint FKF44917551561F12B foreign key (supplierId) references t_supplier (id);
alter table t_contract add index FK94E1847D1561F12B (supplierId), add constraint FK94E1847D1561F12B foreign key (supplierId) references t_supplier (id);
alter table t_expand add index FK66A130C5EDEAA4C1 (productId), add constraint FK66A130C5EDEAA4C1 foreign key (productId) references t_product (id);
alter table t_expand add index FK66A130C51DD329D9 (productFbId), add constraint FK66A130C51DD329D9 foreign key (productFbId) references t_product_fb (id);
alter table t_inquiry add index FK30247CFC1561F12B (supplierId), add constraint FK30247CFC1561F12B foreign key (supplierId) references t_supplier (id);
alter table t_meeting add index FKF3B83D701561F12B (supplierId), add constraint FKF3B83D701561F12B foreign key (supplierId) references t_supplier (id);
alter table t_product add index FKA91FC0241561F12B (supplierId), add constraint FKA91FC0241561F12B foreign key (supplierId) references t_supplier (id);
alter table t_product_fb add index FK25DA0EB7EDEAA4C1 (productId), add constraint FK25DA0EB7EDEAA4C1 foreign key (productId) references t_product (id);
alter table t_supplier_fb add index FKD11EE4C444A903C3 (supplierFbId), add constraint FKD11EE4C444A903C3 foreign key (supplierFbId) references t_supplier_fb (id);
alter table t_supplier_fb add index FKD11EE4C41561F12B (supplierId), add constraint FKD11EE4C41561F12B foreign key (supplierId) references t_supplier (id);
alter table t_user add index FKCB63CCB65D7E95E1 (groupId), add constraint FKCB63CCB65D7E95E1 foreign key (groupId) references t_group (id);


##V3.0.0新增

#用户表新增
ALTER TABLE t_user
    ADD COLUMN c_mail VARCHAR(50) DEFAULT NULL COMMENT '邮箱';
ALTER TABLE t_user
    ADD COLUMN c_meetingMails VARCHAR(500) COMMENT '会议发送邮箱';
ALTER TABLE t_user
    ADD COLUMN c_inquiryRank VARCHAR(10) COMMENT '询价排行';
ALTER TABLE t_user
    ADD COLUMN c_mdmId VARCHAR(32) COMMENT 'OA人员id';
UPDATE t_user set c_inquiryRank = 0;

#组织表新增
ALTER TABLE t_group
    ADD COLUMN groupId VARCHAR(32) COMMENT '所属群组id';
ALTER TABLE t_group
    ADD COLUMN c_code VARCHAR(20) DEFAULT NULL COMMENT 'SRM编码';
ALTER TABLE t_group
    ADD COLUMN c_groupCode VARCHAR(20) DEFAULT NULL COMMENT '所属组织编码';
ALTER TABLE t_group
    ADD COLUMN c_type VARCHAR(1) DEFAULT NULL COMMENT '群组类型';
ALTER TABLE t_group
    ADD COLUMN c_createMan VARCHAR(32) DEFAULT NULL COMMENT '创建人';
ALTER TABLE t_group
    ADD COLUMN c_parentIds VARCHAR(1000) DEFAULT NULL COMMENT '父部门ids';

alter table t_group add constraint FK_group_group foreign key (groupId)
       references t_group (id) on delete restrict on update restrict;

#商品表新增
ALTER TABLE t_product
    ADD COLUMN c_tempCode VARCHAR(20) COMMENT '临时编码';
ALTER TABLE t_product
    ADD COLUMN c_owner VARCHAR(20) COMMENT '货主';
ALTER TABLE t_product
    ADD COLUMN c_deliveryDate VARCHAR(10) COMMENT '发货期';
ALTER TABLE t_product
    ADD COLUMN c_isFreeShip VARCHAR(1) COMMENT '是否包邮';
ALTER TABLE t_product
    ADD COLUMN c_des VARCHAR(2000) COMMENT '描述';
ALTER TABLE t_product
    ADD COLUMN c_mainUrl VARCHAR(200) COMMENT '主图路径';
ALTER TABLE t_product
    ADD COLUMN c_saleState VARCHAR(1) COMMENT '在售状态';
ALTER TABLE t_product
    ADD COLUMN c_batchNumber VARCHAR(20) COMMENT '批次号';
ALTER TABLE t_product
    ADD COLUMN c_unitName VARCHAR(10) COMMENT '单位名称';
ALTER TABLE t_product
    ADD COLUMN c_ownerName VARCHAR(50) COMMENT '货主名称';
ALTER TABLE t_product
    ADD COLUMN c_ossConnect VARCHAR(1) COMMENT '是否关联oss';

#供应商表新增
ALTER TABLE t_supplier
    ADD COLUMN c_manageType VARCHAR(31) COMMENT '经营状态';
ALTER TABLE t_supplier
    ADD COLUMN c_startDate bigint default 0 COMMENT '营业起始时间';
ALTER TABLE t_supplier
    ADD COLUMN c_endDate  bigint default 0 COMMENT '营业结束时间';
ALTER TABLE t_supplier
    ADD COLUMN c_paidCapital VARCHAR(50) COMMENT '实缴资本';
ALTER TABLE t_supplier
    ADD COLUMN c_insNum VARCHAR(10) COMMENT '参保人数';
ALTER TABLE t_supplier
    ADD COLUMN c_taxNumber VARCHAR(20) COMMENT '纳税人识别号';
ALTER TABLE t_supplier
    ADD COLUMN c_taxQualification VARCHAR(200) COMMENT '纳税人资质';
ALTER TABLE t_supplier
    ADD COLUMN c_usedName VARCHAR(50) COMMENT '曾用名';
ALTER TABLE t_supplier
    ADD COLUMN c_regAuthority VARCHAR(50) COMMENT '登记机关';
ALTER TABLE t_supplier
    ADD COLUMN c_orgCode VARCHAR(30) COMMENT '组织机构代码';
ALTER TABLE t_supplier
    ADD COLUMN c_peopleNum VARCHAR(10) COMMENT '人员规模';
ALTER TABLE t_supplier
    ADD COLUMN c_businessScope text COMMENT '经营范围';
ALTER TABLE t_supplier
    ADD COLUMN c_englishName VARCHAR(200) COMMENT '英文名';
ALTER TABLE t_supplier
    ADD COLUMN c_synState VARCHAR(1) COMMENT '供应商同步状态';
ALTER TABLE t_supplier
    ADD COLUMN c_isOpen VARCHAR(1) COMMENT '是否开通前台使用';
ALTER TABLE t_supplier
    ADD COLUMN c_isFromSupplier VARCHAR(1) COMMENT '是否由供应商修改';
ALTER TABLE t_supplier
    ADD COLUMN c_editTime bigint default 0  COMMENT '修改时间';

#供应商副本表新增
ALTER TABLE t_supplier_fb
    ADD COLUMN c_manageType VARCHAR(31) COMMENT '经营状态';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_startDate bigint default 0 COMMENT '营业起始时间';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_endDate  bigint default 0 COMMENT '营业结束时间';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_paidCapital VARCHAR(50) COMMENT '实缴资本';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_insNum VARCHAR(10) COMMENT '参保人数';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_taxNumber VARCHAR(20) COMMENT '纳税人识别号';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_taxQualification VARCHAR(200) COMMENT '纳税人资质';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_usedName VARCHAR(50) COMMENT '曾用名';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_regAuthority VARCHAR(50) COMMENT '登记机关';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_orgCode VARCHAR(30) COMMENT '组织机构代码';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_peopleNum VARCHAR(10) COMMENT '人员规模';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_businessScope text COMMENT '经营范围';
ALTER TABLE t_supplier_fb
    ADD COLUMN c_englishName VARCHAR(200) COMMENT '英文名';

#品牌表新增
ALTER TABLE t_brand
    ADD COLUMN c_manageType VARCHAR(1) COMMENT '经营形式';
ALTER TABLE t_brand
    ADD COLUMN c_isPermission VARCHAR(1) COMMENT '是否已授权';

#审核表新增
ALTER TABLE t_check
    ADD COLUMN c_checkMan VARCHAR(10) COMMENT '审核人姓名';
ALTER TABLE t_check
    ADD COLUMN  c_readTime  bigint(20) COMMENT '阅读时间';

#会议表新增
ALTER TABLE t_meeting
    ADD COLUMN c_createMan VARCHAR(32) COMMENT '会议创建人';


#用户关系审核表
create table t_user_check 
(
   id                  varchar(32) not null comment 'id',
   userId              varchar(32) comment '用户id',
   c_type              varchar(1)  comment '审核类型',
   c_supplierType      varchar(1)  comment '审核供应商类型',
   c_userErpCode       varchar(20) comment '审核人erp编码',
   c_createTime        bigint  default 0 comment '创建时间',
   c_state             varchar(1)  comment '状态',
   primary key (id)

)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '用户关系审核表';

alter table t_user_check add constraint FK_user_usercheck foreign key (userId)
       references t_user (id) on delete restrict on update restrict;

#企业信息表
create table t_enterprise 
(
   id                   varchar(32)  not null comment 'id',
   c_regNumber          varchar(31)  comment '注册号',
   c_regStatus          varchar(31)  comment '经营状态',                   
   c_creditCode         varchar(255) comment '统一社会信用代码',                  
   c_estiblishTime      varchar(50)  comment '成立日期',                   
   c_regCapital         varchar(50)  comment '注册资本',                    
   c_companyType        varchar(1)   comment '公司类型',                    
   c_name               varchar(50)  comment '公司名',                    
   c_enterpriseId       varchar(20)  comment '公司id',                    
   c_orgNumber          varchar(31)  comment '组织机构代码',                    
   c_type               varchar(1)   comment '类型',                    
   c_base               varchar(50)  comment '省份',                    
   c_legalPersonName    varchar(50)  comment '法人',                    
   c_detailJson         text         comment '详情信息',          
   c_createTime         bigint default 0  comment '创建时间',  
   primary key (id)
)
 ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '企业信息表';

#供应商账号信息
create table t_supplier_user 
(
   id                  varchar(32) not null comment 'id',
   supplierId          varchar(32) comment '供应商id',
   c_name              varchar(20) comment '用户名',
   c_password          varchar(50) comment '密码',
   c_realName          varchar(10) comment '姓名',
   c_mobile            varchar(11) comment '联系方式',
   c_mail              varchar(50) comment '邮箱',
   c_role              varchar(1)  comment '角色',
   c_createTime        bigint  default 0 comment '创建时间',
   c_state             varchar(1)  comment '状态',
   primary key (id)

)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8
    COMMENT '供应商账号信息表';

alter table t_supplier_user add constraint FK_supplier_supplieruser foreign key (supplierId)
       references t_supplier (id) on delete restrict on update restrict;


#供应商信息变更表
create table t_supplier_changeinfo 
(
   id                   varchar(32)  comment 'id' not null ,
   supplierId           varchar(32)  comment '供应商id',
   supplierFbId         varchar(32)  comment '供应商副本id',
   c_changeField        varchar(20)  comment '变更字段',
   c_beforeInfo         text         comment '变更前内容',
   c_afterInfo          text         comment '变更后内容',
   c_checkState         varchar(1)   comment '审核状态',
   c_mes                varchar(200) comment '审核说明',
   c_createTime         bigint default 0 comment '创建时间',
   c_state              varchar(1)   comment '状态',
   primary key (id)
)
    ENGINE = InnoDB
     DEFAULT CHARSET = utf8
     COMMENT '供应商信息变更表';
alter table t_supplier_changeinfo add constraint FK_supplier_supplierchangeinfo foreign key (supplierId)
       references t_supplier (id) on delete restrict on update restrict;
alter table t_supplier_changeinfo add constraint FK_supplierfb_supplierchangeinfo foreign key (supplierFbId)
       references t_supplier_fb (id) on delete restrict on update restrict;


#供应商财务信息
create table t_financial 
(
   id                  varchar(32)  comment 'id' not null,
   supplierId          varchar(32)  comment '供应商id',
   supplierFbId        varchar(32)  comment '供应商副本id',
   c_bankName          varchar(200)  comment '开户行',
   c_bankNum           varchar(50)  comment '银行账号',
   c_accountUrl        varchar(500) comment '开户许可证',
   c_bankAccount       varchar(50)  comment '账户名称',
   c_bankAddress       varchar(500) comment '开户行地址',
   c_bankCode          varchar(30)  comment '行号',
   c_taxNo             varchar(30)  comment '税务登记号',
   c_invoiceType       varchar(2)   comment '发票类型',
   c_taxRate           varchar(20)  comment '默认税率',
   c_settleCurrency    varchar(20)  comment '结算币别',
   c_swiftCode         varchar(50)  comment 'swiftcode',
   c_createTime        bigint default 0 comment '创建时间',
   c_state             varchar(1)   comment '状态',
   primary key (id)
)
    ENGINE = InnoDB
      DEFAULT CHARSET = utf8
      COMMENT '供应商财务信息表';

alter table t_financial add constraint FK_financial_supplier foreign key (supplierId)
       references t_supplier (id) on delete restrict on update restrict;
alter table t_financial add constraint FK_financial_supplierfb foreign key (supplierFbId)
       references t_supplier_fb (id) on delete restrict on update restrict;

#检索方案表
create table t_search_scheme 
(
   id                  varchar(32)  comment 'id' not null,
   c_type              varchar(2)  comment '检索方案类型',
   c_name              varchar(50)  comment '方案名称',
   c_content           varchar(1000)  comment '方案内容',
   c_isDefault         varchar(1)  comment '是否默认',
   c_createMan         varchar(32)  comment '创建人',
   c_createTime        bigint default 0 comment '创建时间',
   c_state             varchar(1)   comment '状态',
   primary key (id)
)
    ENGINE = InnoDB
      DEFAULT CHARSET = utf8
      COMMENT '检索方案表';

#file表新增c_is_read字段
ALTER TABLE t_file  ADD  c_is_read varchar(1);
ALTER TABLE t_file  ADD  c_brandIds varchar(1000);

#设置check表 c_readTime 字段默认值为0
update t_check set c_readTime = 0 where c_readTime is null;

#驳回原因表
create table t_reject_reason 
(
   id                  varchar(32)  comment 'id' not null,
   c_reason             varchar(500)  comment '驳回原因',
   c_userId              varchar(32)  comment '用户id',
   c_createTime        bigint default 0 comment '创建时间',
   c_state             varchar(1)   comment '状态',
   primary key (id)
)
    ENGINE = InnoDB
      DEFAULT CHARSET = utf8
      COMMENT '驳回原因表';

#20210728 编码便跟更新admin的密码
update t_user set c_password = 'a4b14442afd06f55d11e9489ffd1db5e7f0d34ba' where c_name = 'admin'

# v3.3.3合同表新增负责采购字段
ALTER TABLE t_contract ADD c_purchaserName VARCHAR(50);
ALTER TABLE t_contract ADD c_purchaserManId VARCHAR(50);
# 负责采购赋值为创建人
update t_contract a inner join t_contract b on b.id = a.id set a.c_purchaserName = b.c_createUser;
update t_contract a inner join t_contract b on b.id = a.id set a.c_purchaserManId = b.c_createManId;

ALTER TABLE t_financial MODIFY COLUMN c_bankName varchar(200)

```

