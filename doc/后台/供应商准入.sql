-- srm_prod.t_entry_registration_discount definition

CREATE TABLE `t_entry_registration_discount` (
                                               `id` varchar(32) NOT NULL COMMENT 'id',
                                               `c_type` varchar(10) DEFAULT NULL COMMENT '类型，阶梯折扣、品牌折扣',
                                               `c_performance_amount` decimal(20,10) DEFAULT NULL COMMENT '履约金额',
                                               `c_discount_ratio` decimal(4,2) DEFAULT NULL COMMENT '折扣比例',
                                               `brand_id` varchar(32) DEFAULT NULL COMMENT '品牌id',
                                               `c_state` varchar(1) DEFAULT NULL COMMENT '数据状态',
                                               `c_create_time` bigint(13) DEFAULT NULL,
                                               `c_update_time` bigint(13) DEFAULT NULL,
                                               `c_create_man` varchar(32) DEFAULT NULL,
                                               `c_update_man` varchar(32) DEFAULT NULL,
                                               `entry_registration_order_id` varchar(32) DEFAULT NULL COMMENT '入驻报备单id',
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入驻报备单阶梯折扣比例表';


-- srm_prod.t_entry_registration_landing_merchants definition

CREATE TABLE `t_entry_registration_landing_merchants` (
                                                        `id` varchar(32) NOT NULL,
                                                        `c_enterprise_name` varchar(50) DEFAULT NULL COMMENT '企业名称',
                                                        `c_uscc` varchar(18) DEFAULT NULL COMMENT '统一社会信用代码',
                                                        `c_company_attribute_type` varchar(2) DEFAULT NULL COMMENT '公司属性类型',
                                                        `c_corporate` varchar(10) DEFAULT NULL COMMENT '法定代表人',
                                                        `c_reg_address` varchar(100) DEFAULT NULL COMMENT '注册地址',
                                                        `c_name_of_account` varchar(20) DEFAULT NULL COMMENT '账户名称',
                                                        `c_phone` varchar(20) DEFAULT NULL COMMENT '电话',
                                                        `c_bank_address` varchar(100) DEFAULT NULL COMMENT '开户行地址',
                                                        `c_name_of_bank` varchar(50) DEFAULT NULL COMMENT '开户行名称',
                                                        `c_bank_account_number` varchar(20) DEFAULT NULL COMMENT '银行账户号',
                                                        `c_bank_code` varchar(12) DEFAULT NULL COMMENT '银行联行号',
                                                        `c_account_user` varchar(10) DEFAULT NULL COMMENT '账号使用人',
                                                        `c_account_user_phone` varchar(20) DEFAULT NULL COMMENT '账号使用人电话',
                                                        `c_invoice_type` varchar(50) DEFAULT NULL COMMENT '票种',
                                                        `c_tax_rate` decimal(4,2) DEFAULT NULL COMMENT '税率',
                                                        `c_state` varchar(1) DEFAULT NULL COMMENT '数据状态',
                                                        `c_email_address` varchar(50) DEFAULT NULL COMMENT '邮箱地址',
                                                        `c_create_time` bigint(13) DEFAULT NULL,
                                                        `c_update_time` bigint(13) DEFAULT NULL,
                                                        `c_create_man` varchar(32) DEFAULT NULL,
                                                        `c_update_man` varchar(32) DEFAULT NULL,
                                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='入驻报备单落地商信息';



-- srm_prod.t_entry_registration_order definition

CREATE TABLE `t_entry_registration_order` (
                                            `id` varchar(32) NOT NULL COMMENT 'id',
                                            `c_registration_number` varchar(18) DEFAULT NULL COMMENT '报备单号',
                                            `c_registration_status` varchar(2) DEFAULT NULL COMMENT '报备状态',
                                            `c_name_of_partner` varchar(50) DEFAULT NULL COMMENT '合作单位名称',
                                            `salesman_id` varchar(32) DEFAULT NULL COMMENT '业务员id',
                                            `c_registration_time` bigint(13) DEFAULT NULL COMMENT '报备时间',
                                            `platform` varchar(32) DEFAULT NULL COMMENT '项目/平台',
                                            `c_type_of_cooperation` varchar(2) DEFAULT NULL COMMENT '合作类型',
                                            `c_cooperation_start_time` bigint(13) DEFAULT NULL COMMENT '合作开始时间',
                                            `c_cooperation_end_time` bigint(13) DEFAULT NULL COMMENT '合作结束时间',
                                            `c_cooperation_contact_name` varchar(10) DEFAULT NULL COMMENT '合作联系人姓名',
                                            `c_cooperation_contact_phone` varchar(20) DEFAULT NULL COMMENT '合作联系人电话',
                                            `c_cooperation_brand` varchar(50) DEFAULT NULL COMMENT '合作品牌',
                                            `c_cooperation_region` varchar(50) DEFAULT NULL COMMENT '合作区域',
                                            `c_notes` varchar(200) DEFAULT NULL COMMENT '备注',
                                            `c_deposit` decimal(20,10) DEFAULT NULL COMMENT '保证金',
                                            `c_storage` varchar(1) DEFAULT NULL COMMENT '仓储，1标识有，0标识无',
                                            `c_payment_type` varchar(2) DEFAULT NULL COMMENT '付款方式',
                                            `c_accountPeriod` int(11) DEFAULT NULL COMMENT '账期天数',
                                            `c_payment_terms` varchar(50) DEFAULT NULL COMMENT '付款条件，逗号隔开。（前后需要加一个逗号，如：,对方开票,客户回款,）',
                                            `c_Initial_discount_ratio` decimal(4,2) DEFAULT NULL COMMENT '初始折扣比例',
                                            `c_state` varchar(1) DEFAULT NULL COMMENT '数据状态',
                                            `c_create_time` bigint(13) DEFAULT NULL,
                                            `c_update_time` bigint(13) DEFAULT NULL,
                                            `c_create_man` varchar(32) DEFAULT NULL,
                                            `c_update_man` varchar(32) DEFAULT NULL,
                                            `c_salesman_name` varchar(10) DEFAULT NULL COMMENT '业务员姓名',
                                            `c_business_company` varchar(20) DEFAULT NULL COMMENT '业务公司名称',
                                            `c_link_generation_time` bigint(13) DEFAULT NULL COMMENT '链接生成时间',
                                            `c_reason_for_rejection` varchar(100) DEFAULT NULL COMMENT '驳回理由',
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='落地商入驻报备单';