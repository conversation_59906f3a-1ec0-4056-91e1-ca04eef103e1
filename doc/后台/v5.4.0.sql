-- srm_zhens.t_supplier_performance definition
use
srm;
-- srm.t_landing_contract definition

CREATE TABLE `t_landing_contract`
(
  `id`                                   varchar(32) NOT NULL COMMENT '主键',
  `c_contract_no`                        varchar(50) NOT NULL COMMENT '合同号',
  `c_type`                               varchar(2)   DEFAULT NULL COMMENT '合同类型',
  `c_signing_type`                       varchar(2)   DEFAULT NULL COMMENT '签定方式',
  `first_signing_group_id`               varchar(32) NOT NULL COMMENT '我方签约主体',
  `second_signing_supplier_id`           varchar(32) NOT NULL COMMENT '对方签约主体',
  `c_effective_start`                    bigint(20) DEFAULT NULL COMMENT '合同生效开始时间',
  `c_effective_end`                      bigint(20) DEFAULT NULL COMMENT '合同失效时间',
  `c_accounting_period`                  int(10) DEFAULT NULL COMMENT '账期（单位：天）',
  `c_payment_ratio_type`                 varchar(2)   DEFAULT NULL COMMENT '付款比例类型',
  `c_allow_payment_confirm_voucher`      tinyint(1) DEFAULT NULL COMMENT '发起付款条件 - 货物签收',
  `c_allow_payment_account_open_invoice` tinyint(1) DEFAULT NULL COMMENT '发起付款条件 - 对方开票',
  `c_allow_payment_customer_return`      tinyint(1) DEFAULT NULL COMMENT '发起付款条件 - 客户回款',
  `c_source_type`                        varchar(2)   DEFAULT NULL COMMENT '合同来源类型',
  `c_create_time`                        bigint(20) DEFAULT NULL COMMENT '创建时间',
  `c_update_time`                        bigint(20) DEFAULT NULL COMMENT '修改时间',
  `c_create_man`                         varchar(32)  DEFAULT NULL COMMENT '创建人',
  `c_update_man`                         varchar(100) DEFAULT NULL COMMENT '修改人',
  `c_state`                              varchar(2)   DEFAULT NULL COMMENT '数据状态',
  `c_signature_status`                   varchar(2)   DEFAULT NULL COMMENT '签章状态',
  `c_association_performance_status`     varchar(2)   DEFAULT NULL COMMENT '合同对于履约信息的关联状态 1：已关联 2：未关联',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='落地商合同表';


-- srm.t_supplier_performance definition

CREATE TABLE `t_supplier_performance`
(
  `id`                          varchar(32) NOT NULL COMMENT '主键',
  `supplier_id`                 varchar(32)  DEFAULT NULL COMMENT '供应商 id',
  `c_platform_code`             varchar(50)  DEFAULT NULL COMMENT '平台类型',
  `c_platform_name`             varchar(50)  DEFAULT NULL COMMENT '平台名称',
  `c_status`                    varchar(2)   DEFAULT NULL COMMENT '状态',
  `c_contacts`                  varchar(50)  DEFAULT NULL COMMENT '联系人',
  `c_mobile`                    varchar(100) DEFAULT NULL COMMENT '电话',
  `c_area`                      varchar(100) DEFAULT NULL COMMENT '合作区域/项目',
  `c_cooperation_type`          varchar(2)   DEFAULT NULL COMMENT '合作模式',
  `c_signing_status`            varchar(100) DEFAULT NULL COMMENT '合同签署状态',
  `landing_contract_id`         varchar(32)  DEFAULT NULL COMMENT '关联合同',
  `c_business_leader`           varchar(50)  DEFAULT NULL COMMENT '业务负责人',
  `c_docking_purchase_erp_code` varchar(50)  DEFAULT NULL COMMENT '对接采购 ERP 编码',
  `c_docking_assistant`         varchar(50)  DEFAULT NULL COMMENT '对接助理',
  `c_create_time`               bigint(20) DEFAULT NULL COMMENT '创建时间',
  `c_create_man`                varchar(32)  DEFAULT NULL COMMENT '创建人',
  `c_update_time`               bigint(20) DEFAULT NULL COMMENT '修改时间',
  `c_update_man`                varchar(32)  DEFAULT NULL COMMENT '修改人',
  `c_state`                     varchar(2)   DEFAULT NULL COMMENT '数据状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='落地商履约信息';


ALTER TABLE t_supplier_order ADD c_mark varchar(200) NULL COMMENT '备注';
ALTER TABLE t_supplier_order ADD c_customer_order_code varchar(50) NULL COMMENT '客户订单号';
