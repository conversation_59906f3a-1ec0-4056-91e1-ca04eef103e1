ALTER TABLE t_order_filing ADD c_docking_sales_id varchar(32) NULL COMMENT '对接销售id';
ALTER TABLE t_order_filing ADD c_docking_sales_name varchar(32) NULL COMMENT '对接销售名字';
ALTER TABLE t_order_filing ADD create_man varchar(32) NULL COMMENT '创建人id';
ALTER TABLE t_order_filing MODIFY COLUMN c_docking_sales_id varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '对接销售钉钉id';
ALTER TABLE t_order_filing ADD c_ding_approval_id varchar(100) NULL COMMENT '钉钉审批实例id';
ALTER TABLE t_order_filing ADD c_ding_approval_opinion varchar(500) NULL COMMENT '钉钉审批意见';







