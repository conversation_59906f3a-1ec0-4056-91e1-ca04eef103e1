package com.xhgj.srm.mobile.dto.account;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

@Data
@ApiModel("对账单参数，用于对账")
@NoArgsConstructor
public class BatchAccountParamDTO {
  @ApiModelProperty(value = "对账单id，修改对账单时传入")
  private String id;

  @ApiModelProperty(value = "供应商id", required = true)
  @NotBlank(message = "供应商id不能为空！")
  private String supplierId;

  @ApiModelProperty(value = "供应商名称", required = true)
  @NotBlank(message = "供应商名称不能为空！")
  private String supplierName;

  @ApiModelProperty(value = "订单id串", required = true)
  @NotEmpty(message = "订单id串不能为空！")
  private List<String> orderIds;

  @ApiModelProperty(value = "对账金额", required = true)
  @NotNull(message = "对账金额不能为空！")
  private String price;

  @ApiModelProperty(value = "供应商备注")
  @Length(max = 140, message = "供应商备注超长")
  private String remark;

  @ApiModelProperty(value = "用户id")
  private String userId;
}
