package com.xhgj.srm.mobile.dto.filing;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.entity.OrderFiling;
import com.xhgj.srm.request.utils.OrderFilingUtil;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class FilingDetailDTO {

  @ApiModelProperty("报备单id")
  private String id;

  @ApiModelProperty("报备单号")
  private String filingNo;

  @ApiModelProperty("报备时间")
  private String filingTime;

  @ApiModelProperty("下单平台")
  private String platform;

  @ApiModelProperty("报备金额")
  private BigDecimal price;

  @ApiModelProperty("客户名称")
  private String customer;

  @ApiModelProperty("预计点单日")
  private String orderTime;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("报备单状态")
  private String state;

  @ApiModelProperty("报备单商品明细")
  private List<FilingProductDetailDTO> productList;

  @ApiModelProperty("对接销售id")
  private String dockingSalesId;

  @ApiModelProperty("对接销售名称")
  private String dockingSalesName;

  @ApiModelProperty("审批意见")
  private String approvalOpinion;

  @ApiModelProperty("客户收货地址")
  private String receiveAddress;

  @ApiModelProperty("收获地址")
  private String receiveAddressName;

  @ApiModelProperty("有效期")
  private String validity;
  /** 对接销售工号 */
  @ApiModelProperty("对接销售工号")
  private String dockingSalesJobNumber;

  public FilingDetailDTO(OrderFiling orderFiling) {
    this.id = orderFiling.getId();
    validity = OrderFilingUtil.getValidity(orderFiling);
    this.filingNo = orderFiling.getFilingNo();
    this.filingTime =
        orderFiling.getFilingTime() > 0
            ? DateUtils.formatTimeStampToNormalDateTime(orderFiling.getFilingTime())
            : "";
    this.orderTime =
        orderFiling.getOrderTime() > 0
            ? DateUtils.formatTimeStampToNormalDateTime(orderFiling.getOrderTime())
            : "";
    this.platform = !StringUtils.isNullOrEmpty(orderFiling.getType()) ? orderFiling.getType() : "";
    this.customer = StringUtils.emptyIfNull(orderFiling.getCustomer());
    this.price = orderFiling.getPrice();
    this.remark = StringUtils.emptyIfNull(orderFiling.getRemark());
    this.state =
        !StringUtils.isNullOrEmpty(orderFiling.getFilingState())
            ? Constants_order.FILING_STATE_MAP.get(orderFiling.getFilingState())
            : "待履约";
    this.dockingSalesId = StrUtil.emptyIfNull(orderFiling.getDockingSalesId());
    this.dockingSalesName = StrUtil.emptyIfNull(orderFiling.getDockingSalesName());
    this.approvalOpinion = StrUtil.emptyIfNull(orderFiling.getDingApprovalOpinion());
    this.receiveAddress = StrUtil.emptyIfNull(orderFiling.getReceiveAddress());
    this.receiveAddressName = StrUtil.emptyIfNull(orderFiling.getReceiveAddressName());
    this.dockingSalesJobNumber = StrUtil.emptyIfNull(orderFiling.getDockingSalesJobNumber());
  }
}
