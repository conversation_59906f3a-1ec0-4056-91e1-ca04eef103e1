package com.xhgj.srm.mobile.dto.supplierOrder;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.mobile.dto.FileDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> @ClassName SupplierOrderShipDTO
 */
@Data
public class SupplierOrderShipDTO {
  @ApiModelProperty("物流id")
  private String id;

  @ApiModelProperty("发货时间")
  private String deliveryTime;

  @ApiModelProperty("快递公司")
  private String logisticsCompany;

  @ApiModelProperty("快递公司编码")
  private String logisticsCode;

  @ApiModelProperty("物流单号")
  private String trackNum;

  @ApiModelProperty("状态 （1--待收货  2--已签收）")
  private String state;

  @ApiModelProperty("状态对应名称")
  private String stateToName;

  @ApiModelProperty("是否入库")
  private Boolean warehousing;

  @ApiModelProperty("发货明细")
  private List<ShipProductDTO> shipProductDTOList;

  @ApiModelProperty("附件")
  private List<FileDTO> fileList;

  @ApiModelProperty("件数")
  private String number;

  public SupplierOrderShipDTO(
      SupplierOrderToForm supplierOrderToForm, List<ShipProductDTO> shipProductDTOList) {
    this.id = supplierOrderToForm.getId();
    this.deliveryTime =
        supplierOrderToForm.getTime() > 0
            ? DateUtil.format(
                new Date(supplierOrderToForm.getTime()), DatePattern.NORM_DATETIME_PATTERN)
            : "";
    this.logisticsCompany = StrUtil.emptyIfNull(supplierOrderToForm.getLogisticsCompany());
    this.logisticsCode = StrUtil.emptyIfNull(supplierOrderToForm.getLogisticsCode());
    this.state = StrUtil.emptyIfNull(supplierOrderToForm.getStatus());
    this.stateToName =
        SupplierOrderFormStatus.findValueByStatus(supplierOrderToForm.getStatus()).getDesc();
    this.trackNum = StrUtil.emptyIfNull(supplierOrderToForm.getTrackNum());
    this.shipProductDTOList = shipProductDTOList;
    this.warehousing = ObjectUtil.equal(Boolean.TRUE, supplierOrderToForm.getWarehousing());
    this.number = StrUtil.emptyIfNull(supplierOrderToForm.getNumber());
  }
}
