package com.xhgj.srm.mobile.dto;

import com.xhgj.srm.jpa.entity.Supplier;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName EnterpriseInfoDTo Create by Liuyq on 2021/6/1 9:55
 */
@Data
public class EnterpriseDetailDTo {
  @ApiModelProperty("供应商id")
  private String id;

  @ApiModelProperty("企业名称")
  private String enterpriseName;

  @ApiModelProperty("统一社会信用代码")
  private String uscc;

  @ApiModelProperty("法定代表人")
  private String corporate;

  @ApiModelProperty("成立日期")
  private String date;

  @ApiModelProperty("注册资本")
  private String regCapital;

  @ApiModelProperty("经营状态")
  private String regStatus;

  @ApiModelProperty("注册地址")
  private String regAddress;

  @ApiModelProperty("营业执照")
  private String licenseUrl;

  @ApiModelProperty("合作类型")
  private String cooperateType;

  public EnterpriseDetailDTo(Supplier supplier, String uploadUrl) {
    this.id = supplier.getId();
    this.enterpriseName = supplier.getEnterpriseName();
    this.uscc = supplier.getUscc();
    this.corporate = supplier.getCorporate();
    this.date =
        supplier.getDate() != null && supplier.getDate() > 0
            ? DateUtils.formatTimeStampToNormalDate(supplier.getDate())
            : "";
    this.regCapital = supplier.getRegCapital();
    this.regStatus =
        !StringUtils.isNullOrEmpty(supplier.getManageType()) ? supplier.getManageType() : "";
    this.regAddress = supplier.getRegAddress();
    this.licenseUrl =
        !StringUtils.isNullOrEmpty(supplier.getLicenseUrl())
            ? uploadUrl + supplier.getLicenseUrl()
            : "";
    this.cooperateType = supplier.getCooperateType();
  }
}
