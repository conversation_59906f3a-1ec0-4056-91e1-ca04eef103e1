package com.xhgj.srm.mobile.dto.account;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.jpa.entity.OrderAccountReturnDetail;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class AccountReturnDetailDTO {

  @ApiModelProperty("回款单id")
  private String id;

  @ApiModelProperty("回款金额")
  private BigDecimal refundPrice;

  @ApiModelProperty("回款时间")
  private String refundTime;

  @ApiModelProperty("是否上传凭证")
  private String isUpload;

  @ApiModelProperty("是否回款")
  private String isRefund;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("回款凭证")
  private List<String> fileList;

  public AccountReturnDetailDTO(OrderAccountReturnDetail orderAccountReturnDetail) {
    this.id = orderAccountReturnDetail.getId();
    this.refundPrice = orderAccountReturnDetail.getPrice();
    this.refundTime =
        orderAccountReturnDetail.getReturnTime() > 0
            ? DateUtils.formatTimeStampToNormalDateTime(orderAccountReturnDetail.getReturnTime())
            : "";
    this.isUpload =
        !StringUtils.isNullOrEmpty(orderAccountReturnDetail.getIsUpload())
            ? Constants.YESORNO.get(orderAccountReturnDetail.getIsUpload())
            : Constants.NO;
    this.isRefund =
        !StringUtils.isNullOrEmpty(orderAccountReturnDetail.getReturnState())
            ? Constants_order.ORDER_REFUND_STATE_MAP.get(orderAccountReturnDetail.getReturnState())
            : "";
    this.remark = StringUtils.emptyIfNull(orderAccountReturnDetail.getRemark());
  }
}
