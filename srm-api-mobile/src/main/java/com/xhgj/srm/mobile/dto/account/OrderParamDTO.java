package com.xhgj.srm.mobile.dto.account;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class OrderParamDTO {

  @ApiModelProperty("订单id")
  private String id;

  @ApiModelProperty("客户订单号")
  private String orderNo;

  @ApiModelProperty("下单时间")
  private String createTime;

  @ApiModelProperty("下单平台")
  private String platform;

  @ApiModelProperty("客户名称")
  private String customer;

  @ApiModelProperty("收件人")
  private String consignee;

  @ApiModelProperty("联系方式")
  private String mobile;

  @ApiModelProperty("最终结算金额")
  private BigDecimal price;
}
