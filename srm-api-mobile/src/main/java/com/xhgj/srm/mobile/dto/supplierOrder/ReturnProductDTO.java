package com.xhgj.srm.mobile.dto.supplierOrder;

import cn.hutool.core.util.ObjectUtil;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR> @ClassName ShipProductDTO
 */
@Data
public class ReturnProductDTO extends BaseOrderDetailProductDTO {

  @ApiModelProperty("退货数量")
  private BigDecimal returnQty;

  @ApiModelProperty("是否开红票")
  private Boolean openRedInvoice;

  public ReturnProductDTO(SupplierOrderDetail supplierOrderDetail) {
    super(supplierOrderDetail.getSupplierOrderProduct(), supplierOrderDetail);
    this.returnQty =
        ObjectUtil.defaultIfNull(supplierOrderDetail.getReturnQty(), new BigDecimal(0));
    this.openRedInvoice = Boolean.TRUE.equals(supplierOrderDetail.getOpenRedInvoice());
  }
}
