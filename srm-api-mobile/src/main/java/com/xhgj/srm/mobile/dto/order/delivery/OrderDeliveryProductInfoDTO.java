package com.xhgj.srm.mobile.dto.order.delivery;

import com.xhgj.srm.mobile.dto.order.OrderProductDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** Created by Geng Shy on 2023/11/24 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDeliveryProductInfoDTO {

  @ApiModelProperty("商品明细")
  private List<OrderDeliveryProductDetailDTO> productDetailVO;
}
