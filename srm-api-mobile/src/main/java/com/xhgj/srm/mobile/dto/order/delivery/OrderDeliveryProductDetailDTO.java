package com.xhgj.srm.mobile.dto.order.delivery;

import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class OrderDeliveryProductDetailDTO {

  @ApiModelProperty("明细id")
  private String id;

  @ApiModelProperty("物料编码")
  private String code;

  @ApiModelProperty("品牌")
  private String brand;

  @ApiModelProperty("商品名称")
  private String name;

  @ApiModelProperty("型号")
  private String model;

  @ApiModelProperty("单位")
  private String unit;

  @ApiModelProperty("未发数量")
  private BigDecimal unCount;

  @ApiModelProperty("数量")
  private BigDecimal num;

  @ApiModelProperty("订单id")
  private String orderId;

  @ApiModelProperty("成本价税率")
  private String costPriceTaxRate;

  @ApiModelProperty("行id")
  private String rowId;

  @ApiModelProperty("单价")
  private BigDecimal price;

  public OrderDeliveryProductDetailDTO(OrderDetail orderDetail) {
    this.id = orderDetail.getId();
    this.code = StringUtils.emptyIfNull(orderDetail.getCode());
    this.brand = StringUtils.emptyIfNull(orderDetail.getBrand());
    this.name = StringUtils.emptyIfNull(orderDetail.getName());
    this.model = StringUtils.emptyIfNull(orderDetail.getModel());
    this.unit = StringUtils.emptyIfNull(orderDetail.getUnit());
    this.unCount = orderDetail.getUnshipNum();
    this.num = orderDetail.getNum();
    this.costPriceTaxRate = orderDetail.getCostPriceTaxRate();
    this.rowId = orderDetail.getErpRowId();
    this.price = orderDetail.getPrice();
  }
}
