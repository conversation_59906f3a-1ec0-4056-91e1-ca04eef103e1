package com.xhgj.srm.mobile.service.impl;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.mobile.utils.CookieUtil;
import com.xhgj.srm.mobile.utils.ImageValidateCode;
import com.xhgj.srm.common.utils.PasswordUtil;
import com.xhgj.srm.jpa.dao.SupplierUserDao;
import com.xhgj.srm.jpa.entity.LoginInfo;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.repository.LoginInfoRepository;
import com.xhgj.srm.jpa.repository.SearchSchemeRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.SupplierUserRepository;
import com.xhgj.srm.mobile.domain.SrmSupplierUserDetails;
import com.xhgj.srm.mobile.dto.SupplierUserLoginData;
import com.xhgj.srm.mobile.service.SupplierUserService;
import com.xhgj.srm.mobile.service.SupplierToMenuService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.redis.util.RedisUtil;
import com.xhiot.boot.security.config.JwtConfig;
import com.xhiot.boot.security.util.JwtTokenUtil;
import java.io.InputStream;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

/**
 * @ClassName SupplierUserServiceImpl Create by Liuyq on 2021/6/8 19:03
 */
@Service
@Slf4j
public class SupplierUserServiceImpl implements SupplierUserService {

  @Autowired SupplierUserRepository repository;
  @Autowired SupplierRepository supplierRepository;
  @Autowired SupplierUserDao supplierUserDao;
  @Autowired JwtConfig jwtConfig;
  @Autowired JwtTokenUtil jwtTokenUtil;
  @Autowired LoginInfoRepository loginInfoRepository;
  @Autowired RedisUtil redisUtil;
  @Autowired SearchSchemeRepository searchSchemeRepository;
  @Resource private SupplierToMenuService supplierToMenuService;
  /** 生成验证码 */
  public static final String VERIFY_ID = "verid";

  private final String baseUrl;

  private final String universalCode;
  /** 上线提醒时间 2023-09-21 18:40:00 精确到毫秒 */
  private static final Long versionTime = 1695290400000L;

  public SupplierUserServiceImpl(SrmConfig config) {
    this.baseUrl = config.getUploadUrl();
    this.universalCode = config.getUniversalCode();
  }

  @Override
  public BootBaseRepository<SupplierUser, String> getRepository() {
    return repository;
  }

  @Override
  public SupplierUserLoginData supplierUserLogin(
      HttpServletRequest request,
      HttpServletResponse response,
      String name,
      String pwd,
      String code) {
    Boolean isFirst = true;
    if (StringUtils.isNullOrEmpty(name)) {
      throw new CheckException("请输入用户名");
    }
    if (StringUtils.isNullOrEmpty(pwd)) {
      throw new CheckException("请输入密码");
    }
    SupplierUser supplierUser = supplierUserDao.getSupplierUserByName(name);
    if (supplierUser == null) {
      throw new CheckException("未找到该账号");
    }
    if (supplierUser.getSupplier() == null) {
      throw new CheckException("该账号供应商为空");
    }

    String supplierId = supplierUser.getSupplier().getId();
    Supplier supplier =
        supplierRepository
            .findById(supplierId)
            .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));

    // 黑名单，登录按钮下方红字提示,用户名或密码错误：t3
    if (Constants.COMMONSTATE_BLACKLIST.equals(supplier.getState())) {
      throw new CheckException("用户名或密码错误");
    }

    String token = "";
    String encryptPwd = PasswordUtil.sha1(Constants.PASSWORDSHAPRE, supplierUser.getId(), pwd);

    Integer i = (Integer) redisUtil.get(supplierUser.getId());
    if (i != null && i >= Constants.LOGIN_NUMBER) {
      if (StringUtils.isNullOrEmpty(code)) {
        throw new CheckException("请输入验证码");
      }
      // 从cookie获取uuid串
      String verid = CookieUtil.getValue(request, "verid");
      String redisVerCode = null;
      if (!StringUtils.isNullOrEmpty(verid)) {
        // 从redis获取验证码
        redisVerCode = (String) redisUtil.get(verid);
        redisUtil.del("verid" + verid);
      }
      if (verid == null || redisVerCode == null) {
        throw new CheckException("验证码已过期，点击刷新验证码");
      }
      CookieUtil.deleteCookie(request, response, "verid");
      // 比对验证码
      if (!redisVerCode.equalsIgnoreCase(code)) {
        throw new CheckException("验证码错误");
      }
    }
    if (encryptPwd.equals(supplierUser.getPassword()) || universalCode.equals(pwd)) {
      token =
          jwtConfig.getTokenHead()
              + jwtTokenUtil.generateToken(
                  loadSupplierUserInfo(supplierUser), supplierUser.getId());
      Integer integer =
          loginInfoRepository.countByCreateTimeAfterAndLoginUserId(
              versionTime, supplierUser.getId());
      // 记录用户登录日志
      LoginInfo loginInfo = new LoginInfo();
      loginInfo.setLoginUserId(supplierUser.getId());
      loginInfo.setLoginPlatform(Constants.PLATFORM_TYPE_BEFORE);
      loginInfo.setLogintype(Constants.LOGINCODE_LOGIN);
      loginInfo.setCreateTime(System.currentTimeMillis());
      loginInfo.setState(Constants.STATE_OK);
      loginInfo.setDescription(Constants.LOG_DESCRIPTION_WEB_LOGIN);
      loginInfoRepository.saveAndFlush(loginInfo);
      redisUtil.del(supplierUser.getId());
      if (integer > 0) {
        isFirst = false;
      }
    } else {
      if (i != null) {
        redisUtil.set(supplierUser.getId(), i + 1, 8 * 60 * 60 * 1000);
      } else {
        redisUtil.set(supplierUser.getId(), 1, 8 * 60 * 60 * 1000);
      }
      throw new CheckException("用户名或密码错误");
    }
    List<String> menuKeys = supplierToMenuService.findAllMenuKeyBySupplierId(supplierId);
    return new SupplierUserLoginData(supplierUser, supplier, token, baseUrl, isFirst, menuKeys);
  }

  @Override
  public UserDetails loadSupplierUserInfo(SupplierUser supplierUser) {
    return new SrmSupplierUserDetails(supplierUser);
  }

  @SneakyThrows
  @Override
  public String getCodeImg(HttpServletResponse response) {
    ImageValidateCode vCode = new ImageValidateCode(response, redisUtil);
    InputStream io = vCode.getImageStream(vCode.getBuffImg());
    byte[] data = new byte[io.available()];
    io.read(data);
    io.close();
    return new String(Base64.getEncoder().encode(data));
  }

  @Override
  public Optional<String> getSupplierUserRealName(String userId) {
    if (StrUtil.isBlank(userId)) {
      return Optional.empty();
    }
    SupplierUser supplierUser = get(userId);
    if (supplierUser == null) {
      return Optional.empty();
    }
    return Optional.of(supplierUser.getRealName());
  }
}
