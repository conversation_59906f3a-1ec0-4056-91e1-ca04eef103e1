package com.xhgj.srm.mobile.service.impl;

import cn.hutool.core.lang.Assert;
import com.xhgj.srm.jpa.dao.SupplierTemplateDao;
import com.xhgj.srm.jpa.entity.SupplierTemplate;
import com.xhgj.srm.jpa.repository.SupplierTemplateRepository;
import com.xhgj.srm.mobile.service.SupplierTemplateService;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2022/10/12 11:21
 */
@Service
@Slf4j
public class SupplierTemplateServiceImpl implements SupplierTemplateService {

  @Autowired private SupplierTemplateRepository repository;
  @Autowired private SupplierTemplateDao dao;

  @Override
  public BootBaseRepository<SupplierTemplate, String> getRepository() {
    return repository;
  }

  @Override
  public SupplierTemplate getByGroupId(String groupId) {
    Assert.notEmpty(groupId);
    return dao.getSupplierTemplateByGroupId(groupId);
  }
}
