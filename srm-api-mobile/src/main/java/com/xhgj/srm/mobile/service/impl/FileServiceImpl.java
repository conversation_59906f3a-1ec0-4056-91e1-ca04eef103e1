package com.xhgj.srm.mobile.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.utils.ZIPUtils;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.mobile.dto.FileDTO;
import com.xhgj.srm.mobile.service.FileService;
import com.xhgj.srm.mobile.service.SupplierFbService;
import com.xhgj.srm.request.utils.DownloadThenUpUtil;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2021/3/1 18:46
 */
@Service
@Slf4j
public class FileServiceImpl implements FileService {

  @Autowired private FileRepository repository;
  @Autowired private FileDao dao;
  @Autowired private SupplierFbService supplierFbService;
  @Resource
  private DownloadThenUpUtil downloadThenUpUtil;

  @Override
  public BootBaseRepository<File, String> getRepository() {
    return repository;
  }

  @Override
  public void copySupplierAgreeFileToSupplierFb(String supplierId, String supplierFbId) {
    addFileToSupplierFb(dao.getFileListByRIdAndAgree(supplierId), supplierFbId);
  }

  @Override
  public void copySupplierFileToSupplierFb(String supplierId, String supplierFbId) {
    addFileToSupplierFb(dao.getFileListByRIdAndRtype(supplierId), supplierFbId);
  }

  @Override
  public void deleteAllFileExxy(String supplierId) {
    String hql =
        "delete from File f where f.state = ? and f.relationId = ? and "
            + " ( f.relationType = ? or f.relationType = ? or f.relationType = ? "
            + "or f.relationType = ? or f.relationType = ? or f.relationType = ? "
            + "or f.relationType = ? or f.relationType = ? or f.relationType = ? or f.relationType = ? ) ";
    Object[] params =
        new Object[] {
          Constants.STATE_OK,
          supplierId,
          Constants.FILE_TYPE_SCXK,
          Constants.FILE_TYPE_ZLZS,
          Constants.FILE_TYPE_SPZCZS,
          Constants.FILE_TYPE_CPSYBG,
          Constants.FILE_TYPE_DSFJCBG,
          Constants.FILE_TYPE_ISOZLRZTX,
          Constants.FILE_TYPE_ISO14001HJGLTX,
          Constants.FILE_TYPE_OHSAS18001ZYJKAQTX,
          Constants.FILE_TYPE_CMSZLGLTXRZZS,
          Constants.FILE_TYPE_DLZS
        };
    dao.executeUpdate(hql, params);
  }

  @Transactional
  @Override
  public void updateSupplierFilesByFb(String supplierId, String fbid) {
    List<File> flist = dao.getFileListByRIdAndRtype(fbid);
    if (flist != null && flist.size() > 0) {
      for (File f : flist) {
        File fi = new File();
        fi.setName(f.getName());
        fi.setRelationId(supplierId);
        fi.setRelationType(f.getRelationType());
        fi.setState(Constants.STATE_OK);
        fi.setType(f.getType());
        fi.setUrl(f.getUrl());
        fi.setCreateTime(System.currentTimeMillis());
        repository.save(fi);
      }
      repository.flush();
    }
  }

  @Transactional
  @Override
  public void updateSupplierFbFilesByFb(String oldFbId, String fbid) {
    List<File> flist = dao.getFileListByRIdAndRtype(oldFbId);
    SupplierFb supplierFb = supplierFbService.get(fbid);
    if (flist != null && flist.size() > 0) {
      for (File f : flist) {
        File fi = new File();
        fi.setName(f.getName());
        fi.setRelationId(supplierFb.getId());
        fi.setRelationType(f.getRelationType());
        fi.setState(Constants.STATE_OK);
        fi.setType(f.getType());
        fi.setUrl(f.getUrl());
        fi.setCreateTime(System.currentTimeMillis());
        repository.save(fi);
      }
    }
    repository.flush();
  }

  @Transactional
  @Override
  public void updateSupplierFbXyByFb(String oldFbId, String fbid) {
    List<File> flist1 = dao.getFileListByRIdAndAgree(oldFbId);
    SupplierFb supplierFb = supplierFbService.get(fbid);
    if (flist1 != null && flist1.size() > 0) {
      for (File f : flist1) {
        File fi = new File();
        fi.setName(f.getName());
        fi.setRelationId(supplierFb.getId());
        fi.setRelationType(f.getRelationType());
        fi.setState(Constants.STATE_OK);
        fi.setType(f.getType());
        fi.setUrl(f.getUrl());
        fi.setCreateTime(System.currentTimeMillis());
        repository.save(fi);
      }
      repository.flush();
    }
  }

  @Override
  @SneakyThrows
  public byte[] downloadZipMoreFile(List<String> fileIdList) {
    if (CollUtil.isEmpty(fileIdList)) {
      return new byte[0];
    }
    Map<String, byte[]> fileNameMapByte = new HashMap<>(16);
    for (String fileId : fileIdList) {
      File file = get(fileId);
      if (file == null) {
        log.error("【" + fileId + "】文件不存在");
        continue;
      }
      String url = file.getUrl();
      if (StrUtil.isNotBlank(url)) {
        try (InputStream inputStream = downloadThenUpUtil.getInputStreamFromOSS(url)) {
          if (inputStream != null) {
            String fileName =
                StrUtil.blankToDefault(file.getName(), String.valueOf(System.currentTimeMillis()));
            byte[] bytes = IOUtils.toByteArray(inputStream);
            if (fileNameMapByte.containsKey(fileName)) {
              String subBefore =
                  StrUtil.subBefore(fileName, CharUtil.DOT, true) + System.currentTimeMillis();
              String suffix = StrUtil.subAfter(fileName, CharUtil.DOT, true);
              fileName = subBefore + CharUtil.DOT + suffix;
            }
            fileNameMapByte.put(fileName, bytes);
          }
        }
      }
    }
    return ZIPUtils.batchFileToZIP(fileNameMapByte);
  }

  private void addFileToSupplierFb(List<File> fileList, String supplierFbId) {
    if (CollUtil.isNotEmpty(fileList)) {
      for (File file : fileList) {
        File newFile = new File();
        newFile.setName(file.getName());
        newFile.setRelationId(supplierFbId);
        newFile.setRelationType(file.getRelationType());
        newFile.setState(Constants.STATE_OK);
        newFile.setType(file.getType());
        newFile.setUrl(file.getUrl());
        newFile.setCreateTime(System.currentTimeMillis());
        repository.save(newFile);
      }
    }
  }

  @Override
  public void saveFile(
      FileDTO contractFile, String userId, String relationId, String relationType) {
    boolean answer = checkFileDTO(contractFile);
    if (!answer) {
      return;
    }
    File file = new File();
    file.setUrl(contractFile.getUrl());
    file.setName(contractFile.getName());
    file.setDescription(contractFile.getName());
    file.setState(Constants.STATE_OK);
    file.setRelationType(relationType);
    file.setRelationId(relationId);
    file.setCreateTime(System.currentTimeMillis());
    file.setUploadMan(userId);
    dao.save(file);
  }

  private boolean checkFileDTO(FileDTO fileDTO) {
    if (fileDTO == null) {
      return false;
    }
    return StrUtil.isNotBlank(fileDTO.getName()) && StrUtil.isNotBlank(fileDTO.getUrl());
  }

  @Override
  public boolean updateRelevanceId(String fileId, String relationId) {
    if (StrUtil.isBlank(fileId) || StrUtil.isBlank(relationId)) {
      return false;
    }
    File file = dao.get(fileId);
    if (file == null) {
      return false;
    }
    file.setRelationId(relationId);
    dao.save(file);
    return true;
  }

  @Override
  public List<File> getFileListByIdAndType(String relationId, String relationType) {
    List<File> list = dao.getFileListBySId(relationId, relationType);
    return CollUtil.emptyIfNull(list);
  }

  @Override
  public void deleteFileByRelationIdAndType(final String relationId, final String relationType) {
    Assert.notBlank(relationId);
    Assert.notBlank(relationType);
    Optional<List<File>> filesOptional =
        repository.findAllByRelationIdAndRelationTypeAndState(
            relationId, relationType, Constants.STATE_OK);
    if (!filesOptional.isPresent() || CollUtil.isNotEmpty(filesOptional.get())) {
      return;
    }
    filesOptional
        .get()
        .forEach(
            file -> {
              file.setState(Constants.STATE_DELETE);
              save(file);
            });
  }

  @Override
  public Optional<File> findFirstByRelationIdAndRelationType(
      String relationId, String relationType) {
    Assert.notBlank(relationId);
    Assert.notBlank(relationType);
    return repository.findFirstByRelationIdAndRelationTypeAndState(
        relationId, relationType, Constants.STATE_OK);
  }
}
