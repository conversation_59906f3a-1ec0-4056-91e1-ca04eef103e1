package com.xhgj.srm.api.portal.controller;

import com.xhgj.srm.dto.account.AccountDetailDTO;
import com.xhgj.srm.dto.account.AccountPageDTO;
import com.xhgj.srm.dto.account.AccountPageQuery;
import com.xhgj.srm.api.portal.service.OrderAccountService;
import com.xhgj.srm.service.ShareOrderAccountService;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("account")
@Api(tags = {"对账单接口"})
@Slf4j
public class AccountController {

  @Resource
  OrderAccountService orderAccountService;
  @Resource
  ShareOrderAccountService shareOrderAccountService;

  @ApiOperation(value = "分页获取对账单信息", notes = "分页获取对账单信息")
  @RequestMapping(value = "/getAccountPage", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<PageResult<AccountPageDTO>> getAccountPage(
      @Validated AccountPageQuery accountPageQuery
  ) {
    return new ResultBean<>(orderAccountService.getAccountPage(accountPageQuery));
  }

  @ApiOperation(value = "获取对账单详情", notes = "获取对账单详情")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "accountId", value = "对账单id", dataType = "String", required = true),
  })
  @RequestMapping(value = "/getAccountDetail", method = RequestMethod.GET)
  @ResponseBody
  public ResultBean<AccountDetailDTO> getAccountDetail(@NotBlank(message = "对账单 id 必传 ") String accountId) {
    return new ResultBean<>(shareOrderAccountService.getAccountDetail(accountId));
  }
}
