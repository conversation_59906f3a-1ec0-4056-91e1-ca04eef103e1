package com.xhgj.srm.api.portal.service.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.portal.service.AssessService;
import com.xhgj.srm.common.enums.AssessStateEnum;
import com.xhgj.srm.common.enums.AssessTypeEnum;
import com.xhgj.srm.jpa.entity.Assess;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.repository.AssessRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class AssessServiceImpl implements AssessService {

  @Resource
  private AssessRepository repository;

  @Override
  public BootBaseRepository<Assess, String> getRepository() {
    return repository;
  }

  public Assess createSupplierMainAddAssess(String supplierId, String createMan, Group group) {
    return createAssess(supplierId, AssessTypeEnum.SUPPLIER_MAIN_ADD, createMan, createMan,
        group);
  }

  /**
   * 创建审核
   *
   * @param targetId 审核目标数据 id，必传
   * @param assessType 审核类型，必传
   * @param assessMan 审核人（明确审核人时传入）
   * @param createMan 创建人
   */
  private Assess createAssess(String targetId,
      AssessTypeEnum assessType, String assessMan, String createMan, Group group) {
    Assert.notEmpty(targetId);
    Assert.notNull(assessType);
    Assert.notEmpty(createMan);
    Assess assess = new Assess();
    assess.setTargetId(StrUtil.emptyIfNull(targetId));
    assess.setAssessType(assessType.getKey());
    assess.setAssessState(AssessStateEnum.UN_ASSESS.getKey());
    assess.setAssessMan(StrUtil.emptyIfNull(assessMan));
    assess.setCreateMan(createMan);
    assess.setCreateTime(System.currentTimeMillis());
    if (group != null) {
      assess.setGroupId(group.getId());
    }
    return repository.save(assess);
  }
}
