package com.xhgj.srm.api.portal.controller;

import com.xhgj.srm.dto.entryregistration.ApprovalResultCallbackParam;
import com.xhgj.srm.dto.entryregistration.EntryRegistrationDetail;
import com.xhgj.srm.service.EntryRegistrationOAService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/entry-registration")
@Api(tags = {"准入报备单相关api"})
@Slf4j
public class EntryRegistrationController {

  @Resource private EntryRegistrationOAService entryRegistrationOAService;

  @ApiOperation(value = "准入报备单oa审核回调", notes = "准入报备单oa审核回调")
  @PostMapping(value = "/approval-result/callback")
  public ResultBean<Boolean> approvalResultCallback(
      @RequestBody @Validated ApprovalResultCallbackParam param) {
    return new ResultBean<>(entryRegistrationOAService.approvalResultCallback(param));
  }


  @ApiOperation(value = "入驻报备单供 H5 详情", notes = "入驻报备单供 H5 详情")
  @GetMapping(value = "/landing-merchant/detail")
  public ResultBean<EntryRegistrationDetail> settlementRegistrationFormForH5Details(
      @RequestParam @Validated String id) {
    return new ResultBean<>(entryRegistrationOAService.settlementRegistrationFormForH5Details(id));
  }

}
