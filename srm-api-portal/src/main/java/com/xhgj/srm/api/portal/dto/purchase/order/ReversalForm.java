package com.xhgj.srm.api.portal.dto.purchase.order;/**
 * @since 2025/5/19 10:02
 */

import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 *<AUTHOR>
 *@date 2025/5/19 10:02:08
 *@description
 */
@Data
public class ReversalForm {
  /**
   * 被冲销的SAP财务凭证号
   */
  @NotBlank(message = "被冲销的SAP财务凭证号不能为空")
  private String sapVoucherNo;

  /**
   * sap冲销凭证号
   */
  @NotBlank(message = "sap冲销凭证号不能为空")
  private String sapReversalVoucherNo;

  /**
   * 冲销类型 1-入库单 2-退库单
   */
  @NotBlank(message = "冲销类型不能为空")
  private String type;

  /**
   * 操作人工号
   */
  @NotBlank(message = "操作人名称不能为空")
  private String createUser;
}
