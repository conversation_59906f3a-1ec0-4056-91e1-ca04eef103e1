spring:
  application:
    name: srm-api-portal
  profiles:
    active: env-test
    include: datasource
  http:
    encoding:
      charset: utf-8
      enabled: true
      force: true
  servlet:
    multipart:
      max-file-size: 20MB
  cloud:
    nacos:
      config:
        file-extension: "yaml"
        shared-configs:
          - data-id: "srm-shared-configs.yaml"
            refresh: true
server:
  port: 8090
  servlet:
    context-path: /${spring.application.name}
  tomcat:
    uri-encoding: utf-8
logging:
  level:
    root: info
xhiot:
  boot:
    swagger:
      enable: true
      title: ${spring.application.name}
      description: "srm-api-portal"
      version: "1.0.0"
    app-name: ${spring.application.name}
    sys:
      error:
        ding:
          config:
            access-token: "3c0c25fd94f37f3c7b19ab884d1a39148b4d5c5ed6dfe2a54e57b27baf2e651a"
            at-mobiles-or-user-ids: ["13197800218"]