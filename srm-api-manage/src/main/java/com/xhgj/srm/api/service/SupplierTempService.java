package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.supplier.BaseSupplierMainDataDTO;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierTemp;
import com.xhiot.boot.framework.jpa.service.BootBaseService;

/**
 * <AUTHOR>
 * @since 2022/7/31 17:26
 */
public interface SupplierTempService extends BootBaseService<SupplierTemp, String> {

  /**
   * 根据供应商创建副本实体
   *
   * @param supplier 供应商实体
   */
  SupplierTemp createBySupplier(Supplier supplier);

  /**
   * 根据 DTO 参数创建副本实体
   *
   * @param dto 参数对象
   */
  SupplierTemp createByDTO(BaseSupplierMainDataDTO dto, Group group);
}
