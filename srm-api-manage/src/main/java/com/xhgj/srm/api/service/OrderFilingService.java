package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.filing.FilingDetailDTO;
import com.xhgj.srm.api.dto.filing.FilingPageDTO;
import com.xhgj.srm.api.dto.filing.FilingPageQuery;
import com.xhgj.srm.api.dto.filing.OrderNoFilingDetailDTO;
import com.xhgj.srm.jpa.dto.filing.FilingStatistics;
import com.xhgj.srm.jpa.entity.OrderFiling;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;

public interface OrderFilingService extends BootBaseService<OrderFiling, String> {

    /**
     * 分页获取报备单列表
     * @return
     */
    PageResult<FilingPageDTO> getFilingPage(FilingPageQuery filingPageQuery);

    /**
     * 获取报备单统计信息
     * @param filingPageQuery
     * @return
     */
    FilingStatistics getFilingStatistics(FilingPageQuery filingPageQuery);

  /**
   * 获取商品报备单详情
   *
   * @param filingId 报备单id
   * @return
   */
  FilingDetailDTO getFilingDetail(String filingId);

  /**
   * 获取订单报备单详情
   *
   * @param filingId
   * @return
   */
  OrderNoFilingDetailDTO getOrderNoFilingDetail(String filingId);

  /** 根据当前日期更新报备单状态 */
  void updateFilingState();
}
