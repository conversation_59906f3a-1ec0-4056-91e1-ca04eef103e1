package com.xhgj.srm.api.dto;

import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderProductSearchForm;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 *采购订单列表物料表头筛选
 */
@Data
public class PurchaseOrderProductTableHeaderQuery extends PurchaseOrderProductSearchForm {

  @ApiModelProperty("筛选类型: 1.供应商名称，2.是否预付款，3.是否上传合同，4.业务员，5.跟单员，6.采购员，7.物料编码，8.品牌，"
      + "9.规格型号，10.仓库,11.是否走scp，12.采购部门,13.制单员，14.是否亏本，15.是否急单, 16.订单状态，17.订单类型，18.供应商开票，19.采购申请类型")
  @NotBlank(message = "筛选类型 必传")
  private String filterType;

  public Map<String, Object> toQueryMap(MergeUserPermission mergeUserPermission) {
    Map<String, Object> queryMap = super.toQueryMap(mergeUserPermission);
    queryMap.put("filterType", this.filterType);
    return queryMap;
  }

}
