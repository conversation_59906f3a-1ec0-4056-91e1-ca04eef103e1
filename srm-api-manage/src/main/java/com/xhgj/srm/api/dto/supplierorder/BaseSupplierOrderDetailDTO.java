package com.xhgj.srm.api.dto.supplierorder;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.dict.BootDictEnumUtil;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/12/15 17:00
 */
@Data
@NoArgsConstructor
public class BaseSupplierOrderDetailDTO {

  @ApiModelProperty("采购订单 id")
  private String id;

  @ApiModelProperty("采购订单号")
  private String code;

  @ApiModelProperty("订单状态")
  private SupplierOrderState orderState;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("供应商Id")
  private String supplierId;

  @ApiModelProperty("是否厂家直发")
  private Boolean directShipment;

  @ApiModelProperty("采购组织")
  private String purchaseGroupName;

  @ApiModelProperty("收件人")
  private String receiveMan;

  @ApiModelProperty("联系方式")
  private String receiveMobile;

  @ApiModelProperty("退货取消金额")
  private BigDecimal cancelReturnPrice;

  @ApiModelProperty("最终结算金额")
  private BigDecimal finalPrice;

  @ApiModelProperty("采购员")
  private String purchaseMan;

  @ApiModelProperty("收件地址")
  private String receiveAddress;

  @ApiModelProperty("创建时间")
  private Long createTime;

  @ApiModelProperty("采购件数")
  private BigDecimal num;

  @ApiModelProperty("订单金额")
  private BigDecimal price;

  @ApiModelProperty("入库进度")
  private String stockProgress;

  @ApiModelProperty("备注")
  private String remark;

  @ApiModelProperty("客户订单号")
  private String customerOrderCode;

  @ApiModelProperty("订单类型")
  private String orderType;

  @ApiModelProperty("采购部门")
  private String purchaseDept;

  @ApiModelProperty("开票方")
  private String invoicingParty;

  @ApiModelProperty("付款条件")
  private String payCondition;

  @ApiModelProperty("账期")
  private String accountPeriod;

  @ApiModelProperty("货币码")
  private String moneyCode;

  @ApiModelProperty("大票项目号")
  private String largeTicketProjectNumber;
  @ApiModelProperty("销售订单号")
  private String salesOrderNo;
  /**
   * 售达方--关联单据
   */
  @ApiModelProperty("售达方--关联单据")
  private String soldToParty;

  /**
   * 业务员--关联单据
   */
  @ApiModelProperty("业务员--关联单据")
  private String salesman;

  @ApiModelProperty("付款条件信息")
  private String paymentTermsStr;
  @ApiModelProperty("是否亏本订单")
  private Boolean loss;
  @ApiModelProperty("是否走 SCP ")
  private Boolean scp;

  @ApiModelProperty("原寄售转自有订单号")
  private String consignmentToOwnedCode;

  @ApiModelProperty("原寄售转自有订单状态")
  private String consignmentToOwnedState;
  @ApiModelProperty("采购部门编码")
  private String purchaseDeptCode;
  @ApiModelProperty("采购员编码")
  private String purchaseManCode;
  @ApiModelProperty("亏本原因")
  private String causeOfLoss;

  public BaseSupplierOrderDetailDTO(SupplierOrder supplierOrder) {
    this.id = StrUtil.emptyIfNull(supplierOrder.getId());
    this.code = StrUtil.emptyIfNull(supplierOrder.getCode());
    this.orderState =
        BootDictEnumUtil.getEnumByKey(SupplierOrderState.class, supplierOrder.getOrderState())
            .orElseThrow(() -> new CheckException("【" + supplierOrder.getId() + "】该订单状态非法"));
    this.supplierName = StrUtil.emptyIfNull(supplierOrder.getSupplierName());
    this.supplierId = StrUtil.emptyIfNull(supplierOrder.getSupplierId());
    this.directShipment = supplierOrder.getDirectShipment();
    this.receiveMobile = supplierOrder.getReceiveMobile();
    this.purchaseGroupName = StrUtil.emptyIfNull(supplierOrder.getGroupName());
    this.receiveMan = StrUtil.emptyIfNull(supplierOrder.getReceiveMan());
    this.cancelReturnPrice = BigDecimalUtil.formatForStandard(supplierOrder.getCancelReturnPrice());
    this.finalPrice = BigDecimalUtil.setScaleBigDecimalHalfUp(supplierOrder.getFinalPrice(), 2);
    this.purchaseMan = StrUtil.emptyIfNull(supplierOrder.getPurchaseMan());
    this.receiveAddress = StrUtil.emptyIfNull(supplierOrder.getReceiveAddress());
    this.createTime = supplierOrder.getOrderCreateTime();
    this.num = BigDecimalUtil.formatForStandard(supplierOrder.getTotalNum());
    this.price = BigDecimalUtil.formatForStandard(supplierOrder.getPrice());
    this.stockProgress = StrUtil.emptyIfNull(supplierOrder.getStockProgress());
    this.remark = StrUtil.emptyIfNull(supplierOrder.getMark());
    this.customerOrderCode = StrUtil.emptyIfNull(supplierOrder.getCustomerOrderCode());
    this.orderType = StrUtil.emptyIfNull(supplierOrder.getOrderType());
    this.purchaseDept = StrUtil.emptyIfNull(supplierOrder.getPurchaseDept());
    this.invoicingParty = StrUtil.emptyIfNull(supplierOrder.getInvoicingParty());
    this.payCondition = StrUtil.emptyIfNull(supplierOrder.getPayCondition());
    this.accountPeriod = StrUtil.emptyIfNull(supplierOrder.getAccountPeriod());
    this.moneyCode = StrUtil.emptyIfNull(supplierOrder.getMoneyCode());
    this.largeTicketProjectNumber = StrUtil.emptyIfNull(supplierOrder.getProjectNo());
    this.salesOrderNo = StrUtil.emptyIfNull(supplierOrder.getSaleOrderNo());
    this.soldToParty = StrUtil.emptyIfNull(supplierOrder.getSoldToParty());
    this.salesman = StrUtil.emptyIfNull(supplierOrder.getSalesman());
    this.paymentTermsStr = StrUtil.emptyIfNull(supplierOrder.getPaymentTermsStr());
    this.loss = BooleanUtil.isTrue(supplierOrder.getLoss());
    this.scp = BooleanUtil.isTrue(supplierOrder.getScp());
    this.consignmentToOwnedCode=StrUtil.emptyIfNull(supplierOrder.getConsignmentToOwnedCode());
    this.purchaseDeptCode=StrUtil.emptyIfNull(supplierOrder.getPurchaseDeptCode());
    this.purchaseManCode=StrUtil.emptyIfNull(supplierOrder.getPurchaseCode());
    this.causeOfLoss=StrUtil.emptyIfNull(supplierOrder.getCauseOfLoss());
  }
}
