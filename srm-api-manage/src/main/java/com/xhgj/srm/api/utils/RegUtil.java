package com.xhgj.srm.api.utils;

import java.util.regex.Pattern;

public class RegUtil {

    /*
     * 判断是否为整数
     * @param str 传入的字符串
     * @return 是整数返回true,否则返回false
     */
    public static boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    /**
     * 验证整数或小数输入
     *
     * @param str 待验证的字符串
     * @return 如果是符合格式的字符串,返回 <b>true </b>,否则为 <b>false </b>
     */
    public static boolean IsNumberOrDecimal(String str) {
        Pattern pattern = Pattern.compile("\\d+(\\.\\d+)?");
        return pattern.matcher(str).matches();
    }


}
