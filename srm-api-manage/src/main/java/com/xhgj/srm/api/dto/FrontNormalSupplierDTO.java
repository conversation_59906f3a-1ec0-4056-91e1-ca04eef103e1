package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.entity.Supplier;
import com.xhiot.boot.core.common.util.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class FrontNormalSupplierDTO {

    @ApiModelProperty("供应商 id")
    private String id;
    @ApiModelProperty("供应商名称")
    private String enterName;
    @ApiModelProperty("MDM 编码")
    private String mdmCode;
    @ApiModelProperty("账号数量")
    private long AccountNum;

    public FrontNormalSupplierDTO(Supplier supplier) {
        this.id = supplier.getId();
        this.enterName = supplier.getEnterpriseName();
        this.mdmCode = supplier.getMdmCode();
    }

}
