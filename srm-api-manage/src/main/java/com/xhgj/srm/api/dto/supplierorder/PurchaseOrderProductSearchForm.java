package com.xhgj.srm.api.dto.supplierorder;

import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.map.TypeAwareMap;
import com.xhgj.srm.jpa.dto.BaseDefaultSearchSchemeForm;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import com.xhgj.srm.unified.dto.BaseUnifiedForm;
import com.xhgj.srm.unified.dto.UnifiedForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 分页查询采购订单
 */
@Data
public class PurchaseOrderProductSearchForm extends BaseSupplierOrderDTO implements
    BaseDefaultSearchSchemeForm, BaseUnifiedForm {

  @ApiModelProperty("订单状态")
  private String orderStates;

  @ApiModelProperty("搜索方案 id")
  private String schemeId;

  @ApiModelProperty("创建时间 【开始】")
  private Long startCreateTime;

  @ApiModelProperty("创建时间 【结束】")
  private Long endCreateTime;

  @ApiModelProperty("查询未签收的订单")
  private Boolean selectUnReceipt;

  @ApiModelProperty("是否挂起")
  private Boolean pending;

  @ApiModelProperty("是否暂存")
  private Boolean staging;

  @ApiModelProperty("是否审核中")
  private Boolean unaudited;

  @ApiModelProperty("是否驳回")
  private Boolean reject;
  @ApiModelProperty("是否有冲销")
  private Boolean counteractState;

  /**
   * 物料编码
   */
  @ApiModelProperty("物料编码")
  private String productCode;

  @ApiModelProperty("单子是否自采")
  private Boolean selfState;

  @ApiModelProperty("是否免费")
  private Boolean freeState;

  @ApiModelProperty(value = "订单类型")
  private String orderType;

  @ApiModelProperty("采购员")
  private String purchaseMan;

  @ApiModelProperty("采购部门")
  private String purchaseDept;

  @ApiModelProperty("采购申请单号")
  private String purchaseApplyCode;

  @ApiModelProperty("是否预付款")
  private Boolean prePay;

  @ApiModelProperty("是否上传合同")
  private Boolean uploadContract;

  /**
   * 品牌
   */
  @ApiModelProperty("品牌")
  private String brand;

  /**
   * 商品名称
   */
  @ApiModelProperty("物料名称")
  private String name;
  /**
   * 规格型号
   */
  @ApiModelProperty("规格型号")
  private String manuCode;
  /**
   * 物料单位
   */
  @ApiModelProperty("物料单位")
  private String unit;
  @ApiModelProperty("订货数量操作符号")
  private LogicalOperatorsEnums orderNumOperators;
  @ApiModelProperty("订货数量")
  private BigDecimal orderNum;
  @ApiModelProperty("含税单价操作符号")
  private LogicalOperatorsEnums productPriceOperators;
  @ApiModelProperty("含税单价")
  private BigDecimal productPrice;
  @ApiModelProperty("税率")
  private BigDecimal decimalRate;
  @ApiModelProperty("去税单价操作符号")
  private LogicalOperatorsEnums taxFreeCbPriceOperators;
  @ApiModelProperty("去税单价")
  private BigDecimal taxFreeCbPrice;

  @ApiModelProperty("金额操作符号")
  private LogicalOperatorsEnums totalPriceOperators;
  @ApiModelProperty("金额")
  private BigDecimal totalPrice;

  @ApiModelProperty("约定交货时间开始")
  private Long deliverTimeStart;

  @ApiModelProperty("约定交货时间结束")
  private Long deliverTimeEnd;
  @ApiModelProperty("已发数量操作符号")
  private LogicalOperatorsEnums shipQtyOperators;
  @ApiModelProperty("已发数量")
  private BigDecimal shipQty;

  @ApiModelProperty("采购入库数量操作符号")
  private LogicalOperatorsEnums stockInputQtyOperators;
  @ApiModelProperty("采购入库数量")
  private BigDecimal stockInputQty;

  @ApiModelProperty("剩余入库数量操作符号")
  private LogicalOperatorsEnums remainQtyOperators;
  @ApiModelProperty("剩余入库数量")
  private BigDecimal remainQty;

  @ApiModelProperty("实际交货时间开始")
  private Long purchaseDeliverTimeStart;

  @ApiModelProperty("实际交货时间结束")
  private Long purchaseDeliverTimeEnd;

  @ApiModelProperty("仓库")
  private String warehouse;

  @ApiModelProperty("仓库名称")
  private String warehouseName;

  @ApiModelProperty("业务员")
  private String salesman;

  @ApiModelProperty("跟单员")
  private String followUpPersonName;

  @ApiModelProperty("物料描述")
  private String description;

  @ApiModelProperty("供应商名称")
  private String supplierName;

  @ApiModelProperty("是否预付款(全选) 1选中")
  private String allPrePay;

  @ApiModelProperty("是否上传合同(全选) 1选中")
  private String allUploadContract;

  @ApiModelProperty("是否走scp(全选) 1选中")
  private String allScp;

  /**
   * 用户id
   */
  @ApiModelProperty("用户id")
  private String userId;

  @ApiModelProperty("是否亏本订单")
  private Boolean loss;

  /**
   * UnifiedForm
   */
  @ApiModelProperty(hidden = true)
  UnifiedForm unifiedForm;

  /**
   * 用户组
   */
  private String userGroup;

  /**
   * 分页参数 pageNo
   */
  private Integer pageNo;

  /**
   * 分页参数 pageSize
   */
  private Integer pageSize;

  public Integer getPageNo() {
    if (pageNo == null || pageNo < 1) {
      return 1;
    }
    return pageNo;
  }

  public Integer getPageSize() {
    if (pageSize == null || pageSize < 1) {
      pageSize = 25;
    }
    return pageSize;
  }

  /**
   * 转化为查询queryMap
   */
  public Map<String, Object> toQueryMap(MergeUserPermission searchPermission) {
    Map<String, Object> map = new TypeAwareMap<>();
    map.put("pageNo", pageNo);
    map.put("pageSize", pageSize);
    map.put("orderCode", this.getOrderCode());
    if (this.getOrderState() != null) {
      map.put("orderState", this.getOrderState().getKey());
    }
    map.put("startCreateTime", this.getStartCreateTime());
    map.put("endCreateTime", this.getEndCreateTime());
    map.put("supplierName", this.getSupplierName());
    map.put("directShipment", this.getDirectShipment());
    map.put("confirmState", this.getConfirmState());
    map.put("cancelState", this.getCancelState());
    map.put("returnState", this.getReturnState());
    map.put("counteractState", this.getCounteractState());
    map.put("refuseState", this.getRefuseState());
    map.put("shipWaitStock", this.getShipWaitStock());
    map.put("pending", this.getPending());
    map.put("staging", this.getStaging());
    map.put("unaudited", this.getUnaudited());
    map.put("reject", this.getReject());
    map.put("productCode", this.getProductCode());
    map.put("purchaseGroupName", this.getPurchaseGroupName());
    map.put("receiveMan", this.getReceiveMan());
    map.put("supplierOpenInvoiceState", this.getSupplierOpenInvoiceState());
    if (Boolean.TRUE.equals(selectUnReceipt)) {
      map.put("supplierOrderFormType", SupplierOrderFormType.DELIVER.getKey());
      map.put("supplierOrderFormStatus", SupplierOrderFormStatus.WAIT_RECEIPT.getKey());
    }
    map.put("salesOrderNo", this.getSalesOrderNo());
    map.put("largeTicketProjectNumbers", this.getLargeTicketProjectNumbers());
    map.put("largeTicketProjectName", this.getLargeTicketProjectName());
    map.put("selfState", this.getSelfState());
    map.put("freeState", this.getFreeState());
    map.put("orderType", this.getOrderType());
    map.put("purchaseMan", this.getPurchaseMan());
    map.put("purchaseDept", this.getPurchaseDept());
    map.put("purchaseApplyCode", this.getPurchaseApplyCode());
    map.put("prePay", this.getPrePay());
    map.put("uploadContract", this.getUploadContract());
    map.put("scp", this.getScp());
    map.put("allScp", this.getAllScp());
    map.put("brand", this.getBrand());
    map.put("name", this.getName());
    map.put("manuCode", this.getManuCode());
    map.put("unit", this.getUnit());
    map.put("orderNumOperators", this.getOrderNumOperators());
    map.put("orderNum", this.getOrderNum());
    map.put("productPriceOperators", this.getProductPriceOperators());
    map.put("productPrice", this.getProductPrice());
    map.put("decimalRate", this.getDecimalRate());
    map.put("taxFreeCbPriceOperators", this.getTaxFreeCbPriceOperators());
    map.put("taxFreeCbPrice", this.getTaxFreeCbPrice());
    map.put("totalPriceOperators", this.getTotalPriceOperators());
    map.put("totalPrice", this.getTotalPrice());
    map.put("deliverTimeStart", this.getDeliverTimeStart());
    map.put("deliverTimeEnd", this.getDeliverTimeEnd());
    map.put("shipQtyOperators", this.getShipQtyOperators());
    map.put("shipQty", this.getShipQty());
    map.put("stockInputQtyOperators", this.getStockInputQtyOperators());
    map.put("stockInputQty", this.getStockInputQty());
    map.put("warehouse", this.getWarehouse());
    map.put("warehouseName", this.getWarehouseName());
    map.put("salesman", this.getSalesman());
    map.put("followUpPersonName", this.getFollowUpPersonName());
    map.put("description", this.getDescription());
    map.put("allPrePay", this.getAllPrePay());
    map.put("allUploadContract", this.getAllUploadContract());
    map.put("userGroup", userGroup);
    map.put("loss", loss);
    map.put("applyForType", this.getApplyForType());
    map.put("businessCompanyName", this.getBusinessCompanyName());
    map.put("makeManName", this.getMakeManName());
    map.put("isWorryOrder", this.getIsWorryOrder());
    map.put("remainQtyOperators", this.getRemainQtyOperators());
    map.put("remainQty", this.getRemainQty());
    map.put("purchaseDeliverTimeStart", this.getPurchaseDeliverTimeStart());
    map.put("purchaseDeliverTimeEnd", this.getPurchaseDeliverTimeEnd());
    map.put("unifiedForm", unifiedForm);
    map.put("orderStates", orderStates);
    map.put("searchPermission", searchPermission);
    map.put("isViewAllOrganization", this.getIsViewAllOrganization());
    map.put("isHistoricalOrder", this.getIsHistoricalOrder());
    map.put("writeOffState", this.getWriteOffState());
    return map;
  }
}
