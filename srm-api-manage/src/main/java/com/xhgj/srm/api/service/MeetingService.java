package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.MeetingListPage;
import com.xhgj.srm.api.dto.MeetingListPageData;
import com.xhgj.srm.api.dto.MeetingPageData;
import com.xhgj.srm.jpa.entity.Meeting;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;

import java.io.InputStream;

/**
 * <AUTHOR>
 * @since 2021/2/24 09:51
 */
public interface MeetingService extends BootBaseService<Meeting, String> {

    /**
     * 分页获取会议列表
     * @return
     */
    PageResult<MeetingPageData> getMeetingPage(String userGroup, String SupplierName, String starttime, String endtime, String name, String personnel,String userId,String schemeId, int pageNo, int pageSize);





    /**
     * @Description 添加会议纪要
     * @Auther: liuyq
     * @Date: 2021/3/25 15:17
     * @param supplierid
     * @param supplierName
     * @param name
     * @param place
     * @param personnel
     * @param depart
     * @param meettime
     * @param file
     * @param userid
     * @param groupId 组织id
     * @return void
     **/
    void addMeeting(String supplierid, String supplierName, String name, String place, String personnel, String depart, String meettime, String file, String userid,String userGroup);


    /**
     * @Description
     * @Auther: liuyq
     * @Date: 2021/3/25 15:19
     * @param meetingid
     * @param name
     * @param place
     * @param personnel
     * @param depart
     * @param meettime
     * @param file
     * @return void
     **/
    void updateMeeting(String meetingid, String name, String place, String personnel, String depart, String meettime, String file,String supplierId,String supplierName,String userId);

    /**
     * @Description 删除会议纪要
     * @Auther: liuyq
     * @Date: 2021/3/25 15:21
     * @param meeting
     * @return void
     **/
    void delMeeting(String meeting,String userId);


    /**
     * @Description
     * @Auther: liuyq
     * @Date: 2021/3/25 15:22
     * @param erpCode
     * @param name
     * @return void
     **/
    MeetingListPage<MeetingListPageData> getMeetingList(String erpCode, String name);


    /**
     * @Description
     * @Auther: liuyq
     * @Date: 2021/3/25 14:33
     * @param id
     * @param files
     * @param type
     * @return void
     **/
    void uploadFile(String id, String files, String type);

    void deleteFileById(String id, String meetingId);

    void addMeetingExcelSave(String originalFilename, InputStream inputStream,String userId);

    /**
     * 同步多组织会议信息
     */
    void synMeetingGroupInfo();
}
