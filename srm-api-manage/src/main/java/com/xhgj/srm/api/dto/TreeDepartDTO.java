package com.xhgj.srm.api.dto;

import com.xhgj.srm.jpa.entity.Group;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TreeDepartDTO {

    @ApiModelProperty(value = "部门id")
    private String id;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "部门编码")
    private String deptCode;

    @ApiModelProperty(value = "部门ERP编码")
    private String deptErpCode;


    public TreeDepartDTO(Group group) {
        this.id = group.getId();
        this.deptCode = group.getCode();
        this.deptName = group.getName();
        this.deptErpCode = group.getErpCode();
    }
}
