package com.xhgj.srm.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class SupplierRateRecordVO {

  /**
   * 平台
   */
  @ApiModelProperty(value = "平台")
  private String platform;

  /**
   * 旧数据
   */
  @ApiModelProperty(value = "旧数据")
  private String oldData;

  /**
   * 新数据
   */
  @ApiModelProperty(value = "新数据")
  private String newData;

  /**
   * 创建时间
   */
  @ApiModelProperty(value = "创建时间")
  private Long createTime;

  /**
   * 创建人
   */
  @ApiModelProperty(value = "创建人")
  private String createMan;

  /**
   * 修改字段
   */
  @ApiModelProperty(value = "修改字段")
  private String updateField;
}
