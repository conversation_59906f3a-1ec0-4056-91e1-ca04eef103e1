package com.xhgj.srm.api.task;

import com.xhgj.srm.api.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

public class MeetingMailTask {

    @Autowired
    UserService userService;

    /**
     * 定时发送邮件
     */
    @Scheduled(cron = "0 0 23 ? * L")
    private void sendMeetingMailTask() {
        userService.sendMeetingMail();
    }
}
