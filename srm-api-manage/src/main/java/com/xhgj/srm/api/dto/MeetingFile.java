package com.xhgj.srm.api.dto;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.File;
import com.xhiot.boot.core.common.util.StringUtils;
import lombok.Data;

/**
 * @ClassName MettingFile
 * Create by Liuyq on 2021/3/30 13:38
 **/
@Data
public class MeetingFile {

    private String id;
    private String uid;
    private String name;
    private String type;
    private String url;

    public MeetingFile(File file, String baseUrl) {
        this.id = file.getId();
//        this.uid = file.getId();
        String fileName = StrUtil.emptyIfNull(file.getName());
        String fileUrl = StrUtil.emptyIfNull(file.getUrl());
        if (!StringUtils.isNullOrEmpty(fileUrl) && !fileUrl.contains("srm/")) {
            fileUrl = "srm" + StrUtil.addPrefixIfNot(fileUrl, "/");
        }
        this.name = fileName + ";" + fileUrl;
        this.url = baseUrl + fileUrl;
        this.type = file.getType() != null && !"".equals(file.getType()) ? file.getType() : "";
    }
}
