package com.xhgj.srm.api.dto.supplierorder;

import com.xhgj.srm.api.dto.PurchaseOrderPageQuery;
import com.xhgj.srm.common.dto.exportfiled.ExportFiledSelectDTO;
import com.xhgj.srm.common.dto.exportfiled.ExportTemplateParamProvider;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/28 17:25
 */
@Data
public class ExportOrderDetailsParams implements ExportTemplateParamProvider {

  @ApiModelProperty("筛选订单 id")
  private List<String> ids;

  @ApiModelProperty("筛选条件")
  private PurchaseOrderPageQuery query;

  @NotBlank(message = "组织 必传")
  @ApiModelProperty("组织")
  private String userGroup;

  @ApiModelProperty("模板 id")
  private String templateId;

  @ApiModelProperty("模板名称")
  private String templateName;

  @ApiModelProperty("是否保存")
  private Boolean save;

  @ApiModelProperty("选中字段集合")
  private List<ExportFiledSelectDTO> selectFieldList;
}
