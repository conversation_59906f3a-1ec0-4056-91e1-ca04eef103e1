package com.xhgj.srm.api.aop;/**
 * @since 2025/4/17 19:18
 */

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.VerifyConfigTypeEnum;
import com.xhgj.srm.jpa.annotations.VersionQuery;
import com.xhgj.srm.jpa.entity.VerifyConfig;
import com.xhgj.srm.jpa.repository.VerifyConfigRepository;
import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.jpa.sharding.util.UserGroupContext;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.List;

/**
 *<AUTHOR>
 *@date 2025/4/17 19:18:11
 *@description
 */
@Aspect
@Component
@Slf4j
public class ThreadControllerGroupAspect {

  /**
   * 记录当前线程切面嵌套深度
   */
  private static final ThreadLocal<Integer> ASPECT_DEPTH = new ThreadLocal<>();
  @Resource
  VerifyConfigRepository verifyConfigRepository;

  @Before("execution(* com.xhgj.srm.api.controller.v2Mix..*.*(..))")
  public void modifyThreadLocalBeforeExecution(JoinPoint joinPoint) {
    // 获取UserGroupContext
    String userGroup = UserGroupContext.getUserGroup();
    Integer depth = ASPECT_DEPTH.get();
    if (StrUtil.isNotBlank(userGroup) || depth != null) {
      if (depth == null) {
        depth = 0;
      }
      log.info("ThreadControllerGroupAspect modifyThreadLocalBeforeExecution");
      // 根据userGroup查询
      if (ShardingContext.getVersion() == null) {
        VerifyConfig verifyConfig = verifyConfigRepository.findFirstByConfigTypeAndEnable(
            VerifyConfigTypeEnum.WORKBENCH_TWO_ZERO_AVAILABLE_ORG.getCode(), Boolean.TRUE);
        List<String> organizationRoleList = verifyConfig.getOrganizationRoleList();
        if (organizationRoleList.contains(userGroup)) {
          // 获取方法上的注解信息
          MethodSignature signature = (MethodSignature) joinPoint.getSignature();
          Method method = signature.getMethod();
          // 默认查ALL版本
          VersionEnum versionEnum = VersionEnum.ALL;
          // 检查方法上是否有VersionQuery注解
          VersionQuery methodAnnotation = method.getAnnotation(VersionQuery.class);
          if (methodAnnotation != null) {
            versionEnum = methodAnnotation.value();
          } else {
            // 如果方法上没有，检查类上是否有注解
            Class<?> clazz = method.getDeclaringClass();
            VersionQuery classAnnotation = clazz.getAnnotation(VersionQuery.class);
            if (classAnnotation != null) {
              versionEnum = classAnnotation.value();
            }
          }
          ShardingContext.setVersion(versionEnum);
          ASPECT_DEPTH.set(depth + 1);
        }
      }
    }
  }

  @After("execution(* com.xhgj.srm.api.controller.v2Mix..*.*(..))")
  public void cleanupThreadLocal() {
    Integer depth = ASPECT_DEPTH.get();
    if (depth != null) {
      depth = depth - 1;
      // 只有最外层方法执行完才清理ThreadLocal
      if (depth == 0) {
        log.info("ThreadControllerGroupAspect cleanupThreadLocal");
        // 只清理当前线程上下文中存在的版本信息
        if (ShardingContext.getVersion() != null) {
          ShardingContext.clear();
        }
        ASPECT_DEPTH.remove();
      } else {
        ASPECT_DEPTH.set(depth);
      }
    }
  }
}
