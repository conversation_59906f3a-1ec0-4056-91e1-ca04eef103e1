package com.xhgj.srm.api.controller.dataProcess;/**
 * @since 2025/4/10 19:06
 */

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xhgj.srm.dto.dataProcess.DataProcessForm;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.UUID;

/**
 *<AUTHOR>
 *@date 2025/4/10 19:06:14
 *@description
 */
@RestController
@RequestMapping("/dataProcess")
public class DataProcessController {

  @Resource
  MissionDispatcher missionDispatcher;

  @PostMapping
  public void process(@RequestBody DataProcessForm dataProcessForm) {
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("className", dataProcessForm.getClassName());
    if (StrUtil.isNotBlank(dataProcessForm.getParams())) {
      JSONObject paramsJson = JSON.parseObject(dataProcessForm.getParams());
      for (String key : paramsJson.keySet()) {
        jsonObject.put(key, paramsJson.get(key));
      }
    }
    missionDispatcher.doDispatch("dataProcess" + UUID.randomUUID().toString().replaceAll("-", ""), jsonObject.toJSONString(),
        MissionTypeEnum.BATCH_TASK_OLD_DATA);
  }

}
