package com.xhgj.srm.api.dto.supplierorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2022/11/29 13:34
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SupplierOrderPageQuery extends BaseSupplierOrderDTO {
  @ApiModelProperty("搜索方案 id")
  private String schemeId;

  @ApiModelProperty("创建时间 【开始】")
  private Long startCreateTime;

  @ApiModelProperty("创建时间 【结束】")
  private Long endCreateTime;

  @ApiModelProperty("查询未签收的订单")
  private Boolean selectUnReceipt;
}
