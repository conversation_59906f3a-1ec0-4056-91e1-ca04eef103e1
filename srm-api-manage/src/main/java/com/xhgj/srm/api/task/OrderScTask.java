package com.xhgj.srm.api.task;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.api.task.DTO.OmsInvoiceStatusDTO;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.enums.InvoiceStatusEnums;
import com.xhgj.srm.api.service.OrderService;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.entity.Order;
import com.xhgj.srm.jpa.repository.OrderRepository;
import com.xhgj.srm.service.OrderAcceptService;
import com.xhiot.boot.mvc.base.ResultBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 *@ClassName OrderScTask
 *<AUTHOR>
 *@Date 2023/7/4 10:54
 */
@Component
@Slf4j
public class OrderScTask {
  @Resource
  private OrderService orderService;
  @Resource
  private OrderRepository orderRepository;
  @Resource
  private HttpUtil httpUtil;
  @Resource
  private OrderDao orderDao;

  /**
   * 3201特殊处理 交建工业品
   */
  public static final String SUB_TYPE_3201 = "3201";



  /*  @Scheduled(cron = "0 0 2 ? * *")
    private void  syncOrderTask() {
      log.info("同步数仓，订单状态开始============================");
      List<Order> orderListByInvoicingState =
          orderService.getOrderListByInvoicingState();
      for (Order order : orderListByInvoicingState) {
        //数仓存在该订单 修改状态
        if(orderService.getScOrderCount(order.getErpOrderNo()) != 0l){
          order.setSupplierOpenInvoiceStatus(InvoiceStatusEnums.INVOICE_STATUS_YES.getStatus());
          orderService.update(order);
          //签收凭证已完成  客户已回款 对账单（供应商）开票状态已开票 的付款状态改为待申请
          String acceptState = orderAcceptService.getAcceptState(order.getId());
          if(Constants_order.ORDER_ACCEPT_CONSENT.equals(acceptState) &&
              Constants_order.CUSTOMER_PAYBACK_CONFIRM.equals(order.getCustomerReturnProgress()) &&
              Constants_order.ORDER_ACCOUNT_OPEN_INVOICED.equals(order.getSupplierOpenInvoiceStatus())
          ){
            order.setPaymentStatus(Constants_order.WAIT_APPLY_PAYMENT_TYPE);
            orderService.update(order);
          }
        }
      }
      log.info("同步数仓，订单状态结束============================");
    }*/

  @Scheduled(cron = "0 0 4 * * ?")
  private void updateOrderInvoiceStateAt530AM() {
    updateOrderInvoiceState();
  }
//  @Scheduled(cron = "0 0 12 * * ?")
//  private void updateOrderInvoiceStateAt12PM() {
//    updateOrderInvoiceState();
//  }
//  @Scheduled(cron = "0 0 20 * * ?")
//  private void updateOrderInvoiceStateAt8PM() {
//    updateOrderInvoiceState();
//  }

  @Scheduled(cron = "0 0 1 * * ?")
  private void updateOrderCustomerAcceptTimeAt1AM() {
    updateOrderCustomerAcceptTime();
  }

  @Scheduled(cron = "0 0 1 * * ?")
  private void updateOrderCustomerInvoiceTimeAt1AM() {
    updateOrderCustomerInvoiceTime();
  }

  public void updateOrderCustomerAcceptTime() {
    log.info("同步履约订单客户签收时间开始============================");
    List<Order> orderList =
        orderRepository.findAllByStateAndSupplierOrderIdIsNotNullAndCustomerAcceptTimeIsNull(Constants.STATE_OK);
    for (Order order : orderList) {
      String result = httpUtil.updateOrderCustomerAcceptTime(order.getOrderNo(), order.getType(),
          order.getSupplierOrderId());
      if (StrUtil.isBlank(result)) {
        continue;
      }
      ResultBean<OmsInvoiceStatusDTO> resultBean =
          JSON.parseObject(result, new TypeReference<ResultBean<OmsInvoiceStatusDTO>>() {});
      if (ObjectUtil.notEqual(ResultBean.SUCCESS, resultBean.getCode())) {
        continue;
      }
      OmsInvoiceStatusDTO data = resultBean.getData();
      if (data == null || data.getCustomerAcceptTime() == null
          || data.getCustomerAcceptTime() == 0) {
        continue;
      }
      orderDao.optimisticLockUpdateOrder(order, o -> o.setCustomerAcceptTime(data.getCustomerAcceptTime()));
      }
    log.info("同步履约订单客户签收时间结束============================");
  }

  public void updateOrderCustomerInvoiceTime() {
    log.info("同步履约订单客户开票时间开始============================");
    List<Order> orderList =
        orderRepository.findAllByStateAndInvoicingStateAndCustomerInvoiceTimeIsNull(Constants.STATE_OK,Constants_order.INVOICE_STATE_DONE);
    for (Order order : orderList) {
      OmsInvoiceStatusDTO invoiceDTO =
          orderService.getInvoiceStatusByOrderNo(order.getOrderNo(), order.getType());
      if (invoiceDTO != null) {
        Long invoiceTime = invoiceDTO.getCustomerInvoiceTime();
        if (invoiceTime != null && invoiceTime > 0) {
          orderDao.optimisticLockUpdateOrder(order, o -> o.setCustomerInvoiceTime(invoiceTime));
        }
      }
    }
    log.info("同步履约订单客户开票时间结束============================");
  }


  public void updateOrderInvoiceState() {
    log.info("同步履约订单客户开票状态开始============================");
    List<Order> orderListByInvoicingState =
        orderService.getAllOrderNo().stream().filter(order -> Constants_order.INVOICE_STATE_UN.equals(order.getInvoicingState()) ||
            Constants_order.INVOICE_STATE_HAND.equals(order.getInvoicingState())).collect(
            Collectors.toList());
    for (Order order : orderListByInvoicingState) {
      //查询履约该订单的开票状态 若已开票即修改为已开票
      String type = order.getType();
      // 判断subType是否为3401
      // 3201特殊处理
      if (SUB_TYPE_3201.equals(order.getSubType())) {
        type = order.getSubType();
      }
      OmsInvoiceStatusDTO invoiceDTO =
          orderService.getInvoiceStatusByOrderNo(order.getOrderNo(), type);
      if (invoiceDTO == null) {
        continue;
      }
      String invoiceStatus = invoiceDTO.getInvoiceStatus();
      orderDao.optimisticLockUpdateOrder(order, o -> {
        if(StrUtil.equals(invoiceStatus,"已开票")){
          o.setInvoicingState(Constants_order.INVOICE_STATE_DONE);
        }
        if(StrUtil.equals(invoiceStatus,"部分开票")){
          o.setInvoicingState(Constants_order.INVOICE_STATE_HAND);
        }
      });
    }
    log.info("同步履约订单客户开票状态结束============================");
  }

  @Scheduled(cron = "0 0 6 ? * *")
  private void  setPayStateTask() {
    log.info("设置满足条件的订单付款状态,任务开始============================");
    List<Order> orderList =
        orderRepository.findAllByCustomerReturnProgressAndSupplierOpenInvoiceStatusAndPaymentStatusAndState(
            Constants_order.RETURN_PROGRESS_ALL, Constants.ORDER_INVOICE_STATE_PASS,
            Constants_order.CAN_NOT_PAYMENT_TYPE,Constants.STATE_OK);
    for (Order order : orderList) {
      orderService.setAccountStatusAllow(order);
    }
    log.info("设置满足条件的订单付款状态,任务结束============================");
  }


}
