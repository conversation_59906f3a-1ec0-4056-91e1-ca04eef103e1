package com.xhgj.srm.api.service;

import com.xhgj.srm.jpa.entity.PermissionType;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

/** <AUTHOR> @ClassName UserToGroupService */
public interface PermissionTypeService extends BootBaseService<PermissionType, String> {

  /**
   * 新增或修改用户数据权限 @Author: liuyq @Date: 2022/7/8 13:58
   *
   * @param permissionCode 数据权限
   * @param userId 用户id 必传
   * @param type 类型 必传
   * @return void
   */
  void saveOrUpdateUserToPermission(String permissionCode, String userId, String type);

  /**
   * @Auhor: liuyq @Date: 2022/8/17 17:32 根据用户和数据范围/操作范围类型获取权限编码
   *
   * @param userId 用户id，必传
   * @param type 数据范围/操作范围类型，必传
   * @return java.lang.String
   */
  String getUserPermissionCodeByUserIdAndType(String userId, String type);

  /**
   * Author: liuyq @Date: 2022/8/17 17:58 根据用户id 和权限编码获取权限内 用户id集合
   *
   * @param userId 用户id，必传
   * @param code 权限编码，必传
   * @return java.util.List<java.lang.String>
   */
  List<String> getUserIdList(String code, String userId);

/**
   * Author: liuyq @Date: 2022/8/17 17:58 根据用户id 和权限编码获取权限内 用户id集合
   *
   * @param userId 用户id，必传
   * @param code 权限编码，必传
   * @return java.util.List<java.lang.String>
   */
  List<String> getUserIdList(String code, String userId,List<String> role);

  /**
   * Author: liuyq @Date: 2022/8/17 17:58 根据用户id获取权限内 用户id集合
   *
   * @param userId 用户id，必传
   * @return java.util.List<java.lang.String>
   */
  List<String> getUserIdListByType(String userId, String type,List<String> role);

  /**
   * Author: liuyq @Date: 2022/8/17 17:58 根据用户id 和权限编码获取权限内 用户名称集合
   *
   * @param userId 用户id，必传
   * @param type 类型，必传
   * @return java.util.List<java.lang.String>
   */
  List<String> getUserNameList(String userId, String type,List<String> role);

  List<String> getConcatNumUserNameList(String userId, String type, List<String> role);

  PermissionType findFirstByUserIdAndType(String userId, String type);
}
