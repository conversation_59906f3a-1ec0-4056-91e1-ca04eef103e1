package com.xhgj.srm.api.factory;/**
 * @since 2025/2/10 19:13
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.returnExchangeOrder.AddNewReturnForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeSaveForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeSaveForm.ReturnProduct;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderInvoiceRelation;
import com.xhgj.srm.api.service.SupplierOrderToFormService;
import com.xhgj.srm.api.utils.ManageSecurityUtil;
import com.xhgj.srm.api.vo.returnExchange.NewReturnProductDetailVO;
import com.xhgj.srm.api.vo.returnExchange.NewReturnVO;
import com.xhgj.srm.api.vo.returnExchange.ReturnExchangeProductDetailVO;
import com.xhgj.srm.api.vo.returnExchange.ReturnExchangeVO;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.dto.FileDTOExt;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.SimpleBooleanEnum;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormExecutionStatusEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormStatus;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.returnExchangeOrder.OrderNumberCleanerAndGenerator;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.dto.InputInvoiceOrderWithDetail;
import com.xhgj.srm.jpa.dao.SupplierOrderDetailDao;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.InventoryLocation;
import com.xhgj.srm.jpa.entity.ReturnExchangeOrder;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.InventoryLocationRepository;
import com.xhgj.srm.jpa.repository.ReturnExchangeOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.service.ShareInputInvoiceService;
import com.xhiot.boot.core.common.exception.CheckException;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/2/10 19:13:18
 *@description
 */
@Component
public class ReturnExchangeOrderFactory {
  @Resource
  SupplierRepository supplierRepository;
  @Resource
  GroupRepository groupRepository;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;
  @Resource
  private ReturnExchangeOrderRepository returnExchangeOrderRepository;
  @Resource
  private ManageSecurityUtil manageSecurityUtil;
  @Resource
  private SupplierOrderToFormService supplierOrderToFormService;
  @Resource
  private SupplierOrderFactory supplierOrderFactory;
  @Resource
  private ShareInputInvoiceService shareInputInvoiceService;
  @Resource
  private SupplierOrderDetailDao supplierOrderDetailDao;
  @Resource
  private InventoryLocationRepository inventoryLocationRepository;

  /**
   * 校验除免费行外，税率一致性
   * @param saveForm
   */
  public void checkRateWithoutFreeLine(ReturnExchangeSaveForm saveForm) {
    List<ReturnProduct> returnProductList = saveForm.getReturnProductList();
    if (CollUtil.isEmpty(returnProductList)) {
      return;
    }
    // 过滤掉免费行
    returnProductList = returnProductList.stream()
        .filter(item -> !Constants.YES.equals(item.getFreeState()))
        .collect(Collectors.toList());
    BigDecimal orginRate = null;
    // 校验税率一致性
    for (ReturnProduct product : returnProductList) {
      BigDecimal taxRate = product.getTaxRate()  == null ? BigDecimal.ZERO : product.getTaxRate();
      if (orginRate == null) {
        orginRate = taxRate;
      }
      if (taxRate.compareTo(orginRate) != 0) {
        throw new CheckException("订单物料行的税率必须一致，请检查订单税率");
      }
    }
  }

  /**
   * 寄售判断
   */
  public void checkConsign(ReturnExchangeSaveForm saveForm) {
    List<ReturnProduct> returnProductList = saveForm.getReturnProductList();
    for (ReturnProduct returnProduct : returnProductList) {
      // 项目类别与是否免费校验
      if (ObjectUtil.equals(Constants.PROJECT_TYPE_JS, saveForm.getProjectType())
          && !ObjectUtil.equals(SimpleBooleanEnum.NO.getKey(), returnProduct.getFreeState())) {
        throw new CheckException("项目类别为“寄售”时，是否免费只能是“否”");
      }
    }
  }

  /**
   * 折价判断
   * @param saveForm
   */
  public void checkLoss(ReturnExchangeSaveForm saveForm) {
    if (Boolean.FALSE.equals(saveForm.getLoss())) {
      List<String> rowError = new ArrayList<>();
      for (int i = 0; i < saveForm.getReturnProductList().size(); i++) {
        ReturnProduct returnProduct = saveForm.getReturnProductList().get(i);
        // 亏本：本次退货本次退货去税总价若低于入库原价
        BigDecimal returnTotalPrice = returnProduct.getTotalNetPrice();
        BigDecimal originalTotalPrice = returnProduct.getOriginalTotalPrice();
        if (originalTotalPrice == null) {
          continue;
        }
        if (NumberUtil.isLess(returnTotalPrice, originalTotalPrice)) {
          rowError.add((i + 1) + "行");
        }
      }
      if (CollUtil.isNotEmpty(rowError)) {
        throw new CheckException(StrUtil.join("、", rowError) +
            "，本次退货去税总价若低于原单总价，无法提交。");
      }
    }
  }

  /**
   * 是否厂直发判断
   */
  public Boolean checkDirect(ReturnExchangeSaveForm saveForm) {
    boolean direct = false;
    List<ReturnProduct> returnProductList = saveForm.getReturnProductList();
    for (ReturnProduct returnProduct : returnProductList) {
      if (WarehouseEnum.HAI_NING_DIRECT_SALES.getCode().equals(returnProduct.getWarehouse())) {
        direct = true;
        break;
      }
    }
    return direct;
  }

  public void checkReturnOrder(String originalOrderNo) {
    if (StrUtil.isBlank(originalOrderNo)) {
      throw new CheckException("退货原单号不能空");
    }
    // 通过returnOrderNo查询supplierOrder
    SupplierOrder supplierOrder =
        supplierOrderRepository.findFirstByCodeAndState(originalOrderNo, Constants.STATE_OK);
    if (supplierOrder != null) {
      throw new CheckException("不能与本期订单号一致");
    }
  }

  /**
   * #check 查询库位判断是否涉及wms采购订单
   */
  public void checkWms(List<SupplierOrderDetail> supplierOrderDetails, String groupCode) {
    List<String> warehouseList = supplierOrderDetails.stream().map(item -> item.getWarehouse()).distinct()
        .collect(Collectors.toList());
    for (String warehouse : warehouseList) {
      InventoryLocation inventoryLocation =
          inventoryLocationRepository.findFirstByGroupCodeAndWarehouseAndState(groupCode, warehouse,
              Constants.STATE_OK).orElseThrow(() -> new CheckException("仓库不存在"));
      boolean b = inventoryLocation.hasWmsOrder();
      if (b) {
        throw new CheckException(inventoryLocation.getWarehouseName() + "库位在WMS中管理，请联系您公司的仓库同事在WMS出库");
      }
    }
  }

  /**
   * 创建
   * @param saveForm
   * @return
   */
  public ReturnExchangeOrder create(ReturnExchangeSaveForm saveForm) {
    ReturnExchangeOrder returnExchangeOrder = null;
    if (StrUtil.isBlank(saveForm.getId())) {
      // 新增
      returnExchangeOrder = new ReturnExchangeOrder();
      returnExchangeOrder.setReturnReason(saveForm.getReturnReason());
      returnExchangeOrder.setOriginalOrderNo(saveForm.getOriginalOrderNo());
      returnExchangeOrder.setNeedRedInvoice(saveForm.getNeedRedInvoice());
      returnExchangeOrder.setCreateTime(System.currentTimeMillis());
      returnExchangeOrder.setUpdateTime(System.currentTimeMillis());
      returnExchangeOrder.setProjectType(saveForm.getProjectType());
      returnExchangeOrder.setState(Constants.STATE_OK);
    }else {
      // 修改
      returnExchangeOrder = returnExchangeOrderRepository.findById(saveForm.getId())
          .orElseThrow(() -> new CheckException("退换货单不存在"));
      // 更新
      returnExchangeOrder.setReturnReason(saveForm.getReturnReason());
      returnExchangeOrder.setOriginalOrderNo(saveForm.getOriginalOrderNo());
      returnExchangeOrder.setNeedRedInvoice(saveForm.getNeedRedInvoice());
      returnExchangeOrder.setUpdateTime(System.currentTimeMillis());
      returnExchangeOrder.setProjectType(saveForm.getProjectType());
    }
    return returnExchangeOrder;
  }

  public SupplierOrder createLinkSupplierOrder(ReturnExchangeOrder returnExchangeOrder, ReturnExchangeSaveForm saveForm) {
    SupplierOrder supplierOrder = null;
    // 货币汇率默认1
    BigDecimal exchangeRate = BigDecimal.ONE;
    // 厂直发判断
    Boolean direct = this.checkDirect(saveForm);
    Group group =
        groupRepository.findFirstByErpCodeAndState(saveForm.getUserGroup(), Constants.STATE_OK);
    if (group == null) {
      throw new CheckException("采购组织不存在");
    }
    String groupName = group.getName();
    // 采购部门
    Group purchaseDept = groupRepository.findFirstByErpCodeAndState(saveForm.getPurchaseDeptCode(),
        Constants.STATE_OK);
    if (purchaseDept == null) {
      throw new CheckException("采购部门不存在");
    }
    if (!Objects.equals(group.getCode(), purchaseDept.getGroupCode())) {
      throw new CheckException("采购部门不属于该采购组织");
    }
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    Supplier supplier = supplierRepository.findById(saveForm.getSupplierId())
        .orElseThrow(() -> new CheckException("供应商不存在"));
    String supplierName = supplier.getEnterpriseName();
    if (returnExchangeOrder.getSupplierOrderId() == null) {
      // 新增
      supplierOrder = new SupplierOrder();
      // 生成唯一订单号
      supplierOrder.setCode(OrderNumberCleanerAndGenerator.INSTANCE.generate());
      supplierOrder.setOrderCreateTime(System.currentTimeMillis());
      supplierOrder.setCreateTime(System.currentTimeMillis());
      supplierOrder.setCreateMan(user.getId());
      supplierOrder.setState(Constants.STATE_LOCKED);
    }else{
      // 修改
      supplierOrder = supplierOrderRepository.findById(returnExchangeOrder.getSupplierOrderId())
          .orElseThrow(() -> new CheckException("退换货单关联单不存在"));
      supplierOrder.setUpdateMan(user.getId());
      supplierOrder.setUpdateTime(System.currentTimeMillis());
      // 如果有取消订单不可进行编辑
      if (supplierOrderToFormService.countByTypeAndSupplierOrderIdAndState(
          SupplierOrderFormType.CANCEL, supplierOrder.getId())>0) {
        throw new CheckException("该订单存在取消单无法进行编辑");
      }
      // 首次提交校验
      boolean firstSubmitFlag =
          supplierOrderFactory.checkSupplierOrderFirstSubmitFlag(saveForm.getSaveType(),
              supplierOrder.getOrderState());
      if (firstSubmitFlag) {
        supplierOrderFactory.checkSupplierOrderFirstSubmit(supplierOrder.getId(),
            saveForm.getReturnProductList().size(),
            supplierName,
            saveForm.getSupplierId(),
            supplierOrder.getSupplierName(),
            supplierOrder.getSupplierId());
      }
    }
    // 统一保存内容
    supplierOrder.setOrderState(SupplierOrderState.UNAUDITED.getOrderState());
    if (saveForm.getSaveType() == 1) {
      supplierOrder.setOrderState(SupplierOrderState.STAGING.getOrderState());
    }
    supplierOrder.setSupplierId(saveForm.getSupplierId());
    supplierOrder.setSupplierName(supplierName);
    supplierOrder.setGroupCode(saveForm.getUserGroup());
    supplierOrder.setGroupName(groupName);
    supplierOrder.setPurchaseTime(System.currentTimeMillis());
    supplierOrder.setDirectShipment(direct);
    supplierOrder.setPrice(saveForm.getTotalAmountIncludingTax());
    supplierOrder.setReceiveMobile(saveForm.getReceiveMobile());
    supplierOrder.setReceiveMan(saveForm.getReceiveMan());
    supplierOrder.setPurchaseMan(saveForm.getPurchaseMan());
    supplierOrder.setReceiveAddress(saveForm.getReceiveAddress());
    supplierOrder.setOrderConfirmState(false);
    supplierOrder.setOrderCancelState(false);
    supplierOrder.setOrderReturnState(false);
    supplierOrder.setOrderShipWaitStockState(false);
    supplierOrder.makeAndSetStockProgress(BigDecimal.ZERO, saveForm.getTotalNum());
    supplierOrder.setTotalNum(saveForm.getTotalNum());
    supplierOrder.setState(Constants.STATE_LOCKED);
    supplierOrder.setRefuseState(Constants.STATE_NO);
    supplierOrder.setSupplierOpenInvoiceState(Constants.ORDER_INVOICE_STATE_NOT_DONE);
    supplierOrder.setOrderType(PurchaseOrderTypeEnum.RETURN_EXCHANGE.getKey());
    supplierOrder.setPurchaseDept(purchaseDept.getName());
    supplierOrder.setPurchaseDeptCode(saveForm.getPurchaseDeptCode());
    supplierOrder.setPurchaseCode("xhgj00"+ saveForm.getPurchaseMan().substring(0,4));
    supplierOrder.setInvoicingParty(saveForm.getInvoicingParty());
    supplierOrder.setMoneyCode(saveForm.getMoneyCode());
    supplierOrder.setOrderRate(exchangeRate.stripTrailingZeros().toPlainString());
    supplierOrder.setSupContacts(saveForm.getSupContacts());
    supplierOrder.setSupMobile(saveForm.getSupMobile());
    supplierOrder.setSupEmail(saveForm.getSupEmail());
    supplierOrder.setSupFax(saveForm.getSupFax());
    supplierOrder.setInvoiceType(saveForm.getInvoiceType());
    supplierOrder.setLoss(saveForm.getLoss());
    return supplierOrder;
  }

  public List<SupplierOrderProduct> createLinkSupplierOrderProduct(List<ReturnProduct> returnProductList) {
    if (CollUtil.isEmpty(returnProductList)) {
      return new ArrayList<>();
    }
    List<SupplierOrderProduct> res = new ArrayList<>();
    for (ReturnProduct returnProduct : returnProductList) {
      SupplierOrderProduct supplierOrderProduct = new SupplierOrderProduct();
      supplierOrderProduct.setCode(returnProduct.getProductCode());
      supplierOrderProduct.setBrand(returnProduct.getBrand());
      supplierOrderProduct.setName(returnProduct.getProductName());
      supplierOrderProduct.setManuCode(returnProduct.getManuCode());
      supplierOrderProduct.setModel(returnProduct.getModel());
      supplierOrderProduct.setSpecification(returnProduct.getSpecification());
      supplierOrderProduct.setUnit(returnProduct.getUnit());
      supplierOrderProduct.setUnitCode(returnProduct.getUnitCode());
      supplierOrderProduct.setUnitDigit(3);
      res.add(supplierOrderProduct);
    }
    return res;
  }

  public List<SupplierOrderDetail> createLinkSupplierOrderDetail(
      String supplierOrderId,
      String orderFormId,
      String projectType,
      Boolean needRedInvoice,
      List<ReturnProduct> returnProductList,
      List<SupplierOrderProduct> linkSupplierOrderProduct
      ) {
    if (CollUtil.isEmpty(returnProductList)) {
      return new ArrayList<>();
    }
    List<SupplierOrderDetail> res = new ArrayList<>();
    int index = 0;
    for (ReturnProduct returnProduct : returnProductList) {
      SupplierOrderProduct supplierOrderProduct = linkSupplierOrderProduct.get(index);
      SupplierOrderDetail supplierOrderDetail = new SupplierOrderDetail();
      supplierOrderDetail.setBatchNo(returnProduct.getBatchNo());
      supplierOrderDetail.setOrderProductId(supplierOrderProduct.getId());
      supplierOrderDetail.setOrderToFormId(orderFormId);
      supplierOrderDetail.setSapRowId(String.valueOf(returnProduct.getIndexNum()));
      // 退换货订单中的物料明细
      supplierOrderDetail.setNum(BigDecimalUtil.setScaleBigDecimalHalfUp(returnProduct.getNum(),3));
      supplierOrderDetail.setSortNum(returnProduct.getIndexNum());
      supplierOrderDetail.setWaitQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      supplierOrderDetail.setShipQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      supplierOrderDetail.setReturnQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"), 3));
      supplierOrderDetail.setCancelQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      supplierOrderDetail.setStockInputQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      supplierOrderDetail.setWaitStockInputQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      supplierOrderDetail.setRemainQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      supplierOrderDetail.setStockOutputQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"),3));
      supplierOrderDetail.setSettleQty(BigDecimalUtil.setScaleBigDecimalHalfUp(new BigDecimal("0"), 3));
      supplierOrderDetail.setTotalPrice(returnProduct.getTotalPrice());
      supplierOrderDetail.setMark(returnProduct.getMark());
      supplierOrderDetail.setTaxRate(returnProduct.getTaxRate());
      supplierOrderDetail.setCreateTime(System.currentTimeMillis());
      supplierOrderDetail.setState(Constants.STATE_OK);
      supplierOrderDetail.setPrice(returnProduct.getPrice());
      supplierOrderDetail.setDescription(returnProduct.getDescription());
      supplierOrderDetail.setWarehouse(returnProduct.getWarehouse());
      supplierOrderDetail.setWarehouseName(returnProduct.getWarehouseName());
      supplierOrderDetail.setTotalAmountIncludingTax(returnProduct.getTotalPrice());
      supplierOrderDetail.setFreeState(returnProduct.getFreeState());
      supplierOrderDetail.setProjectType(projectType);
      supplierOrderDetail.setPurchaseOrderId(supplierOrderId);
      supplierOrderDetail.setOpenRedInvoice(needRedInvoice);
      supplierOrderDetail.setOriginalTotalPrice(returnProduct.getOriginalTotalPrice());
      supplierOrderDetail.setOriginalPrice(returnProduct.getOriginalPrice());
      supplierOrderDetail.setReturnFlag(returnProduct.getReturnFlag());
      // todo InvoicableNum是多少？
//      supplierOrderDetail.setInvoicableNum(BigDecimalUtil.setScaleBigDecimalHalfUp(returnProduct.getNum(),3));
      supplierOrderDetail.setInvoicableNum(BigDecimal.ZERO);
      supplierOrderDetail.setInvoicedNum(BigDecimal.ZERO);
//      supplierOrderDetail.setProductRate(returnProduct.getProductRate());
      res.add(supplierOrderDetail);
      index++;
    }
    return res;
  }

  public SupplierOrderToForm createLinkSupplierOrderForm(String supplierOrderId, ReturnExchangeSaveForm saveForm) {
    SupplierOrderToForm supplierOrderToForm = new SupplierOrderToForm();
    supplierOrderToForm.setSupplierOrderId(supplierOrderId);
    supplierOrderToForm.setType(SupplierOrderFormType.DETAILED.getType());
    supplierOrderToForm.setCreateTime(System.currentTimeMillis());
    supplierOrderToForm.setState(Constants.STATE_OK);
    supplierOrderToForm.setNum(saveForm.getTotalNum());
    return supplierOrderToForm;
  }

  public ReturnExchangeVO toVO(
      ReturnExchangeOrder returnExchangeOrder,
      SupplierOrder supplierOrder,
      List<SupplierOrderDetail> supplierOrderDetails,
      List<FileDTOExt> fileDTOExts,
      List<PurchaseOrderInvoiceRelation> invoiceRelations,
      BigDecimal invoiceTotalNum
  ) {
    ReturnExchangeVO vo = new ReturnExchangeVO();
    vo.setId(returnExchangeOrder.getId());
    vo.setSupplierOrderId(returnExchangeOrder.getSupplierOrderId());
    vo.setCode(supplierOrder.getCode());
    vo.setOrderStateKey(supplierOrder.getOrderState());
    vo.setCreateTime(returnExchangeOrder.getCreateTime());
    vo.setGroupCode(supplierOrder.getGroupCode());
    vo.setGroupName(supplierOrder.getGroupName());
    vo.setProjectType(returnExchangeOrder.getProjectType());
    vo.setPurchaseDept(supplierOrder.getPurchaseDept());
    vo.setPurchaseDeptCode(supplierOrder.getPurchaseDeptCode());
    vo.setPurchaseMan(supplierOrder.getPurchaseMan());
    vo.setPurchaseCode(supplierOrder.getPurchaseCode());
    vo.setSupplierId(supplierOrder.getSupplierId());
    vo.setSupplierName(supplierOrder.getSupplierName());
    vo.setInvoicingParty(supplierOrder.getInvoicingParty());
    vo.setNeedRedInvoice(returnExchangeOrder.getNeedRedInvoice());
    vo.setMoneyCode(supplierOrder.getMoneyCode());
    vo.setLoss(supplierOrder.getLoss());
    vo.setDirectShipment(supplierOrder.getDirectShipment());
    vo.setInvoiceType(supplierOrder.getInvoiceType());
    vo.setReturnReason(returnExchangeOrder.getReturnReason());
    vo.setSupContacts(supplierOrder.getSupContacts());
    vo.setSupMobile(supplierOrder.getSupMobile());
    vo.setSupEmail(supplierOrder.getSupEmail());
    vo.setSupFax(supplierOrder.getSupFax());
    vo.setReceiveMan(supplierOrder.getReceiveMan());
    vo.setReceiveMobile(supplierOrder.getReceiveMobile());
    vo.setReceiveAddress(supplierOrder.getReceiveAddress());
    vo.setPrice(supplierOrder.getPrice());
    vo.setOriginalOrderNo(returnExchangeOrder.getOriginalOrderNo());
    vo.setSupplierOpenInvoiceState(supplierOrder.getSupplierOpenInvoiceState());
    vo.setAnnexDTOs(fileDTOExts);
    vo.setInvoiceTotalNum(invoiceTotalNum);
    List<ReturnExchangeProductDetailVO> detailVOS = supplierOrderDetails.parallelStream().map(supplierOrderDetail -> {
      // todo SupplierOrderProduct supplierOrderProduct优化加载
      SupplierOrderProduct supplierOrderProduct = supplierOrderDetail.getSupplierOrderProduct();
      ReturnExchangeProductDetailVO detailVO = new ReturnExchangeProductDetailVO();
      detailVO.setId(supplierOrderDetail.getId());
      detailVO.setIndexNum(supplierOrderDetail.getSortNum());
      detailVO.setProductCode(supplierOrderProduct.getCode());
      detailVO.setBrand(supplierOrderProduct.getBrand());
      detailVO.setProductName(supplierOrderProduct.getName());
      detailVO.setDescription(supplierOrderDetail.getDescription());
      detailVO.setManuCode(supplierOrderProduct.getManuCode());
      detailVO.setUnit(supplierOrderProduct.getUnit());
      detailVO.setSpecification(supplierOrderProduct.getSpecification());
      detailVO.setModel(supplierOrderProduct.getModel());
      detailVO.setNum(supplierOrderDetail.getNum());
      detailVO.setWarehouse(supplierOrderDetail.getWarehouse());
      detailVO.setWarehouseName(supplierOrderDetail.getWarehouseName());
      detailVO.setBatchNo(supplierOrderDetail.getBatchNo());
      detailVO.setOriginalPrice(supplierOrderDetail.getOriginalPrice());
      detailVO.setOriginalTotalPrice(supplierOrderDetail.getOriginalTotalPrice());
      detailVO.setPrice(supplierOrderDetail.getPrice());
      detailVO.setTaxRate(supplierOrderDetail.getTaxRate());
      detailVO.setReturnedNum(supplierOrderDetail.getStockOutputQty());
      detailVO.setCancelReturnNum(supplierOrderDetail.getCancelQty());
      detailVO.setMark(supplierOrderDetail.getMark());
      detailVO.setFreeState(supplierOrderDetail.getFreeState());
      detailVO.setReturnFlag(supplierOrderDetail.getReturnFlag());
      detailVO.setInvoicedNum(supplierOrderDetail.getInvoicedNum());
      return detailVO;
    }).collect(Collectors.toList());
    vo.setReturnProductList(detailVOS);
    vo.setInvoiceRelationList(invoiceRelations);
    BigDecimal canInvoicedNum = detailVOS.stream()
        .map(item -> item.getNum().subtract(item.getCancelReturnNum()))
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    vo.setCanInvoicedNum(canInvoicedNum);
    return vo;
  }

  public SupplierOrderToForm createReturnForm( ReturnExchangeOrder returnExchangeOrder,
      SupplierOrder supplierOrder, AddNewReturnForm addNewReturnForm, String warehouse) {
    SupplierOrderToForm supplierOrderToForm = new SupplierOrderToForm();
    supplierOrderToForm.setSupplierOrderId(supplierOrder.getId());
    supplierOrderToForm.setType(SupplierOrderFormType.RETURN.getKey());
    supplierOrderToForm.setTime(System.currentTimeMillis());
    supplierOrderToForm.setCreateTime(System.currentTimeMillis());
    supplierOrderToForm.setState(Constants.STATE_OK);
    supplierOrderToForm.setLogisticsCompany(addNewReturnForm.getLogisticsCompany());
    supplierOrderToForm.setLogisticsCode(addNewReturnForm.getLogisticsCode());
    supplierOrderToForm.setTrackNum(addNewReturnForm.getTrackNum());
    supplierOrderToForm.setCode(addNewReturnForm.getCode());
    supplierOrderToForm.setStatus(SupplierOrderFormStatus.RETURN.getStatus());
    supplierOrderToForm.setReturnReason(returnExchangeOrder.getReturnReason());
    supplierOrderToForm.setReturnWarehouse(warehouse);
    supplierOrderToForm.setConsignee(supplierOrder.getReceiveMan());
    supplierOrderToForm.setReceiveAddress(supplierOrder.getReceiveAddress());
    supplierOrderToForm.setRemark(addNewReturnForm.getRemark());
    //是否需要开红票
    supplierOrderToForm.setNeedRedTicket(Convert.toStr(BooleanUtil.toInt(returnExchangeOrder.getNeedRedInvoice())));
    supplierOrderToForm.setSapReturnNumber(supplierOrder.getCode());
    Boolean directShipment = supplierOrder.getDirectShipment();
    if (Boolean.TRUE.equals(directShipment)) {
      supplierOrderToForm.setExecutionStatus(SupplierOrderFormExecutionStatusEnum.NO_NEED_EXECUTION.getKey());
    } else {
      supplierOrderToForm.setExecutionStatus(SupplierOrderFormExecutionStatusEnum.PENDING_EXECUTION.getKey());
    }
    return supplierOrderToForm;
  }

  public List<SupplierOrderDetail> createReturnDetails(ReturnExchangeOrder returnExchangeOrder,
      SupplierOrder supplierOrder,
      List<SupplierOrderDetail> supplierOrderDetails, SupplierOrderToForm returnForm, Map<String, BigDecimal> id2ReturnNum) {
    BigDecimal totalReturnNum = BigDecimal.ZERO;
    BigDecimal totalReturnAmount = BigDecimal.ZERO;
    List<SupplierOrderDetail> returnDetailList = new ArrayList<>();
    for (SupplierOrderDetail supplierOrderDetail : supplierOrderDetails) {
      // 获取本次退货数量
      BigDecimal returnNum = id2ReturnNum.get(supplierOrderDetail.getId());
      SupplierOrderDetail returnDetail = new SupplierOrderDetail();
      returnDetail.setOrderToFormId(returnForm.getId());
      returnDetail.setState(Constants.STATE_OK);
      returnDetail.setCreateTime(System.currentTimeMillis());
      returnDetail.setOrderProductId(supplierOrderDetail.getOrderProductId());
      returnDetail.setSortNum(supplierOrderDetail.getSortNum());
      returnDetail.setDetailedId(supplierOrderDetail.getId());
      // 保存InWareHouseId无意义
      returnDetail.setInWareHouseId(supplierOrderDetail.getInWareHouseId());
      // 保存InWareHouseName无意义
      returnDetail.setInWareHouseName(supplierOrderDetail.getInWareHouseName());
      returnDetail.setWarehouse(supplierOrderDetail.getWarehouse());
      returnDetail.setWarehouseName(supplierOrderDetail.getWarehouseName());
      returnDetail.setReturnQty(returnNum);
      returnDetail.setBatchNo(supplierOrderDetail.getBatchNo());
      returnDetail.setStockOutputQty(returnNum);
      returnDetail.setPrice(supplierOrderDetail.getPrice());
      returnDetail.setReturnPrice(supplierOrderDetail.getPrice());
      returnDetail.setTotalPrice(supplierOrderDetail.getPrice().multiply(returnNum));
      returnDetail.setReturnAmount(returnDetail.getTotalPrice().setScale(2, RoundingMode.HALF_UP));
      returnDetail.setPurchaseOrderId(supplierOrder.getId());
      returnDetail.setOpenRedInvoice(returnExchangeOrder.getNeedRedInvoice());
      returnDetail.setDescription(supplierOrderDetail.getDescription());
      returnDetail.setTaxRate(supplierOrderDetail.getTaxRate());
      returnDetail.setReturnFlag(supplierOrderDetail.getReturnFlag());
      // 计算退货数量是否合规 -- 本次退货数量 = 需要退货数量 - 取消数量 - 已退数量
      BigDecimal maxReturnNum = supplierOrderDetail.getNum().subtract(supplierOrderDetail.getCancelQty()).subtract(supplierOrderDetail.getStockOutputQty());
      if (NumberUtil.isGreater(returnNum, maxReturnNum)) {
        throw new CheckException("本次退货数量不能大于最大可退货数量");
      }
      // 对于原详情的退货数量进行累加
      supplierOrderDetail.setStockOutputQty(supplierOrderDetail.getStockOutputQty().add(returnNum));
      supplierOrderDetail.setReturnQty(supplierOrderDetail.getReturnQty().add(returnNum));
      // 不变化
//      supplierOrderDetail.setInvoicableNum(supplierOrderDetail.getInvoicableNum().subtract(returnNum));
      totalReturnNum = totalReturnNum.add(returnNum);
      totalReturnAmount = totalReturnAmount.add(returnDetail.getReturnAmount());
      returnDetailList.add(returnDetail);
    }
    return returnDetailList;
  }


  public List<NewReturnVO> toNewReturnVOList(
      List<SupplierOrderToForm> returnForms,
      List<SupplierOrderDetail> returnDetails) {
    List<String> detailIds =
        returnDetails.stream().map(SupplierOrderDetail::getId).collect(Collectors.toList());
    detailIds.add("-1");
    // 1.查询退货单关联的发票信息
    List<InputInvoiceOrderWithDetail> invoiceOrderWithDetails =
        shareInputInvoiceService.getOrderInvoiceRelationListByDetailIdsRef(detailIds);
    // 2.returnDetails根据orderToFormId分组
    Map<String, List<SupplierOrderDetail>> orderToFormId2Details = returnDetails.stream()
        .collect(Collectors.groupingBy(SupplierOrderDetail::getOrderToFormId));
    List<NewReturnVO> newReturnVOS = new ArrayList<>();
    for (SupplierOrderToForm returnForm : returnForms) {
      List<SupplierOrderDetail> returnDetailsOne =
          orderToFormId2Details.getOrDefault(returnForm.getId(), new ArrayList<>());
      List<NewReturnProductDetailVO> newReturnProductDetailVOS =
          returnDetailsOne.stream().map(item -> {
            List<PurchaseOrderInvoiceRelation> invoiceRelations = invoiceOrderWithDetails.stream()
                .map(inputInvoice -> new PurchaseOrderInvoiceRelation(
                    inputInvoice.getInputInvoiceOrder())).collect(Collectors.toList());
            // todo SupplierOrderProduct supplierOrderProduct优化加载
            SupplierOrderProduct supplierOrderProduct = item.getSupplierOrderProduct();
            return toNewReturnProductDetailVO(item, supplierOrderProduct, invoiceRelations);
          }).collect(Collectors.toList());
      NewReturnVO newReturnVO = toNewReturnVO(returnForm, newReturnProductDetailVOS);
      newReturnVOS.add(newReturnVO);
    }
    return newReturnVOS;
  }


  public NewReturnVO toNewReturnVO(
      SupplierOrderToForm returnForm,
      List<NewReturnProductDetailVO> returnDetails
  ) {
    NewReturnVO vo = new NewReturnVO();
    vo.setId(returnForm.getId());
    vo.setTime(returnForm.getTime());
    vo.setLogisticsCompany(returnForm.getLogisticsCompany());
    vo.setLogisticsCode(returnForm.getLogisticsCode());
    vo.setTrackNum(returnForm.getTrackNum());
    vo.setProductVoucher(returnForm.getProductVoucher());
    vo.setProductVoucherYear(returnForm.getProductVoucherYear());
    vo.setRemark(returnForm.getRemark());
    vo.setExecutionStatus(returnForm.getExecutionStatus());
    vo.setReturnWarehouse(returnForm.getReturnWarehouse());
    vo.setConsignee(returnForm.getConsignee());
    vo.setReceiveAddress(returnForm.getReceiveAddress());
    vo.setReturnProductDetails(returnDetails);
    if (StrUtil.isNotBlank(returnForm.getSource())) {
      vo.setSource(returnForm.getSource());
    } else {
      if (StrUtil.equals(WarehouseEnum.HAI_NING_DIRECT_SALES.getCode(), vo.getReturnWarehouse())) {
        vo.setSource("SRM");
      } else {
        vo.setSource("WMS");
      }
    }
    return vo;
  }


  public NewReturnProductDetailVO toNewReturnProductDetailVO(
      SupplierOrderDetail supplierOrderDetail,
      SupplierOrderProduct supplierOrderProduct,
      List<PurchaseOrderInvoiceRelation> invoiceRelations) {
    NewReturnProductDetailVO vo = new NewReturnProductDetailVO();
    vo.setId(supplierOrderDetail.getId());
    vo.setProductCode(supplierOrderProduct.getCode());
    vo.setBrand(supplierOrderProduct.getBrand());
    vo.setProductName(supplierOrderProduct.getName());
    vo.setDescription(supplierOrderDetail.getDescription());
    vo.setManuCode(supplierOrderProduct.getManuCode());
    vo.setSpecification(supplierOrderProduct.getSpecification());
    vo.setModel(supplierOrderProduct.getModel());
    vo.setUnit(supplierOrderProduct.getUnit());
    vo.setNum(supplierOrderDetail.getStockOutputQty());
    vo.setWarehouse(supplierOrderDetail.getWarehouse());
    vo.setWarehouseName(supplierOrderDetail.getWarehouseName());
    vo.setBatchNo(supplierOrderDetail.getBatchNo());
    vo.setPrice(supplierOrderDetail.getPrice());
    vo.setTaxRate(supplierOrderDetail.getTaxRate());
    vo.setSapRowId(supplierOrderDetail.getSortNum().toString());
    vo.setReturnProductVoucherRowId(supplierOrderDetail.getSapRowId());
    vo.setInvoiceRelationList(invoiceRelations);
    vo.setInvoicedNum(supplierOrderDetail.getInvoicedNum() == null ? BigDecimal.ZERO : supplierOrderDetail.getInvoicedNum());
    return vo;
  }

  /**
   * 新增单个取消明细
   * @param supplierOrderDetail 原单
   * @param supplierOrderToForm 取消表单
   * @param cancelNum 取消数
   * @return
   */
  public SupplierOrderDetail createSingleCancelOrderDetail(SupplierOrderDetail supplierOrderDetail,
      SupplierOrderToForm supplierOrderToForm, BigDecimal cancelNum) {
    SupplierOrderDetail cancelDetail = new SupplierOrderDetail();
    // 关联原单信息
    cancelDetail.setDetailedId(supplierOrderDetail.getId());
    cancelDetail.setOrderProductId(supplierOrderDetail.getOrderProductId());
    cancelDetail.setBatchNo(supplierOrderDetail.getBatchNo());
    cancelDetail.setSapRowId(supplierOrderDetail.getSapRowId());
    cancelDetail.setDeliverTime(supplierOrderDetail.getDeliverTime());
    cancelDetail.setErpRowNum(supplierOrderDetail.getErpRowNum());
    cancelDetail.setNum(supplierOrderDetail.getNum());
    cancelDetail.setSortNum(supplierOrderDetail.getSortNum());
    cancelDetail.setWaitQty(supplierOrderDetail.getWaitQty());
    cancelDetail.setShipQty(supplierOrderDetail.getShipQty());
    cancelDetail.setReturnQty(supplierOrderDetail.getReturnQty());
    cancelDetail.setStockInputQty(supplierOrderDetail.getStockInputQty());
    cancelDetail.setWaitStockInputQty(supplierOrderDetail.getWaitStockInputQty());
    cancelDetail.setRemainQty(supplierOrderDetail.getRemainQty());
    cancelDetail.setStockOutputQty(supplierOrderDetail.getStockOutputQty());
    cancelDetail.setSettleQty(supplierOrderDetail.getSettleQty());
    cancelDetail.setTotalPrice(supplierOrderDetail.getTotalPrice());
    cancelDetail.setMark(supplierOrderDetail.getMark());
    cancelDetail.setTaxRate(supplierOrderDetail.getTaxRate());
    cancelDetail.setPrice(supplierOrderDetail.getPrice());
    cancelDetail.setDescription(supplierOrderDetail.getDescription());
    cancelDetail.setWarehouse(supplierOrderDetail.getWarehouse());
    cancelDetail.setWarehouseName(supplierOrderDetail.getWarehouseName());
    cancelDetail.setTotalAmountIncludingTax(supplierOrderDetail.getTotalAmountIncludingTax());
    cancelDetail.setFreeState(supplierOrderDetail.getFreeState());
    cancelDetail.setProjectType(supplierOrderDetail.getProjectType());
    cancelDetail.setPurchaseOrderId(supplierOrderDetail.getPurchaseOrderId());
    cancelDetail.setOpenRedInvoice(supplierOrderDetail.getOpenRedInvoice());
    cancelDetail.setOriginalTotalPrice(supplierOrderDetail.getOriginalTotalPrice());
    cancelDetail.setOriginalPrice(supplierOrderDetail.getOriginalPrice());
    cancelDetail.setReturnFlag(supplierOrderDetail.getReturnFlag());
    // 关联表单信息
    cancelDetail.setOrderToFormId(supplierOrderToForm.getId());
    // 取消单明细信息
    cancelDetail.setCreateTime(System.currentTimeMillis());
    cancelDetail.setState(Constants.STATE_OK);
    cancelDetail.setCancelQty(cancelNum);
    return cancelDetail;
  }
}
