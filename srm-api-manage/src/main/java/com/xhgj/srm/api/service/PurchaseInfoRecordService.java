package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.HasRecordBySupplierAndOrgParam;
import com.xhgj.srm.api.dto.PurchaseInfoRecordBatchSaveDTO;
import com.xhgj.srm.api.dto.PurchaseInfoRecordImportProgressVo;
import com.xhgj.srm.api.dto.PurchaseInfoRecordListDTO;
import com.xhgj.srm.api.dto.PurchaseInfoRecordListParam;
import com.xhgj.srm.api.dto.PurchaseInfoRecordPageQuery;
import com.xhgj.srm.api.dto.PurchaseInfoRecordTableDTO;
import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.jpa.dto.purchaseInfoRecord.PurchaseInfoRecordStatistics;
import com.xhgj.srm.jpa.entity.PurchaseInfoRecord;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.io.InputStream;
import java.util.List;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @since 2023/12/29 9:53
 */
public interface PurchaseInfoRecordService extends BootBaseService<PurchaseInfoRecord, String> {
  /** 分页查询表格数据 */
  PageResult<PurchaseInfoRecordTableDTO> getTablePage(
      PurchaseInfoRecordPageQuery query, Pageable pageable);

  PageResult<PurchaseInfoRecordTableDTO> getTablePageRef(PurchaseInfoRecordPageQuery query);

  /**
   * 获取统计信息
   * @param query
   * @return
   */
  PurchaseInfoRecordStatistics getStatistics(PurchaseInfoRecordPageQuery query);

  /** 导入保存*/
  PurchaseInfoRecordBatchSaveDTO saveFromExcel(InputStream file, String originalFilename, String userId, String taskId);

  /** 导出，支持勾选、筛选、全部导出 */
  void exportExcel(PurchaseInfoRecordPageQuery query);

  /**
   * 根据采购组织、供应商、物料编码、货币码、匹配最新一条价格库信息（即生效的）采购记录信息（用于采购订单选择物料匹配）
   */
  List<PurchaseInfoRecordListDTO> getNewestRecordByProductCode(PurchaseInfoRecordListParam param);

  /**
   * 根据供应商、采购组织搜索是否存在价格库信息
   */
  Boolean hasRecordBySupplierAndUserGroup(HasRecordBySupplierAndOrgParam param);

  /**
   * 提交导入
   * @param importBatchNumber 导入批次号
   */
  void submitImport(String importBatchNumber, String fileUrl, User user);

  /**
   * 管理员审核，钉钉审批回调异常时补偿接口
   */
  void approveFromAdmin(ApprovalResult param);

  /**
   * 获取导入进度
   */
  PurchaseInfoRecordImportProgressVo getImportProgress(String taskId);

  /**
   * 初始化商品描述
   * 原则上只执行一次
   */
  void initProductDesc();
}
