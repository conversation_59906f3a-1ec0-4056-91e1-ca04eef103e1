package com.xhgj.srm.api.dto.account;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023-02-23 19:27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AccountQuery extends BaseAccountDTO{

  @ApiModelProperty("提交时间范围 开始")
  private Long startCommitTime;

  @ApiModelProperty("提交时间范围 结束")
  private Long endCommitTime;

  @ApiModelProperty("审核时间范围 开始")
  private Long startAssessTime;

  @ApiModelProperty("审核时间范围 开始")
  private Long endAssessTime;

  @ApiModelProperty("查询方案 id ")
  private String schemeId;

  @ApiModelProperty("关联平台")
  private String platforms;
}
