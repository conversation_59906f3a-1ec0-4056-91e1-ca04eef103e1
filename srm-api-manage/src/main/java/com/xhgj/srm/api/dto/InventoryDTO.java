package com.xhgj.srm.api.dto;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.enums.PurchaseOrderTypeEnum;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.CommonlyUseUtil;
import com.xhgj.srm.jpa.entity.Inventory;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.request.vo.sap.SapInventoryVO;
import com.xhiot.boot.core.common.util.dict.BootDictEnumUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.UUID;

/**
 * InventoryDTO
 */
@Data
@NoArgsConstructor
public class InventoryDTO {

  /**
   * 主键
   */
  @ApiModelProperty("主键")
  private String id;

  /**
   * 物料编码
   */
  @ApiModelProperty("物料编码")
  private String productCode;

  /**
   * 库房名称
   */
  @ApiModelProperty("库房名称")
  private String warehouseName;

  @ApiModelProperty("库房编码")
  private String warehouse;

  @ApiModelProperty("库房id")
  private String inventoryLocationId;

  /**
   * 批号
   */
  @ApiModelProperty("批号")
  private String batchNo;

  /**
   * 品牌
   */
  @ApiModelProperty("品牌")
  private String brand;

  /**
   * 名称
   */
  @ApiModelProperty("名称")
  private String name;

  /**
   * 型号
   */
  @ApiModelProperty("型号")
  private String model;


  /**
   * 描述（物料备注）
   */
  @ApiModelProperty("描述（物料备注）")
  private String description;

  /**
   * 单位
   */
  @ApiModelProperty("单位")
  private String unit;

  /**
   * 单位编码
   */
  @ApiModelProperty("单位编码")
  private String unitCode;

  /**
   * 库存总数（不含寄售数量）
   */
  @ApiModelProperty("库存总数（不含寄售数量）")
  private BigDecimal inventoryTotalNumber;

  /**
   * 寄售库存数量
   */
  @ApiModelProperty("寄售库存数量")
  private BigDecimal consignmentInventoryNumber;


  /**
   * 批次锁库数量
   */
  @ApiModelProperty("批次锁库数量")
  private BigDecimal batchLockNumber;

  /**
   * 可用数量
   */
  @ApiModelProperty("可用数量")
  private BigDecimal availableNumber;

  /**
   * 产品经理
   */
  @ApiModelProperty("产品经理")
  private String productManager;


  /**
   * 项目报价员
   */
  @ApiModelProperty("项目报价员")
  private String projectQuote;

  /**
   * 采购订单号
   */
  @ApiModelProperty("采购订单号")
  private String orderCode;

  /**
   * 采购员
   */
  @ApiModelProperty("采购员")
  private String purchaseMan;

  /**
   * 供应商名称
   */
  @ApiModelProperty("供应商名称")
  private String supplierName;

  /**
   * 税率
   */
  @ApiModelProperty("税率")
  private BigDecimal taxRate;

  /**
   * 未税单价
   */
  @ApiModelProperty("未税单价")
  private BigDecimal unTaxPrice;


  /**
   * 金蝶批次
   */
  @ApiModelProperty("金蝶批次")
  private String kingDeeBatch;

  /**
   * 期初备注
   */
  @ApiModelProperty("期初备注")
  private String openingNotes;

  /**
   * 四级类目
   */
  @ApiModelProperty("四级类目")
  private String fourCategory;

  /**
   * 三级类目
   */
  @ApiModelProperty("三级类目")
  private String threeCategory;

  /**
   * 二级类目
   */
  @ApiModelProperty("二级类目")
  private String twoCategory;

  /**
   * 一级类目
   */
  @ApiModelProperty("一级类目")
  private String oneCategory;


  /**
   * 采购部门
   */
  @ApiModelProperty("采购部门")
  private String purchaseDepartment;


  /**
   * 在库时间(天)
   */
  @ApiModelProperty("在库时间(天)")
  private String inStockTime;

  @ApiModelProperty("采购订单id")
  private String supplierOrderId;

  @ApiModelProperty("采购订单状态")
  private SupplierOrderState orderState;

  @ApiModelProperty("是否有待确认")
  private Boolean confirmState;

  @ApiModelProperty("单子是否赠品订单")
  private Boolean freeState;

  @ApiModelProperty("单子是否自采")
  private Boolean selfState;

  @ApiModelProperty("审核时间")
  private Long auditTime;

  @ApiModelProperty("订单类型 1 金蝶采购订单 NB 标准采购（SAP） Z040 寄售 Z010 委外加工")
  private String orderType;

  /**
   * version
   * @see com.xhgj.srm.jpa.sharding.enums.VersionEnum
   */
  @ApiModelProperty("版本")
  private String version;

  public InventoryDTO(Inventory entity, SupplierOrder supplierOrder) {
    this.id = entity.getId();
    this.productCode = entity.getProductCode();
    this.warehouseName = entity.getWarehouseName();
    this.warehouse = entity.getWarehouse();
    this.batchNo = entity.getBatchNo();
    this.brand = CommonlyUseUtil.buildBrandName(entity.getBrandNameCn(), entity.getBrandNameEn());
    this.name = entity.getName();
    this.model = entity.getModel();
    this.description = entity.getDescription();
    this.unit = entity.getUnit();
    this.unitCode = entity.getUnitCode();
    this.inventoryTotalNumber = entity.getInventoryTotalNumber();
    this.consignmentInventoryNumber = entity.getConsignmentInventoryNumber();
    this.batchLockNumber = entity.getBatchLockNumber();
    this.availableNumber = entity.getAvailableNumber();
    this.productManager = entity.getProductManager();
    this.projectQuote = entity.getProjectQuote();
    this.orderCode = entity.getOrderCode();
    this.purchaseMan = entity.getPurchaseMan();
    this.supplierName = entity.getSupplierName();
    this.taxRate = entity.getTaxRate();
    this.unTaxPrice = entity.getUnTaxPrice();
    this.kingDeeBatch = entity.getKingDeeBatch();
    this.openingNotes = entity.getOpeningNotes();
    this.fourCategory = entity.getFourCategory();
    this.threeCategory = entity.getThreeCategory();
    this.twoCategory = entity.getTwoCategory();
    this.oneCategory = entity.getOneCategory();
    this.purchaseDepartment = entity.getPurchaseDepartment();
    this.inStockTime = entity.getInStockTime();
    if (supplierOrder != null) {
      this.supplierOrderId = supplierOrder.getId();
      this.version = supplierOrder.getVersion();
      this.orderState =
          BootDictEnumUtil.getEnumByKey(SupplierOrderState.class, supplierOrder.getOrderState()).orElse(null);
      this.confirmState =
          SupplierOrderState.WAIT.getOrderState().equals(supplierOrder.getOrderState())&&supplierOrder.getOrderConfirmState();
      this.freeState = supplierOrder.getFreeState();
      this.selfState = supplierOrder.getSelfState();
      this.auditTime = supplierOrder.getAuditTime();
      this.orderType = StrUtil.emptyToDefault(supplierOrder.getOrderType(), PurchaseOrderTypeEnum.JIN_DIE.getKey());
    }

  }


  public InventoryDTO(SapInventoryVO sapInventoryVO,SupplierOrder supplierOrder) {
    this.id = UUID.randomUUID().toString().replaceAll("-", "");
    this.productCode = sapInventoryVO.getProductCode();
    this.warehouseName = sapInventoryVO.getWarehouseName();
    this.batchNo = sapInventoryVO.getBatchNo();
    this.name = sapInventoryVO.getProductName();
    this.inventoryTotalNumber = sapInventoryVO.getTotalStock();
    this.consignmentInventoryNumber = sapInventoryVO.getConsignmentStock();
    this.batchLockNumber = sapInventoryVO.getLockStock();
    this.availableNumber = sapInventoryVO.getAvailableStock();
    this.orderCode = sapInventoryVO.getOrderNo();
    this.purchaseMan = sapInventoryVO.getPurchaseMan();
    this.supplierName = sapInventoryVO.getSupplierName();
    this.taxRate = sapInventoryVO.getTaxRate();
    this.unTaxPrice = sapInventoryVO.getUnitPrice();
    this.kingDeeBatch = sapInventoryVO.getJinDieBatch();
    this.openingNotes = sapInventoryVO.getStockRemark();
    this.purchaseDepartment = sapInventoryVO.getPurchaseDept();
    this.inStockTime = sapInventoryVO.getInStockTime();
    if (supplierOrder != null) {
      this.supplierOrderId = supplierOrder.getId();
      this.version = supplierOrder.getVersion();
      this.orderState =
          BootDictEnumUtil.getEnumByKey(SupplierOrderState.class, supplierOrder.getOrderState()).orElse(null);
      this.confirmState =
          SupplierOrderState.WAIT.getOrderState().equals(supplierOrder.getOrderState())&&supplierOrder.getOrderConfirmState();
      this.freeState = supplierOrder.getFreeState();
      this.selfState = supplierOrder.getSelfState();
      this.auditTime = supplierOrder.getAuditTime();
      this.orderType = StrUtil.emptyToDefault(supplierOrder.getOrderType(), PurchaseOrderTypeEnum.JIN_DIE.getKey());
    }

  }




}
