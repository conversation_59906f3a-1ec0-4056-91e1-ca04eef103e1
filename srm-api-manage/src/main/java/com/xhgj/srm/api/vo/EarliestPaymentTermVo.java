package com.xhgj.srm.api.vo;

import cn.hutool.core.collection.CollUtil;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPaymentTermsVO;
import com.xhgj.srm.jpa.entity.PurchaseOrderPaymentTerms;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Collections;
import java.util.List;

/**
 * @Author: fanghuanxu
 * @Date: 2025/3/21 10:35
 * @Description: 最早付款条件Vo
 */
@Data
@NoArgsConstructor
public class EarliestPaymentTermVo {

  @ApiModelProperty("采购订单号")
  private String purchaseOrderNo;

  @ApiModelProperty("付款条件")
  private String paymentTermsStr;

  @ApiModelProperty("付款条件")
  private List<PurchaseOrderPaymentTermsVO> paymentTermsAddParamList;

  public EarliestPaymentTermVo(SupplierOrder order,
      List<PurchaseOrderPaymentTerms> purchaseOrderPaymentTerms) {
    this.purchaseOrderNo = order.getCode();
    this.paymentTermsStr = order.getPaymentTermsStr();
    if (CollUtil.isNotEmpty(purchaseOrderPaymentTerms)) {
      this.paymentTermsAddParamList = PurchaseOrderPaymentTermsVO.of(purchaseOrderPaymentTerms);
    }else {
      this.paymentTermsAddParamList = Collections.emptyList();
    }  }

}
