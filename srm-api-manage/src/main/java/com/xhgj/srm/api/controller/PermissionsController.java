package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.dto.permissions.FindPermissionsDTO;
import com.xhgj.srm.api.service.PermissionsService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Geng Shy on 2023/10/23
 * 权限相关接口
 */
@RestController
@Validated
@RequestMapping("/permissions")
public class PermissionsController {

  @Resource
  private PermissionsService service;

  @ApiOperation(value = "查询全部菜单")
  @GetMapping(value = "/getMenus")
  public ResultBean<List<FindPermissionsDTO>> getMenus() {
    return new ResultBean<>(service.findAll());
  }
}
