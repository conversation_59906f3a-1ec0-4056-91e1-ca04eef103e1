package com.xhgj.srm.api.dto.RejectReason;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.RejectReason;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class RejectReasonAddParamDTO {

    @ApiModelProperty(value = "驳回原因",required = true)
    @NotEmpty(message = "驳回原因不能为空")
    private String reason;
    @ApiModelProperty(value = "用户id",required = true)
    @NotEmpty(message = "userId不能为空")
    private String userId;

    public RejectReason bulidRejectReason(RejectReasonAddParamDTO rejectReasonAddParamDTO) {
        RejectReason rejectReason = new RejectReason();
        rejectReason.setReason(rejectReasonAddParamDTO.getReason());
        rejectReason.setUserId(rejectReasonAddParamDTO.getUserId());
        rejectReason.setCreateTime(System.currentTimeMillis());
        rejectReason.setState(Constants.STATE_OK);
        return rejectReason;
    }
}
