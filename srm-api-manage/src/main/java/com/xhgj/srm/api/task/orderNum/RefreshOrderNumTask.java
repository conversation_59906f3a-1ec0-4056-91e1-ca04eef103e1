package com.xhgj.srm.api.task.orderNum;/**
 * @since 2025/2/18 10:33
 */

/**
 *<AUTHOR>
 *@date 2025/2/18 10:33:37
 *@description
 */

import com.xhgj.srm.common.utils.asmDisOrder.AsmDisOrderBatchNoCleanerAndGenerator;
import com.xhgj.srm.common.utils.asmDisOrder.AsmDisOrderCleanerAndGenerator;
import com.xhgj.srm.common.utils.returnExchangeOrder.OrderNumberCleanerAndGenerator;
import com.xhgj.srm.common.utils.supplierOrderForm.SupplierOrderFormCodeGenerator;
import com.xhgj.srm.common.utils.transferOrder.TransferOrderCleanerAndGenerator;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import java.time.LocalDate;

/**
 * 清空昨日订单号
 */
@Component
public class RefreshOrderNumTask {

  @Scheduled(cron = "0 1 0 * * ?")
  public void refreshOrderNum() {
    // 清空昨日订单号
    OrderNumberCleanerAndGenerator.INSTANCE.clearYesterdayOrderNumbers(LocalDate.now());
    // 清空上个月订单号
    TransferOrderCleanerAndGenerator.INSTANCE.clearLastMonthOrderNumbers(LocalDate.now());
    // 清空上个月订单号
    AsmDisOrderCleanerAndGenerator.INSTANCE.clearLastMonthOrderNumbers(LocalDate.now());
    // 清空昨日批次号
    AsmDisOrderBatchNoCleanerAndGenerator.INSTANCE.clearYesterdayOrderNumbers(LocalDate.now());
    // 清空昨日采购订单form单号
    SupplierOrderFormCodeGenerator.INSTANCE.clearYesterdayOrderNumbers(LocalDate.now());
  }
}
