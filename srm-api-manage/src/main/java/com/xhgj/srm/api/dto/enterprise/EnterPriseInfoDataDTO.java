package com.xhgj.srm.api.dto.enterprise;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class EnterPriseInfoDataDTO {


    @ApiModelProperty(value = "公司名")
    private String name;

    @ApiModelProperty(value = "经营状态")
    private String regStatus;

    @ApiModelProperty(value = "法人")
    private String legalPersonName;

    @ApiModelProperty(value = "统一社会信用代码")
    private String creditCode;

    @ApiModelProperty(value = "注册资本")
    private String regCapital;

    @ApiModelProperty(value = "成立日期")
    private String estiblishTime;

    @ApiModelProperty(value = "省份")
    private String base;



}
