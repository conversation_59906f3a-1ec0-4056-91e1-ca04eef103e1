package com.xhgj.srm.api.dto.supplier;

import com.xhgj.srm.jpa.entity.Supplier;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class InteriorSupplierPageDTO {
  @ApiModelProperty("供应商id")
  private String supplierId;

  @ApiModelProperty("mdm编码")
  private String mdmCode;

  @ApiModelProperty("企业名称")
  private String supplierName;

  @ApiModelProperty(value = "社会统一信用代码")
  private String uScc;

  @ApiModelProperty("省")
  private String province;

  @ApiModelProperty("市")
  private String city;

  @ApiModelProperty("入库时间")
  private Long createTime;

  @ApiModelProperty("同步时间")
  private Long syncTime;

  public InteriorSupplierPageDTO(Supplier supplier, Long syncTime) {
    this.supplierId = supplier.getId();
    this.mdmCode = supplier.getMdmCode();
    this.supplierName = supplier.getEnterpriseName();
    this.uScc = supplier.getUscc();
    this.province = supplier.getProvince();
    this.city = supplier.getCity();
    this.createTime = supplier.getCreateTime();
    this.syncTime = syncTime;
  }
}
