package com.xhgj.srm.api.dto;

import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import lombok.Data;

@Data
public class CheckMyCheckSupplierDataDTO {

    private String id;
    private String userGroup;
    private String enterName;
    private String purchaser;
    private String level;
    private String brand;
    private String contact;
    private String mobile;
    private String enterNature;
    private String industry;
    private String checkMan;
    private String reason;
    private String checkTime;
    private String applyTime;
    private String checkState;
    private String shieldState;
    private String resourceType;

    public CheckMyCheckSupplierDataDTO(Supplier supplier) {
        this.id = supplier.getId();
        this.userGroup = !StringUtils.isNullOrEmpty(supplier.getUseGroup())?supplier.getUseGroup():"";
        this.enterNature =  !StringUtils.isNullOrEmpty(supplier.getEnterpriseNature())?supplier.getEnterpriseNature():"";
        this.enterName = !StringUtils.isNullOrEmpty(supplier.getEnterpriseName())?supplier.getEnterpriseName():"";
        this.industry = !StringUtils.isNullOrEmpty(supplier.getIndustry())?supplier.getIndustry():"";
        this.level = !StringUtils.isNullOrEmpty(supplier.getEnterpriseLevel())?
            SupplierLevelEnum.getAbbrByCode(supplier.getEnterpriseLevel()):"";
        this.purchaser = !StringUtils.isNullOrEmpty(supplier.getPurchaserName())?supplier.getPurchaserName():"";
        this.mobile = !StringUtils.isNullOrEmpty(supplier.getMobile())?supplier.getMobile():"";
        this.applyTime = supplier.getEditTime()>0?DateUtils.formatTimeStampToNormalDateTime(supplier.getEditTime()):DateUtils.formatTimeStampToNormalDateTime(supplier.getCreateTime());
        if(!StringUtils.isNullOrEmpty(supplier.getShieldState())){
            resourceType = Constants.RESOURCE_TYPE_SHIELD;
        } else {
            resourceType = Constants.RESOURCE_TYPE_REGISTER;
        }
        this.shieldState =  !StringUtils.isNullOrEmpty(supplier.getShieldState())?supplier.getShieldState():"";

    }

}
