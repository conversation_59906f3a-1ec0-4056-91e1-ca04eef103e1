package com.xhgj.srm.api.service.impl;/**
 * @since 2025/2/10 17:13
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.srm.api.dto.returnExchangeOrder.AddNewReturnForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.AddNewReturnForm.ReturnProductDetail;
import com.xhgj.srm.api.dto.returnExchangeOrder.AddNewCancelForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeOrderCount;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeOrderFillDTO;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeSaveForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.ReturnExchangeSearchForm;
import com.xhgj.srm.api.dto.returnExchangeOrder.UnCancelForm;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderInvoiceRelation;
import com.xhgj.srm.api.factory.ReturnExchangeOrderFactory;
import com.xhgj.srm.api.factory.SupplierOrderFactory;
import com.xhgj.srm.api.service.MissionService;
import com.xhgj.srm.api.service.ReturnExchangeOrderService;
import com.xhgj.srm.api.service.SupplierOrderDetailService;
import com.xhgj.srm.api.service.SupplierOrderToFormService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.api.utils.ManageSecurityUtil;
import com.xhgj.srm.api.vo.returnExchange.NewReturnVO;
import com.xhgj.srm.api.vo.returnExchange.ReturnExchangeVO;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.FileDTOExt;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.returnExchangeOrder.OrderNumberCleanerAndGenerator;
import com.xhgj.srm.common.vo.returnExchange.ReturnExchangeListVO;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.Constants_FileRelationType;
import com.xhgj.srm.common.Constants_PurchaseInfoRecord;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.dao.ReturnExchangeOrderDao;
import com.xhgj.srm.jpa.dao.SupplierOrderDetailDao;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import com.xhgj.srm.jpa.dto.permission.OperatorPermission;
import com.xhgj.srm.jpa.dto.permission.SearchPermission;
import com.xhgj.srm.jpa.dto.returnExchange.ReturnExchangeStatistics;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.InputInvoiceOrder;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.ReturnExchangeOrder;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderToFormV2;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.repository.FileRepository;
import com.xhgj.srm.jpa.repository.OrderInvoiceRelationRepository;
import com.xhgj.srm.jpa.repository.ReturnExchangeOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderProductRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderToFormRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.sharding.enums.VersionEnum;
import com.xhgj.srm.jpa.sharding.util.ShardingContext;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import com.xhgj.srm.factory.SapFactory;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationParam;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult;
import com.xhgj.srm.request.service.third.erp.sap.dto.ReceiptVoucherSynchronizationResult.ReturnMessage;
import com.xhgj.srm.request.service.third.erp.sap.dto.UpdatePurchaseOrderSapParam;
import com.xhgj.srm.request.service.third.sap.SAPService;
import com.xhgj.srm.service.SharePermissionTypeService;
import com.xhgj.srm.util.ImportExcelUtil;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.config.BootConfig;
import com.xhiot.boot.mvc.base.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ReturnExchangeOrderServiceImpl implements ReturnExchangeOrderService {
  @Resource
  private ReturnExchangeOrderFactory returnExchangeOrderFactory;
  @Resource
  private SupplierOrderFactory supplierOrderFactory;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;
  @Resource
  private ReturnExchangeOrderRepository returnExchangeOrderRepository;
  @Resource
  private FileDao fileDao;
  @Resource
  private FileRepository fileRepository;
  @Resource
  private SupplierOrderToFormRepository supplierOrderToFormRepository;
  @Resource
  private SupplierOrderDetailRepository supplierOrderDetailRepository;
  @Resource
  private SupplierOrderProductRepository supplierOrderProductRepository;
  @Resource
  private SapFactory sapFactory;
  @Resource
  private SAPService sapService;
  @Resource
  private ExportUtil exportUtil;
  private final String baseUrl;
  @Resource
  private ManageSecurityUtil manageSecurityUtil;
  @Resource
  private SharePermissionTypeService sharePermissionTypeService;
  @Resource
  private ReturnExchangeOrderDao returnExchangeOrderDao;
  @Resource
  private OrderInvoiceRelationRepository orderInvoiceRelationRepository;
  @Resource
  private UserService userService;
  @Resource
  private MissionService missionService;
  @Autowired
  private MissionDispatcher missionDispatcher;
  @Resource
  private SupplierRepository supplierRepository;
  @Resource
  private BootConfig bootConfig;
  @Resource
  private SupplierOrderDetailService supplierOrderDetailService;
  @Resource
  private SupplierOrderDetailDao supplierOrderDetailDao;
  @Resource
  private ImportExcelUtil importExcelUtil;
  @Resource
  private SupplierOrderToFormService supplierOrderToFormService;
  @Resource
  private PlatformTransactionManager transactionManager;



  public ReturnExchangeOrderServiceImpl(SrmConfig config) {
    this.baseUrl = config.getUploadUrl();
  }



  @Override
  @Transactional(rollbackFor = Exception.class)
  public String saveRefundExchangeOrder(ReturnExchangeSaveForm saveForm) {
    ReturnExchangeOrder returnExchangeOrder;
    try {
      // -----------------校验-----------------
      // 税率一致性判断
      returnExchangeOrderFactory.checkRateWithoutFreeLine(saveForm);
      // 折价判断
      returnExchangeOrderFactory.checkLoss(saveForm);
      // 寄售判断
      returnExchangeOrderFactory.checkConsign(saveForm);
      // 退货原单号校验
      returnExchangeOrderFactory.checkReturnOrder(saveForm.getOriginalOrderNo());
      // -----------------保存-----------------
      // 退换货订单主表
      returnExchangeOrder = returnExchangeOrderFactory.create(saveForm);
      // 关联supplier_order表id
      SupplierOrder supplierOrder = returnExchangeOrderFactory.createLinkSupplierOrder(returnExchangeOrder, saveForm);
      supplierOrderRepository.saveAndFlush(supplierOrder);
      returnExchangeOrder.setSupplierOrderId(supplierOrder.getId());
      returnExchangeOrderRepository.saveAndFlush(returnExchangeOrder);
      // 删除原附件
      fileDao.deleteByRelationIdAndRelationType(supplierOrder.getId(), Constants_FileRelationType.ORDER_ANNEX);
      // 保存附件
      List<File> files = supplierOrderFactory.generateAnnex(saveForm.getFileList(), supplierOrder.getId());
      fileRepository.saveAll(files);
      fileRepository.flush();
      // 删除原订单详情信息
      supplierOrderFactory.delSupplierOrderInfo(supplierOrder.getId());
      // 保存订单关联表单
      SupplierOrderToForm linkSupplierOrderForm =
          returnExchangeOrderFactory.createLinkSupplierOrderForm(supplierOrder.getId(), saveForm);
      supplierOrderToFormRepository.saveAndFlush(linkSupplierOrderForm);
      // 保存订单物料信息
      List<SupplierOrderProduct> linkSupplierOrderProduct =
          returnExchangeOrderFactory.createLinkSupplierOrderProduct(saveForm.getReturnProductList());
      supplierOrderProductRepository.saveAll(linkSupplierOrderProduct);
      supplierOrderProductRepository.flush();
      // 保存订单明细信息
      List<SupplierOrderDetail> supplierOrderDetails =
          returnExchangeOrderFactory.createLinkSupplierOrderDetail(supplierOrder.getId(),
              linkSupplierOrderForm.getId(), saveForm.getProjectType(), saveForm.getNeedRedInvoice(),
              saveForm.getReturnProductList(), linkSupplierOrderProduct);
      supplierOrderDetailRepository.saveAll(supplierOrderDetails);
      supplierOrderDetailRepository.flush();
      // -----------------sap相关请求-----------------
      if (saveForm.getSaveType() == 2) {
        UpdatePurchaseOrderSapParam mm021ParamForReturnExchangeOrder =
            sapFactory.createMM021ParamForReturnExchangeOrder(returnExchangeOrder, supplierOrder,
                linkSupplierOrderProduct, supplierOrderDetails);
        sapService.sapPurchaseOrderWithAlarm(mm021ParamForReturnExchangeOrder,
            supplierOrder.getCode());
      }
    }  catch (Exception e) {
      log.error("保存退换货订单失败", e);
      OrderNumberCleanerAndGenerator.INSTANCE.rollbackOrderNumber();
      throw e;
    }
    finally {
      OrderNumberCleanerAndGenerator.clear();
    }
    return returnExchangeOrder.getId();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void addRefundOrder(AddNewReturnForm form) {
    // -----------------查询基本数据-----------------
    String supplierOrderId = form.getSupplierOrderId();
    // 退换货订单
    ReturnExchangeOrder returnExchangeOrder =
        returnExchangeOrderRepository.findFirstBySupplierOrderIdAndState(supplierOrderId,
            Constants.STATE_OK);
    if (returnExchangeOrder == null) {
      throw new CheckException("退换货订单不存在");
    }
    // 关联supplierOrder
    SupplierOrder supplierOrder = supplierOrderRepository.findById(supplierOrderId)
        .orElseThrow(() -> new CheckException("退换货订单不存在"));
    // 查询本次退货物料
    List<String> supplierOrderDetailIds =
        form.getProductDetailList().stream().map(ReturnProductDetail::getId)
        .collect(Collectors.toList());
    Map<String, BigDecimal> id2ReturnNum =
        form.getProductDetailList().stream().collect(Collectors.toMap(ReturnProductDetail::getId,
            ReturnProductDetail::getReturnNum));
    List<SupplierOrderDetail> supplierOrderDetails =
        supplierOrderDetailRepository.findAllById(supplierOrderDetailIds);
    if (CollUtil.isEmpty(supplierOrderDetails)) {
      throw new CheckException("本次退货物料不存在");
    }
    // 退货仓库获取
    String warehouse = supplierOrderDetails.get(0).getWarehouse();
    // -----------------校验-----------------
    returnExchangeOrderFactory.checkWms(supplierOrderDetails, supplierOrder.getGroupCode());
    // 校验supplierOrder状态
    if (!StrUtil.equalsAny(supplierOrder.getOrderState(), SupplierOrderState.WAIT.getKey(),
        SupplierOrderState.IN_PROGRESS.getKey())) {
      throw new CheckException("订单状态非待履约、履约中，无法退货");
    }
    // 校验供应商信息是否存在
    Supplier supplier = supplierRepository.findById(supplierOrder.getSupplierId())
        .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    Boolean oneTimeSupplier = supplier.isOneTimeSupplier();
    Supplier supplierInvoice = supplier;
    if (!oneTimeSupplier) {
      String invoicingParty = supplierOrder.getInvoicingParty();
      supplierInvoice =
          supplierRepository.findFirstByEnterpriseNameAndState(invoicingParty, Constants.STATE_OK)
              .orElseThrow(() -> new CheckException("供应商信息不存在！"));
    }
    // -----------------生成数据-----------------
    // 退货单form
    SupplierOrderToForm returnForm =
        returnExchangeOrderFactory.createReturnForm(returnExchangeOrder, supplierOrder, form,
            warehouse);
    supplierOrderToFormRepository.saveAndFlush(returnForm);
    // 退货单明细生成
    List<SupplierOrderDetail> returnDetails =
        returnExchangeOrderFactory.createReturnDetails(returnExchangeOrder,supplierOrder,
            supplierOrderDetails,
            returnForm,
            id2ReturnNum);
    supplierOrderDetailRepository.saveAll(returnDetails);
    supplierOrderDetailRepository.flush();
    supplierOrderDetailRepository.saveAll(supplierOrderDetails);
    supplierOrderDetailRepository.flush();
    BigDecimal remainNum =
        supplierOrderDetailDao.getReturnExchangeRemainNum(supplierOrder.getId());
    if (NumberUtil.equals(remainNum,BigDecimal.ZERO)) {
      supplierOrder.setOrderState(SupplierOrderState.COMPLETE.getKey());
    }else {
      supplierOrder.setOrderState(SupplierOrderState.IN_PROGRESS.getKey());
    }
    supplierOrder.setOrderReturnState(true);
    //
    supplierOrderRepository.saveAndFlush(supplierOrder);
    // -----------------sap相关请求-----------------
    ReceiptVoucherSynchronizationParam mm031ParamForAddNewReturn =
        sapFactory.createMM031ParamForAddNewReturn(returnExchangeOrder, supplierOrder, returnForm,
            returnDetails);
    ReceiptVoucherSynchronizationResult result = sapService.sapMaterialVoucherWithLockGroup(mm031ParamForAddNewReturn);
    List<ReturnMessage> returnMessages = result.getReturnMessages();
    for (ReturnMessage returnMessage : returnMessages) {
      String lineItem = returnMessage.getLineItem();
      String documentNumber = returnMessage.getDocumentNumber();
      String purchaseOrderLineItems = returnMessage.getPurchaseOrderLineItems();
      String year = returnMessage.getYear();
      returnForm.setProductVoucher(documentNumber);
      returnForm.setProductVoucherYear(year);
      if (StrUtil.isNotBlank(purchaseOrderLineItems)) {
        Integer purchaseOrderLineItems1 = Integer.valueOf(purchaseOrderLineItems);
        Optional<SupplierOrderDetail> first =
            returnDetails.stream().filter(supplierOrderDetail -> Objects.equals(supplierOrderDetail.getSortNum(), purchaseOrderLineItems1)).findFirst();
        first.ifPresent(supplierOrderDetail -> {
          supplierOrderDetail.setBatchNo(returnMessage.getCharge());
          supplierOrderDetail.setSapRowId(lineItem);
          supplierOrderDetail.setSortNum(purchaseOrderLineItems1);
          supplierOrderDetailRepository.saveAndFlush(supplierOrderDetail);
        });
      }
      supplierOrderToFormRepository.saveAndFlush(returnForm);
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void addCancelOrder(AddNewCancelForm cancelForm) {
    String supplierOrderId = cancelForm.getSupplierOrderId();
    String returnExchangeOrderId = cancelForm.getReturnExchangeOrderId();
    SupplierOrder supplierOrder = supplierOrderRepository.findById(supplierOrderId)
        .orElseThrow(() -> CheckException.noFindException(SupplierOrder.class, supplierOrderId));
    ReturnExchangeOrder returnExchangeOrder = returnExchangeOrderRepository.findById(returnExchangeOrderId)
        .orElseThrow(() -> CheckException.noFindException(ReturnExchangeOrder.class, returnExchangeOrderId));
    if (!StrUtil.equalsAny(supplierOrder.getOrderState(), SupplierOrderState.WAIT.getKey(),
        SupplierOrderState.IN_PROGRESS.getKey())) {
        throw new CheckException("订单状态非待履约、履约中，无法取消");
    }
    // 生成取消单
    SupplierOrderToForm supplierOrderToForm =
        supplierOrderToFormRepository.getFirstByTypeAndSupplierOrderIdAndState(
            SupplierOrderFormType.CANCEL.getType(), supplierOrderId, Constants.STATE_OK);
    // 如果该订单取消类型表单为空则创建取消类型表单
    if (ObjectUtil.isEmpty(supplierOrderToForm)) {
      supplierOrderToForm = new SupplierOrderToForm();
      supplierOrderToForm.setSupplierOrderId(supplierOrder.getId());
      supplierOrderToForm.setType(SupplierOrderFormType.CANCEL.getType());
      supplierOrderToForm.setCreateTime(System.currentTimeMillis());
      supplierOrderToForm.setTime(System.currentTimeMillis());
      supplierOrderToForm.setUpdateTime(System.currentTimeMillis());
      supplierOrderToForm.setState(Constants.STATE_OK);
      supplierOrderToFormRepository.save(supplierOrderToForm);
    }
    // 取消总数
    BigDecimal cancelAllCount = NumberUtil.null2Zero(supplierOrderToForm.getNum());
    List<SupplierOrderDetail> cancelDetails = new ArrayList<>();
    List<SupplierOrderDetail> supplierOrderDetailList =
        supplierOrderDetailRepository.findAllById(cancelForm.getSupplierOrderDetails());
    for (int i = 0; i < supplierOrderDetailList.size(); i++) {
      SupplierOrderDetail supplierOrderDetail = supplierOrderDetailList.get(i);
      BigDecimal remainReturnNum = NumberUtil.null2Zero(supplierOrderDetail.getNum())
          .subtract(NumberUtil.null2Zero(supplierOrderDetail.getStockOutputQty()))
          .subtract(NumberUtil.null2Zero(supplierOrderDetail.getCancelQty()));
      if (NumberUtil.equals(remainReturnNum, BigDecimal.ZERO)) {
        throw new CheckException(StrUtil.format("第{}行物料的剩余待退数量为0，请检查", i + 1));
      }
      // 整行取消，不考虑累加
      supplierOrderDetail.setCancelQty(remainReturnNum);
      cancelAllCount = NumberUtil.add(cancelAllCount, remainReturnNum);
      supplierOrderDetailRepository.save(supplierOrderDetail);
      // 保存取消订单明细
      SupplierOrderDetail cancelDetail =
          returnExchangeOrderFactory.createSingleCancelOrderDetail(supplierOrderDetail,
              supplierOrderToForm,remainReturnNum);
      cancelDetails.add(supplierOrderDetailRepository.saveAndFlush(cancelDetail));
      BigDecimal curCancelPrice = NumberUtil.mul(remainReturnNum, supplierOrderDetail.getPrice());
      supplierOrder.setCancelReturnPrice(
          NumberUtil.add(supplierOrder.getCancelReturnPrice(), curCancelPrice));
    }
    // 更新取消单总数量
    supplierOrderToForm.setNum(cancelAllCount);
    supplierOrderToFormRepository.save(supplierOrderToForm);
    // 进度变更
    BigDecimal remainNum =
        supplierOrderDetailDao.getReturnExchangeRemainNum(supplierOrder.getId());
    if (NumberUtil.equals(remainNum,BigDecimal.ZERO)) {
      supplierOrder.setOrderState(SupplierOrderState.COMPLETE.getKey());
    }
    supplierOrder.setOrderCancelState(Boolean.TRUE);
    supplierOrderRepository.save(supplierOrder);
    // -----------------sap相关请求-----------------
    UpdatePurchaseOrderSapParam mm021ParamForReturnExchangeOrderCancel =
        sapFactory.createMM021ParamForReturnExchangeOrderCancel(returnExchangeOrder,
            supplierOrder, cancelDetails);
    sapService.sapPurchaseOrderWithAlarm(mm021ParamForReturnExchangeOrderCancel, supplierOrder.getCode());
  }

  @Override
  @Transactional
  public void unCancelOrder(UnCancelForm unCancelForm) {
    String supplierOrderId = unCancelForm.getSupplierOrderId();
    SupplierOrder supplierOrder = supplierOrderRepository.findById(supplierOrderId)
        .orElseThrow(() -> CheckException.noFindException(SupplierOrder.class, supplierOrderId));
    ReturnExchangeOrder returnExchangeOrder =
        returnExchangeOrderRepository.findById(unCancelForm.getReturnExchangeOrderId()).orElseThrow(
            () -> CheckException.noFindException(ReturnExchangeOrder.class, unCancelForm.getReturnExchangeOrderId()));
    SupplierOrderDetail supplierOrderDetail =
        supplierOrderDetailRepository.findById(unCancelForm.getSupplierOrderDetailId()).orElseThrow(
            () -> CheckException.noFindException(SupplierOrderDetail.class,
                unCancelForm.getSupplierOrderDetailId()));

    if (BooleanUtil.isFalse(supplierOrder.getDirectShipment())) {
      throw new CheckException("当前订单非厂家直发订单，暂不支持反取消");
    }

    // 获取订单明细信息
    SupplierOrderDetail detailed = supplierOrderDetail.getDetailed();
    BigDecimal cancelQty = supplierOrderDetail.getCancelQty();
    detailed.setCancelQty(BigDecimal.ZERO);
    // 反取消后 订单取消数量回滚
    supplierOrderDetailRepository.save(detailed);
    // 逻辑删除取消单详情
    supplierOrderDetail.setState(Constants.STATE_DELETE);
    supplierOrderDetailRepository.save(supplierOrderDetail);
    // 取消表单
    SupplierOrderToForm supplierOrderToForm =
        supplierOrderToFormRepository.getFirstByTypeAndSupplierOrderIdAndState(
            SupplierOrderFormType.CANCEL.getType(), supplierOrderId, Constants.STATE_OK);
    // 实时更新订单取消状态
    List<SupplierOrderDetail> supplierCancelOrderDetailList =
        supplierOrderDetailService.getByOrderToFormId(supplierOrderToForm.getId());
    if(CollUtil.isEmpty(supplierCancelOrderDetailList)){
      supplierOrder.setOrderCancelState(Boolean.FALSE);
      supplierOrderToForm.setState(Constants.STATE_DELETE);
      supplierOrderToFormRepository.save(supplierOrderToForm);
    }
    // 获取订单退货数量 如果订单有退货数量，订单状态更新为履约中；如果没有退货数量，订单状态更新为待履约
    // 获取该订单下所以订单详情集合
    BigDecimal returnNumber = supplierOrderDetailService.getSupplierOrderReturnQty(supplierOrderId);
    if (NumberUtil.isGreater(returnNumber, BigDecimal.ZERO)) {
      supplierOrder.setOrderState(SupplierOrderState.IN_PROGRESS.getOrderState());
    } else {
      supplierOrder.setOrderState(SupplierOrderState.WAIT.getOrderState());
    }
    // 该物料的退货/取消金额
    BigDecimal currReturnOrCancelPrice = NumberUtil.mul(cancelQty,
        supplierOrderDetail.getPrice());
    supplierOrder.setCancelReturnPrice(
        NumberUtil.sub(supplierOrder.getCancelReturnPrice(), currReturnOrCancelPrice));
    // 更新取消单总数量
    supplierOrderToForm.setNum(NumberUtil.sub(supplierOrderToForm.getNum(),cancelQty));
    supplierOrderToFormRepository.save(supplierOrderToForm);
    // 调用sap
    UpdatePurchaseOrderSapParam mm021ParamForReturnExchangeOrderSingleUnCancel =
        sapFactory.createMM021ParamForReturnExchangeOrderSingleUnCancel(returnExchangeOrder,
            supplierOrder, supplierOrderDetail);

    sapService.sapPurchaseOrderWithAlarm(mm021ParamForReturnExchangeOrderSingleUnCancel, supplierOrder.getCode());
  }


  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_RETURN_EXCHANGE_ORDER)
  public PageResult<ReturnExchangeListVO> getPage(ReturnExchangeSearchForm form) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, form.getUserGroup(), Constants.USER_PERMISSION_RETURN_EXCHANGE_ORDER);
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
    Map<String, Object> queryMap = form.toQueryMap(mergeUserPermission);
    PageResult<ReturnExchangeListVO> page = returnExchangeOrderDao.getPage(queryMap);
    List<ReturnExchangeListVO> content = page.getContent();
    List<String> supplierOrderIds =
        content.stream().map(ReturnExchangeListVO::getSupplierOrderId)
        .collect(Collectors.toList());
    // 附件
    List<File> attachments =
        fileRepository.findByRelationIdInAndRelationTypeAndState(supplierOrderIds,
            Constants_FileRelationType.ORDER_ANNEX, Constants.STATE_OK);
    // 根据supplierOrderId分组
    Map<String, List<File>> attachmentMap =
        attachments.stream().collect(Collectors.groupingBy(File::getRelationId));
    content.forEach(vo -> {
      List<File> findAttachments = attachmentMap.get(vo.getSupplierOrderId());
      findAttachments = CollUtil.emptyIfNull(findAttachments);
      List<FileDTOExt> fileDTOExts = findAttachments.parallelStream()
          .map(item -> new FileDTOExt(item.getId(), item.getDescription(), item.getUrl(), baseUrl))
          .collect(Collectors.toList());
      vo.setAnnexDTOs(fileDTOExts);
    });
    // 查询附件列表
    return page;
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_RETURN_EXCHANGE_ORDER)
  public ReturnExchangeStatistics getStatistics(ReturnExchangeSearchForm form) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_RETURN_EXCHANGE_ORDER);
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
    Map<String, Object> queryMap = form.toQueryMap(mergeUserPermission);
    return returnExchangeOrderDao.getStatistics(queryMap);
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_RETURN_EXCHANGE_ORDER)
  public ReturnExchangeOrderCount getCount(ReturnExchangeSearchForm form) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    form.setPageNo(1);
    form.setPageSize(1);
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_RETURN_EXCHANGE_ORDER);
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, new OperatorPermission());
    form.setTabState(SupplierOrderState.REVIEWED.name());
    long reviewed =
        returnExchangeOrderDao.getCount(form.toQueryMap(mergeUserPermission));
    form.setTabState(SupplierOrderState.NOT_REVIEWED.name());
    long notReviewed =
        returnExchangeOrderDao.getCount(form.toQueryMap(mergeUserPermission));
    ReturnExchangeOrderCount count = new ReturnExchangeOrderCount();
    count.setReviewedCount(reviewed);
    count.setNotReviewedCount(notReviewed);
    return count;
  }

  @Override
  public ReturnExchangeVO getDetail(String id) {
    SupplierOrder supplierOrder = null;
    ReturnExchangeOrder returnExchangeOrder = null;
    Optional<SupplierOrder> optionalSupplierOrder = supplierOrderRepository.findById(id);
    if (optionalSupplierOrder.isPresent()) {
      supplierOrder = optionalSupplierOrder.get();
      returnExchangeOrder =
          returnExchangeOrderRepository.findFirstBySupplierOrderIdAndState(supplierOrder.getId(),
              Constants.STATE_OK);
    } else {
      returnExchangeOrder = returnExchangeOrderRepository.findById(id)
          .orElseThrow(() -> new CheckException("退换货订单不存在"));
      supplierOrder = supplierOrderRepository.findById(returnExchangeOrder.getSupplierOrderId())
          .orElseThrow(() -> new CheckException("退换货订单不存在"));
    }
    // 查询退换货订单
    if (returnExchangeOrder == null) {
      throw new CheckException("退换货订单不存在");
    }
    if (returnExchangeOrder.getState().equals(Constants.STATE_DELETE)) {
      throw new CheckException("退换货订单已删除");
    }
    if (supplierOrder == null) {
      throw new CheckException("退换货订单不存在");
    }
    // 查询附件
    List<File> attachments =
        fileRepository.findByRelationIdInAndRelationTypeAndState(
            CollUtil.toList(supplierOrder.getId()),
            Constants_FileRelationType.ORDER_ANNEX, Constants.STATE_OK);
    List<FileDTOExt> fileDTOExts = attachments.parallelStream()
        .map(item -> new FileDTOExt(item.getId(), item.getDescription(), item.getUrl(), baseUrl))
        .collect(Collectors.toList());
    // 查询订单明细
    List<SupplierOrderToForm> supplierOrderToForms =
        supplierOrderToFormRepository.findBySupplierOrderIdAndState(supplierOrder.getId(),
            Constants.STATE_OK);
    SupplierOrderToForm detailForm = supplierOrderToForms.stream()
        .filter(item -> item.getType().equals(SupplierOrderFormType.DETAILED.getType())).findFirst()
        .orElse(null);
    if (detailForm == null) {
      throw new CheckException("订单详情不存在");
    }
    // 查询订单物料
    List<SupplierOrderDetail> supplierOrderDetails =
        supplierOrderDetailRepository.findByOrderToFormIdAndState(detailForm.getId(), Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierOrderDetails)) {
      throw new CheckException("订单物料不存在");
    }
    // 查询已开票数量
    List<SupplierOrderToForm> outForm = supplierOrderToForms.stream()
        .filter(item -> item.getType().equals(SupplierOrderFormType.RETURN.getType()))
        .collect(Collectors.toList());
    BigDecimal invoiceTotalNum = CollUtil.emptyIfNull(outForm).stream().map(
        supplierOrderToForm -> supplierOrderDetailRepository.getAllByOrderToFormIdAndStateOrderBySortNumAsc(
                supplierOrderToForm.getId(), Constants.STATE_OK).stream()
            .map(SupplierOrderDetail::getInvoicedNum).filter(Objects::nonNull)
            .reduce(BigDecimal.ZERO, BigDecimal::add)).reduce(BigDecimal.ZERO, BigDecimal::add);
    // 查询关联发票号
    List<InputInvoiceOrder> orderInvoiceRelations =
        orderInvoiceRelationRepository.findAllByOrderCodesAndStateAndInvoiceState(supplierOrder.getCode(),
            Constants.STATE_OK, Constants.ORDER_INVOICE_STATE_PASS);
    List<PurchaseOrderInvoiceRelation> invoiceRelations =
        CollUtil.emptyIfNull(orderInvoiceRelations).stream().map(PurchaseOrderInvoiceRelation::new)
            .collect(Collectors.toList());
    // 查询订单物料关联表
    return returnExchangeOrderFactory.toVO(returnExchangeOrder, supplierOrder, supplierOrderDetails,
        fileDTOExts, invoiceRelations, invoiceTotalNum);
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_RETURN_EXCHANGE_ORDER)
  public void export(ReturnExchangeSearchForm form) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    checkExportPermission(user.getId());
    form.setPageNo(1);
    form.setPageSize(Integer.MAX_VALUE);
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, form.getUserGroup(), Constants.USER_PERMISSION_RETURN_EXCHANGE_ORDER);
    OperatorPermission operatorPermission =
        sharePermissionTypeService.getOperatorPermission(user, form.getUserGroup(), Constants.USER_PERMISSION_EXPORT_RETURN_EXCHANGE);
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, operatorPermission);
    Map<String, Object> queryMap = form.toQueryMap(mergeUserPermission);
    queryMap.put("version", ShardingContext.getVersion());
    Mission mission =
        missionService.createMission(user, MissionTypeEnum.BATCH_TASK_EXPORT_RETURN_EXCHANGE.getTypeName(),
            Constants.PLATFORM_TYPE_AFTER,
            null,
            null);
    missionDispatcher.doDispatch(mission.getId(), JSON.toJSONString(queryMap),
        MissionTypeEnum.BATCH_TASK_EXPORT_RETURN_EXCHANGE);
  }

  /**
   * 校验导出权限
   * @param userId
   */
  private void checkExportPermission(String userId) {
    String permissionCode =
        userService.getPermissionByType(userId, Constants.USER_PERMISSION_EXPORT_RETURN_EXCHANGE);
    permissionCode = StrUtil.blankToDefault(permissionCode, Constants.EXPORT_OWM_PURCHASE_TYPE);
    if (StrUtil.equals(permissionCode, Constants.NOT_EXPORT_IMPORT_KEY)) {
      throw new CheckException("您没有退换货订单导出权限！");
    }
  }

  @Override
  public void batchDelete(List<String> ids) {
    for (String id : ids) {
      // 开启事务
      TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
      try {
        transactionTemplate.execute(status -> {
        this.delete(id);
        return null;
        });
      } catch (Exception e) {
        throw e;
      }
    }
  }


  @Override
  public void delete(String id) {
    ReturnExchangeOrder returnExchangeOrder = returnExchangeOrderRepository.findById(id)
        .orElseThrow(() -> new CheckException("退换货订单不存在"));
    if (Constants.STATE_DELETE.equals(returnExchangeOrder.getState())) {
      throw new CheckException("退换货订单已删除");
    }
    SupplierOrder supplierOrder =
        supplierOrderRepository.findById(returnExchangeOrder.getSupplierOrderId())
            .orElseThrow(() -> new CheckException("退换货订单不存在"));
    // 暂存或驳回状态才能删除
    if (!SupplierOrderState.STAGING.getKey().equals(supplierOrder.getOrderState())
        && !SupplierOrderState.REJECT.getKey().equals(supplierOrder.getOrderState())) {
      throw new CheckException("只有暂存或驳回状态才能删除");
    }
    // 删除订单
    supplierOrder.setState(Constants.STATE_DELETE);
    supplierOrderRepository.saveAndFlush(supplierOrder);
    // 删除退换货订单
    returnExchangeOrder.setState(Constants.STATE_DELETE);
    returnExchangeOrderRepository.saveAndFlush(returnExchangeOrder);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void deleteReturnExchangeOrder(List<String> ids) {
    for (String id : ids) {
      ReturnExchangeOrder returnExchangeOrder = returnExchangeOrderRepository.findById(id)
          .orElseThrow(() -> new CheckException("退换货订单不存在"));
      if (Constants.STATE_DELETE.equals(returnExchangeOrder.getState())) {
        throw new CheckException("退换货订单已删除");
      }
      SupplierOrder supplierOrder =
          supplierOrderRepository.findById(returnExchangeOrder.getSupplierOrderId())
              .orElseThrow(() -> new CheckException("退换货订单不存在"));
      // 暂存或驳回状态才能删除
      if (!SupplierOrderState.STAGING.getKey().equals(supplierOrder.getOrderState())
          && !SupplierOrderState.REJECT.getKey().equals(supplierOrder.getOrderState())) {
        throw new CheckException("只有暂存或驳回状态才能删除");
      }
      // 删除订单
      supplierOrder.setState(Constants.STATE_DELETE);
      supplierOrderRepository.saveAndFlush(supplierOrder);
      //删除退库单
      List<SupplierOrderToForm> supplierOrderToFormList =
          supplierOrderToFormService.findBySupplierOrderId(supplierOrder.getId());
      for (SupplierOrderToForm supplierOrderToForm : supplierOrderToFormList) {
        supplierOrderToForm.setState(Constants.STATE_NO);
        supplierOrderToFormRepository.save(supplierOrderToForm);
      }
      // 删除退换货订单
      returnExchangeOrder.setState(Constants.STATE_DELETE);
      returnExchangeOrderRepository.saveAndFlush(returnExchangeOrder);
    }

  }

  @Override
  public List<NewReturnVO> getReturnList(String supplierOrderId) {
    ReturnExchangeOrder returnExchangeOrder =
        returnExchangeOrderRepository.findFirstBySupplierOrderIdAndState(supplierOrderId,
            Constants.STATE_OK);
    if (returnExchangeOrder == null) {
      throw new CheckException("退换货订单不存在");
    }
    SupplierOrder supplierOrder =
        supplierOrderRepository.findById(supplierOrderId)
            .orElseThrow(() -> new CheckException("退换货订单不存在"));
    // 查询退库单form
    List<SupplierOrderToForm> supplierOrderToForms =
        supplierOrderToFormRepository.findBySupplierOrderIdAndTypeAndState(supplierOrderId, SupplierOrderFormType.RETURN.getType(), Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierOrderToForms)) {
      return new ArrayList<>();
    }
    List<String> formIds = supplierOrderToForms.stream().map(SupplierOrderToForm::getId)
        .collect(Collectors.toList());
    // 查询退库单明细
    List<SupplierOrderDetail> supplierOrderDetails =
        supplierOrderDetailRepository.findByOrderToFormIdInAndState(formIds, Constants.STATE_OK);
    return returnExchangeOrderFactory.toNewReturnVOList(supplierOrderToForms, supplierOrderDetails);
  }

  @Override
  public void getCancelOrderList(String test) {
  }

  @Override
  public byte[] downloadReturnExchangeOrderFill(List<ReturnExchangeOrderFillDTO> fillDTOList)
      throws IOException {
    XSSFWorkbook workbook = new XSSFWorkbook();
    List<Integer> widths = new ArrayList<>();
    List<String> titles = new ArrayList<>();
    if (VersionEnum.V2 == ShardingContext.getVersion()) {
      titles = Constants_Excel.DOWNLOAD_RETURN_EXCHANGE_ORDER_INFO_V2;
    } else {
      titles = Constants_Excel.DOWNLOAD_RETURN_EXCHANGE_ORDER_INFO;
    }
    int size = titles.size();
    for (int i = 0; i < size; i++) {
      widths.add(30);
    }
    Sheet sheet = exportUtil.createSheet(workbook, "product", widths);
    CellStyle titleStyle = exportUtil.getTitleStyle(workbook);
    CellStyle baseStyle = exportUtil.getBaseStyle(workbook);
    Row titleRow = sheet.createRow(0);
    exportUtil.createTitle(titles, titleStyle,titleRow);
    fillDTOList = CollUtil.emptyIfNull(fillDTOList);
    for (int i = 0; i < fillDTOList.size(); i++) {
      Row row = sheet.createRow(i + 1);
      ReturnExchangeOrderFillDTO dto = fillDTOList.get(i);
      int index = 0;
      exportUtil.createCell(row, index++, dto.getIndexNum(), baseStyle);
      exportUtil.createCell(row, index++, dto.getProductCode(), baseStyle);
      exportUtil.createCell(row, index++, dto.getBrand(), baseStyle);
      exportUtil.createCell(row, index++, dto.getProductName(), baseStyle);
      exportUtil.createCell(row, index++, dto.getDescription(), baseStyle);
      if (VersionEnum.V2 == ShardingContext.getVersion()) {
        exportUtil.createCell(row, index++, dto.getSpecification(), baseStyle);
        exportUtil.createCell(row, index++, dto.getModel(), baseStyle);
      } else {
        exportUtil.createCell(row, index++, dto.getManuCode(), baseStyle);
      }
      exportUtil.createCell(row, index++, dto.getUnit(), baseStyle);
      // 退货数量转换为3位小数
      BigDecimal num = dto.getNum();
      String numStr = num == null ? "" : num.setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
      exportUtil.createCell(row, index++, numStr, baseStyle);
      // 入库原价(去税)转换为6位小数
      BigDecimal originalPrice = dto.getOriginalPrice();
      String originalPriceStr = originalPrice == null ? "" : originalPrice.setScale(6, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
      exportUtil.createCell(row, index++, originalPriceStr, baseStyle);
      // 本次退货含税单价转换为6位小数
      BigDecimal price = dto.getPrice();
      String priceStr = price == null ? "" : price.setScale(6, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
      exportUtil.createCell(row, index++, priceStr, baseStyle);
      // 税率转换为百分比
      exportUtil.createCell(row, index++, dto.getTaxRate(), baseStyle);
      // 入库原总价(去税)转换为6位小数
      BigDecimal originalTotalPrice = dto.getOriginalTotalPrice();
      String originalTotalPriceStr = originalTotalPrice == null ? "" : originalTotalPrice.setScale(6, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString();
      exportUtil.createCell(row, index++, originalTotalPriceStr, baseStyle);
      exportUtil.createCell(row, index++, dto.getBatchNo(), baseStyle);
      exportUtil.createCell(row, index++, dto.getMark(), baseStyle);
    }
    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    workbook.write(byteArrayOutputStream);
    return byteArrayOutputStream.toByteArray();
  }

  @Override
  public List<ReturnExchangeOrderFillDTO> fill(MultipartFile file) throws IOException {
    if (file == null || file.isEmpty()) {
      throw new CheckException("文件不能为空");
    }
    List<String> titles = new ArrayList<>();
    if (VersionEnum.V2 == ShardingContext.getVersion()) {
      titles = Constants_Excel.DOWNLOAD_RETURN_EXCHANGE_ORDER_INFO_V2;
    } else {
      titles = Constants_Excel.DOWNLOAD_RETURN_EXCHANGE_ORDER_INFO;
    }
    //    fillDTOList = CollUtil.emptyIfNull(fillDTOList);
//    // 根据序号转换为map
//    Map<Integer, ReturnExchangeOrderFillDTO> originMap = fillDTOList.stream()
//        .collect(Collectors.toMap(ReturnExchangeOrderFillDTO::getIndexNum, Function.identity(),
//            (k1, k2) -> k1));
    List<ReturnExchangeOrderFillDTO> res = new ArrayList<>();
    try (Workbook workbook = exportUtil.buildByFile(StrUtil.emptyIfNull(file.getOriginalFilename()),
        file.getInputStream());) {
      Sheet sheet = workbook.getSheetAt(0);
      if (!exportUtil.validateExcel(sheet, 0, titles)) {
        throw new CheckException("文件异常，请导入指定模板。");
      }
      int rowNumCount = sheet.getPhysicalNumberOfRows();
      if (rowNumCount > 1) {
        for (int i = 0; i < rowNumCount; i++) {
          Row row = sheet.getRow(i + 1);
          if (exportUtil.isEmptyRow(row)) {
            continue;
          }
          boolean fail = false;
          ReturnExchangeOrderFillDTO dto = new ReturnExchangeOrderFillDTO();
          int index = 0;
          try {
            String indexNum = exportUtil.getCellStringValue(row.getCell(index++));
            String productCode = exportUtil.getCellStringValue(row.getCell(index++));
            String brand = exportUtil.getCellStringValue(row.getCell(index++));
            String productName = exportUtil.getCellStringValue(row.getCell(index++));
            String description = exportUtil.getCellStringValue(row.getCell(index++));
            if (VersionEnum.V2 == ShardingContext.getVersion()) {
              dto.setSpecification(exportUtil.getCellStringValue(row.getCell(index++)));
              dto.setModel(exportUtil.getCellStringValue(row.getCell(index++)));
            } else {
              dto.setManuCode(exportUtil.getCellStringValue(row.getCell(index++)));
            }
            String unit = exportUtil.getCellStringValue(row.getCell(index++));
            String num = exportUtil.getCellStringValue(row.getCell(index++));
            String originalPrice = exportUtil.getCellStringValue(row.getCell(index++));
            String price = exportUtil.getCellStringValue(row.getCell(index++));
            String taxRate = exportUtil.getCellStringValue(row.getCell(index++));
            String originalTotalPrice = exportUtil.getCellStringValue(row.getCell(index++));
            String batchNo = exportUtil.getCellStringValue(row.getCell(index++));
            String mark = exportUtil.getCellStringValue(row.getCell(index++));
            // 校验是否为数字类型
            Integer indexNumInt = -1;
            if (NumberUtil.isInteger(indexNum)) {
              indexNumInt = NumberUtil.parseInt(indexNum);
            }
            dto.setIndexNum(indexNumInt);
            dto.setProductCode(productCode);
            dto.setBrand(brand);
            dto.setProductName(productName);
            dto.setDescription(description);
            dto.setUnit(unit);
            BigDecimal numDecimal = Convert.toBigDecimal(num);
            if (numDecimal != null) {
              dto.setNum(numDecimal.setScale(3, RoundingMode.HALF_UP));
            }
            dto.setNum(numDecimal);
            BigDecimal originalPriceDecimal = Convert.toBigDecimal(originalPrice);
            if (originalPriceDecimal != null) {
              dto.setOriginalPrice(originalPriceDecimal.setScale(6, RoundingMode.HALF_UP));
            }
            dto.setOriginalPrice(originalPriceDecimal);
            BigDecimal priceDecimal = Convert.toBigDecimal(price);
            if (priceDecimal != null) {
              dto.setPrice(priceDecimal.setScale(6, RoundingMode.HALF_UP));
            }
            dto.setPrice(priceDecimal);
            dto.setTaxRate(taxRate);
            BigDecimal originalTotalPriceDecimal = Convert.toBigDecimal(originalTotalPrice);
            if (originalTotalPriceDecimal != null) {
              dto.setOriginalTotalPrice(originalTotalPriceDecimal.setScale(6, RoundingMode.HALF_UP));
            }
            dto.setOriginalTotalPrice(originalTotalPriceDecimal);
            dto.setBatchNo(batchNo);
            dto.setMark(mark);
            // 报错文案：导入完成！【序号】【物料编码】报错文案}、【序号】【物料编码】报错文案}、【序号】【物料编码】报错文案}
            StringBuilder errorMsg = new StringBuilder();
            errorMsg.append("【").append(indexNumInt).append("】")
                .append("【").append(productCode).append("】");
//            ReturnExchangeOrderFillDTO originOne = originMap.get(indexNumInt);
//            if (originOne == null || !originOne.getProductCode().equals(productCode)) {
//              errorMsg.append(", 物料编码和序号与订单内不一致");
//              fail = true;
//            }
            // 退货数量最大填写三位小数
            BigDecimal validNum = Convert.toBigDecimal(num);
            if (validNum != null && validNum.scale() > 3) {
              errorMsg.append(", 退货数量最大填写三位小数");
              fail = true;
            }
            // 含税单价最大支持六位小数
            BigDecimal validPrice = Convert.toBigDecimal(price);
            if (validPrice != null && validPrice.scale() > 6) {
              errorMsg.append(", 含税单价最大支持六位小数");
              fail = true;
            }
            // 税率输入错误
            Set<String> taxRateSet = Constants_PurchaseInfoRecord.TAX_RATE_MAP.keySet();
            if (!taxRateSet.contains(taxRate)) {
              errorMsg.append(", 税率输入错误");
              fail = true;
            }
            // 备注最大只支持150字
            if (StrUtil.isNotBlank(mark) && mark.length() > 150) {
              errorMsg.append(", 备注最大只支持150字");
              fail = true;
            }
            if (fail) {
              dto.setErrorMsg(errorMsg.toString());
            }
            res.add(dto);
          } catch (Exception e) {
            throw new CheckException("第" + (i + 1) + "行数据异常：" + e.getMessage());
          }
        }
      }
      return res;
    }
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_RETURN_EXCHANGE_ORDER)
  public Long exportCount(ReturnExchangeSearchForm form) {
    if (CollUtil.isNotEmpty(form.getIds())) {
      return Long.valueOf(form.getIds().size());
    }
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    SearchPermission searchPermission =
        sharePermissionTypeService.getSearchPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_RETURN_EXCHANGE_ORDER);
    OperatorPermission operatorPermission =
        sharePermissionTypeService.getOperatorPermission(user, form.getUserGroup(),
            Constants.USER_PERMISSION_RETURN_EXCHANGE_ORDER);
    MergeUserPermission mergeUserPermission =
        sharePermissionTypeService.mergePermission(searchPermission, operatorPermission);
    Map<String, Object> queryMap = form.toQueryMap(mergeUserPermission);
    return returnExchangeOrderDao.getPage(queryMap).getTotalCount();
  }

  @Override
  public void importExchangeOrder(MultipartFile file) {
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    String savePath = null;
    try {
      savePath = importExcelUtil.saveExcel(file);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
    Mission mission = missionService.createMission(user,
        MissionTypeEnum.BATCH_TASK_IMPORT_RETURN_EXCHANGE_ORDER.getTypeName(), Constants.PLATFORM_TYPE_AFTER,
        null, null);
    Map<String, Object> mapParam = new HashMap<>(4);
    mapParam.put("userId", user.getId());
    mapParam.put("filePath", savePath);
    mapParam.put("fileName", file.getOriginalFilename());
    mapParam.put("version", ShardingContext.getVersion());
    missionDispatcher.doDispatch(
        mission.getId(),
        JSON.toJSONString(mapParam),
        MissionTypeEnum.BATCH_TASK_IMPORT_RETURN_EXCHANGE_ORDER);
  }
}
