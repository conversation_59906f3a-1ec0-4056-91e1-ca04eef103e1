package com.xhgj.srm.api.task;

import com.xhgj.srm.api.service.GroupService;
import com.xhgj.srm.api.service.InventoryLocationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
@Component
@Slf4j
public class GroupTask {
  @Resource
  private GroupService groupService;

  @Resource
  private InventoryLocationService inventoryLocationService;

  @Scheduled(cron = "0 0 2 ? * *")
  private void  syncOrg() {
    log.info("同步组织部门开始========================================================");
    groupService.syncOrg();
    log.info("同步组织部门结束========================================================");
  }


  @Scheduled(cron = "0 0 0 * * ?")
  private void  syncInventoryLocationListTask() {
    log.info("同步库位列表开始========================================================");
    inventoryLocationService.syncInventoryLocationListTask();
    log.info("同步库位列表结束========================================================");
  }


}
