package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.supplier.SupplierFinancialDTO;
import com.xhgj.srm.dto.financial.FinancialDTO;
import com.xhgj.srm.jpa.entity.BaseSupplierInGroup;
import com.xhgj.srm.jpa.entity.Financial;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

public interface FinancialService extends BootBaseService<Financial, String> {

  void addFinancial(List<FinancialDTO> financialDTOS, Supplier supplier);

  /**
   * 将供应商的财务复制到目标供应商副本
   *
   * @param supplierId 供应商 id
   * @param supplierFb 目标供应商副本实体
   */
  void copySupplierFinancialToSupplierFb(String supplierId, SupplierFb supplierFb);

  /** 更新供应商信息 */
  void deleteAndUpdateFinancialInSupplier(List<FinancialDTO> financialDTOS, String supplierId);

  /**
   * 删除供应商财务信息
   *
   * @param supplierId
   */
  void deleteFinancialInSupplier(String supplierId);

  /**
   * 根据副本信息更新供应商财务西悉尼
   *
   * @param fbId
   * @param supplierId
   */
  void updateFinancialInSupplierByFb(String supplierId, String fbId);

  /**
   * 根据组织内供应商 id 获得财务信息
   *
   * @param supplierInGroupId
   */
  List<Financial> getFinancialListBySupplierInGroupId(String supplierInGroupId);

  /**
   * 保存组织内供应商财务信息
   *
   * @param baseSupplierInGroup 组织内供应商 ，必传
   * @param supplierInGroup 目标实体，若是更新
   * @param dtoList 参数对象列表
   */
  void saveSupplierInGroupFinancial(
      BaseSupplierInGroup baseSupplierInGroup,
      SupplierInGroup supplierInGroup,
      List<SupplierFinancialDTO> dtoList);

  /**
   * 保存个人供应商的财务信息
   *
   * @param supplierInGroupId 组织内供应商 id
   * @param bankName 开户银行
   * @param bankNum 银行账号
   * @param bankNum bankAccount
   */
  void savePersonSupplierInGroupFinancial(
      String supplierInGroupId, String bankName, String bankNum,String bankAccount,String bankCode);
  /**
   * 通过组织下供应商 id 获得个人供应商的财务信息
   *
   * @param supplierInGroupId 组织下供应商 id 必传
   */
  Financial getPersonSupplierFinancial(String supplierInGroupId);

  /**
   * 获取供应商所有财务信息
   */
  List<SupplierFinancialDTO> getList(String supplierId, String groupCode);


  List<Financial> getBySupplierId(String supplierId);

  /**
   * @description: 更新银行账户信息
   * @param: supplierId
   * @param: enterpriseName
   **/
  void updateFinancialRecord(String supplierId, String enterpriseName);
}
