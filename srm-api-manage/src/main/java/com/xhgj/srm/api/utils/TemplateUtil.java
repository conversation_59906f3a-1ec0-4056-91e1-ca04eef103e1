package com.xhgj.srm.api.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.supplier.ChinaSupplierDTO;
import com.xhgj.srm.api.dto.supplier.SupplierFileDTO;
import com.xhgj.srm.api.vo.supplier.ChinaSupplierVO;
import com.xhgj.srm.common.ConstantsSupplierTemplate;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/8/1 9:41
 */
public class TemplateUtil {

  public static final Map<String, String> ALL_FIELD_TO_NAME = new HashMap<>();

  static {
    ALL_FIELD_TO_NAME.putAll(ConstantsSupplierTemplate.SUPPLIER_TEMPLATE_CHINA_BASE_INFO);
    ALL_FIELD_TO_NAME.putAll(ConstantsSupplierTemplate.SUPPLIER_TEMPLATE_CHINA_FINANCE_INFO);
    ALL_FIELD_TO_NAME.putAll(ConstantsSupplierTemplate.SUPPLIER_TEMPLATE_CHINA_CONTACT_INFO);
    ALL_FIELD_TO_NAME.putAll(ConstantsSupplierTemplate.SUPPLIER_LEVEL_LINK_BRAND);
    ALL_FIELD_TO_NAME.putAll(ConstantsSupplierTemplate.SUPPLIER_LEVEL_LINK_CATEGORY);
    ALL_FIELD_TO_NAME.putAll(ConstantsSupplierTemplate.SUPPLIER_LEVEL_LINK_EVALUATION_TABLE);
    ALL_FIELD_TO_NAME.putAll(ConstantsSupplierTemplate.SUPPLIER_TEMPLATE_ABROAD_BASE_INFO);
    ALL_FIELD_TO_NAME.putAll(ConstantsSupplierTemplate.SUPPLIER_TEMPLATE_ABROAD_FINANCE_INFO);
    ALL_FIELD_TO_NAME.putAll(ConstantsSupplierTemplate.SUPPLIER_TEMPLATE_ABROAD_CONTACT_INFO);
    ALL_FIELD_TO_NAME.putAll(ConstantsSupplierTemplate.SUPPLIER_TEMPLATE_PERSON_BASE_INFO);
    ALL_FIELD_TO_NAME.putAll(ConstantsSupplierTemplate.SUPPLIER_TEMPLATE_PERSON_FINANCE_INFO);
  }

  /**
   * 根据模板表校验对象
   *
   * @param obj 校验对象
   * @param map 字段与是否必填的对应关系表
   */
  public static void validateObjByTemplateMap(Object obj, Map<String, Boolean> map) {
    String str =
        Arrays.stream(ReflectUtil.getFields(obj.getClass()))
            .filter(
                field -> {
                  String fieldName = ReflectUtil.getFieldName(field);
                  boolean required = BooleanUtil.isTrue(MapUtil.getBool(map, fieldName));
                  Object fieldValue = ReflectUtil.getFieldValue(obj, field);
                  return required && ObjectUtil.isEmpty(fieldValue);
                })
            .map(ReflectUtil::getFieldName)
            .map(ALL_FIELD_TO_NAME::get)
            .collect(Collectors.joining(","));

    if (obj instanceof ChinaSupplierDTO) {
      ChinaSupplierDTO chinaSupplierDTO = (ChinaSupplierDTO) obj;
      String level = chinaSupplierDTO.getEnterpriseLevel();
      String brandResult = validBrand(level, chinaSupplierDTO.getBrandList(), map);
      if (StrUtil.isNotBlank(brandResult)) {
        str += brandResult;
        throw new CheckException("您好，当前等级的供应商需关联经营品牌才可提交。");
      }
      String categoryResult = validCategory(level, chinaSupplierDTO.getCategoryList(), map);
      if (StrUtil.isNotBlank(categoryResult)) {
        str += categoryResult;
        throw new CheckException("您好，当前等级的供应商需关联经营类目才可提交。");
      }
      // 供应商评估表
      String evaluationTableStr = validEvaluationTable(level, chinaSupplierDTO.getEvaluationTable()
          , map);
      if (StrUtil.isNotBlank(evaluationTableStr)) {
        str += evaluationTableStr;
        throw new CheckException("您好，当前等级的供应商需关联供应商评估表才可提交。");
      }
    }
    if (obj instanceof ChinaSupplierVO) {
      ChinaSupplierVO chinaSupplierVO = (ChinaSupplierVO) obj;
      String level = chinaSupplierVO.getEnterpriseLevel();
      String brandResult = validBrand(level, chinaSupplierVO.getBrandList(), map);
      if (StrUtil.isNotBlank(brandResult)) {
        str += brandResult;
        throw new CheckException("您好，当前等级的供应商需关联经营品牌才可提交。");
      }
      String categoryResult = validCategory(level, chinaSupplierVO.getCategoryList(), map);
      if (StrUtil.isNotBlank(categoryResult)) {
        str += categoryResult;
        throw new CheckException("您好，当前等级的供应商需关联经营类目才可提交。");
      }
      // 供应商评估表
      String evaluationTableStr = validEvaluationTable(level, chinaSupplierVO.getEvaluationTable()
          , map);
      if (StrUtil.isNotBlank(evaluationTableStr)) {
        str += evaluationTableStr;
        throw new CheckException("您好，当前等级的供应商需关联供应商评估表才可提交。");
      }
    }
    // 去除最后一个逗号
    if (StrUtil.isNotBlank(str) && str.endsWith(",")) {
      str = str.substring(0, str.length() - 1);
    }
    if (!StringUtils.isNullOrEmpty(str)) {
      throw new CheckException("如下字段必填：" + str);
    }
  }

  /**
   * 根据传入对象和供应商等级校验对象
   *
   * @param obj 校验对象
   * @param level 供应商等级
   * @param map 字段与是否必填的对应关系表
   */
  public static boolean validateObjByTemplateMap(ChinaSupplierVO obj, String level,
      Map<String, Boolean> map) {

    String str = Arrays.stream(ReflectUtil.getFields(obj.getClass())).filter(field -> {
      String fieldName = ReflectUtil.getFieldName(field);
      boolean required = BooleanUtil.isTrue(MapUtil.getBool(map, fieldName));
      Object fieldValue = ReflectUtil.getFieldValue(obj, field);
      return required && ObjectUtil.isEmpty(fieldValue);
    }).map(ReflectUtil::getFieldName).map(ALL_FIELD_TO_NAME::get).collect(Collectors.joining(","));
    String brandResult = validBrand(level, obj.getBrandList(), map);
    if (StrUtil.isNotBlank(brandResult)) {
      str += brandResult;
    }
    String categoryResult = validCategory(level, obj.getCategoryList(), map);
    if (StrUtil.isNotBlank(categoryResult)) {
      str += categoryResult;
    }
    // 去除最后一个逗号
    if (StrUtil.isNotBlank(str) && str.endsWith(",")) {
      str = str.substring(0, str.length() - 1);
    }
    if (!StringUtils.isNullOrEmpty(str)) {
      return false;
    }
    return true;
  }

  /**
   * 经营品牌
   */
  private static String validBrand(String level, List<?> brandList, Map<String, Boolean> map) {
    // 获取等级
    SupplierLevelEnum levelEnum = SupplierLevelEnum.getEnum(level);
    // 获取模板中品牌等级配置
    String fieldName =
        String.format("%s-%s-%s", "brand", levelEnum.getField(), levelEnum.getCode());
    Boolean brandNeed = map.get(fieldName);
    if (Boolean.TRUE.equals(brandNeed)) {
      // 判断chinaSupplierVO的brandList是否为空
      if (CollUtil.isEmpty(brandList)) {
        return "经营品牌,";
      }
    }
    return "";
  }

  /**
   * 经营类目
   */
  private static String validCategory(String level, List<?> categoryList, Map<String, Boolean> map) {
    // 获取等级
    SupplierLevelEnum levelEnum = SupplierLevelEnum.getEnum(level);
    // 获取模板中类目等级配置
    String fieldName =
        String.format("%s-%s-%s", "category", levelEnum.getField(), levelEnum.getCode());
    Boolean categoryNeed = map.get(fieldName);
    if (Boolean.TRUE.equals(categoryNeed)) {
      // 判断chinaSupplierVO的categoryList是否为空
      if (CollUtil.isEmpty(categoryList)) {
        return "经营类目,";
      }
    }
    return "";
  }

  /**
   * 供应商评估表
   */
  private static String validEvaluationTable(String level, SupplierFileDTO evaluationTable, Map<String,
      Boolean> map) {
    // 获取等级
    SupplierLevelEnum levelEnum = SupplierLevelEnum.getEnum(level);
    // 获取模板中类目等级配置
    String fieldName =
        String.format("%s-%s-%s", "evaluation", levelEnum.getField(), levelEnum.getCode());
    Boolean evaluationTableNeed = map.get(fieldName);
    if (Boolean.TRUE.equals(evaluationTableNeed)) {
      // 判断chinaSupplierVO的evaluationTable的url为空
      if (Objects.isNull(evaluationTable) || StrUtil.isBlank(evaluationTable.getUrl())) {
        return "供应商评估表,";
      }
    }
    return "";
  }


}
