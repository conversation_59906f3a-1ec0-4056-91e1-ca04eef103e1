package com.xhgj.srm.api.dto;

import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.jpa.entity.File;
import com.xhiot.boot.core.common.util.StringUtils;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/3/2 15:05
 */
@Data
public class SupplierDetailFile {

  private String uid;
  private String name;
  private String url;

  public SupplierDetailFile(File file, String baseUrl) {
    this.uid = file.getId();
    String fileName = StrUtil.emptyIfNull(file.getName());
    String fileUrl = StrUtil.emptyIfNull(file.getUrl());
    if (!StringUtils.isNullOrEmpty(fileUrl) && !fileUrl.contains("srm/")) {
      fileUrl = "srm" + StrUtil.addPrefixIfNot(fileUrl, "/");
    }
    this.name = !StringUtils.isNullOrEmpty(fileName) && !StringUtils.isNullOrEmpty(fileUrl) ? fileName + ";" + fileUrl : "";
    this.url = !StringUtils.isNullOrEmpty(fileUrl) ? baseUrl + fileUrl : "";
  }
}
