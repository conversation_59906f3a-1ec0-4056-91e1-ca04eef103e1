package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.GroupPageDTO;
import com.xhgj.srm.api.dto.suppliertemplate.SupplierTemplateDataDTO;
import com.xhgj.srm.api.dto.suppliertemplate.SupplierTemplateFieldDTO;
import com.xhgj.srm.api.dto.suppliertemplate.SupplierTemplatePageDTO;
import com.xhgj.srm.api.dto.suppliertemplate.SupplierTemplateQuery;
import com.xhgj.srm.api.dto.suppliertemplate.UpdateAndSaveSupplierTemplateDTO;
import com.xhgj.srm.common.enums.SupplierTemplateTypeEnum;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.SupplierTemplate;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Pageable;

/**
 * <AUTHOR>
 * @since 2022/7/5 11:12
 */
public interface SupplierTemplateService extends BootBaseService<SupplierTemplate, String> {

  /**
   * 修改或保存供应商模板
   *
   * @param updateAndSaveSupplierTemplateDTO 模板的 DTO
   */
  void updateAndSave(UpdateAndSaveSupplierTemplateDTO updateAndSaveSupplierTemplateDTO);

  /**
   * 分页查询供应商模板
   *
   * @param query 查询条件
   * @param param 分页参数
   */
  PageResult<SupplierTemplatePageDTO> findPage(SupplierTemplateQuery query, PageParam param);

  /**
   * 获取供应商详情，id 不传时是获取模板
   *
   * @param id 模板 id
   */
  List<SupplierTemplateDataDTO> getSupplierTemplateDTO(String id);

  /**
   * 删除供应商模板
   *
   * @param userId 用户 id
   * @param idList 供应商模板集合
   */
  void deleteSupplierTemplate(String userId, List<String> idList);

  /**
   * 公共组织编码和模板类型获得供应商模板
   *
   * @param userGroup 组织编码
   * @param type 模板类型 {@link SupplierTemplateTypeEnum}
   */
  List<SupplierTemplateFieldDTO> getSupplierTemplateByGroupCode(
      String userGroup, SupplierTemplateTypeEnum type);

  /**
   * 根据组织和类型获取字段与是否必填的对应关系
   *
   * @param group 组织，必传
   * @param supplierTemplateType 类型必传
   */
  Map<String, Boolean> getSupplierRequiredMap(
      Group group, SupplierTemplateTypeEnum supplierTemplateType);

  /**
   * 分页获取除去已经配置了模板的组织
   * @param toPageable 分页参数
   */
  PageResult<GroupPageDTO> getRemoveHasTemplateOrgPage(Pageable toPageable);
}
