package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.service.OrderAccountToOrderService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.OrderAccountToOrder;
import com.xhgj.srm.jpa.repository.OrderAccountToOrderRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023-02-23 21:12
 */
@Service
public class OrderAccountToOrderServiceImpl implements OrderAccountToOrderService {
  @Autowired
  private OrderAccountToOrderRepository repository;

  @Override
  public BootBaseRepository<OrderAccountToOrder, String> getRepository() {
    return repository;
  }

  @Override
  public List<String> getOrderIdLIstByOrderAccountId(String orderAccountId) {
    Assert.notBlank(orderAccountId);
    return CollUtil.emptyIfNull(
        repository.getAllByOrderAccountIdAndState(orderAccountId, Constants.STATE_OK))
        .stream().map(OrderAccountToOrder::getOrderId).collect(Collectors.toList());
  }

  @Override
  public OrderAccountToOrder getByOrderId(String orderId){
    return repository.getFirstByOrderIdAndState(orderId,Constants.STATE_OK);
  }

  @Override
  public String getAccountIdByOrderId(String orderId){
    return Optional.ofNullable(getByOrderId(orderId)).map(OrderAccountToOrder::getOrderAccountId)
        .orElse(StrUtil.EMPTY);
  }
}
