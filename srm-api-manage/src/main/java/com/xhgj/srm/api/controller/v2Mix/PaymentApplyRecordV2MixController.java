package com.xhgj.srm.api.controller.v2Mix;

import com.xhgj.srm.api.controller.AbstractRestController;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPrepaidApplicationPreInfoDTO;
import com.xhgj.srm.api.service.PurchaseOrderService;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("")
@Api(tags = {"付款申请记录相关api"})
@Slf4j
public class PaymentApplyRecordV2MixController extends AbstractRestController {

  @Resource private PurchaseOrderService purchaseOrderService;

  @ApiOperation("查询采购订单申请预付前置信息")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "ids", value = "采购订单 id", required = true),
      @ApiImplicitParam(name = "applyId", value = "编辑的申请 id", required = false)
  })
  @GetMapping(value = "/purchaseOrder/prepaid-application")
  public ResultBean<List<PurchaseOrderPrepaidApplicationPreInfoDTO>> getPrepaidApplicationPreInfo(
      @RequestParam List<String> ids,
      @RequestParam(required = false) String applyId
  ) {
    return new ResultBean<>(
        purchaseOrderService.getPrepaidApplicationPreInfoByIds(ids, applyId));
  }
}
