package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import com.xhgj.srm.api.dto.supplier.SupplierFileDTO;
import com.xhgj.srm.api.service.ExtraFileService;
import com.xhgj.srm.api.service.SupplierFbService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.ExtraFileDao;
import com.xhgj.srm.jpa.entity.ExtraFile;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.jpa.repository.ExtrafileRepository;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2021/3/1 18:53
 */
@Service
public class ExtraFileServiceImpl implements ExtraFileService {

  @Autowired ExtraFileDao dao;
  @Autowired ExtrafileRepository repository;
  @Autowired SupplierFbService supplierFbService;

  @Override
  public BootBaseRepository<ExtraFile, String> getRepository() {
    return repository;
  }

  @Transactional(propagation = Propagation.REQUIRES_NEW)
  @Override
  public void addZDYFile(String relationid, String filesall) {
    dao.deleteByRelationId(relationid, null);
    if (!StringUtils.isNullOrEmpty(filesall)) {
      String[] oneFile = filesall.split("&#&");
      if (ArrayUtil.isNotEmpty(oneFile)) {
        for (String s : oneFile) {
          String[] twoFile = s.split(",");
          if (ArrayUtil.isNotEmpty(twoFile)) {
            for (int j = 0; j < twoFile.length; j++) {
              String[] threeFile = twoFile[j].split(";");
              if (ArrayUtil.isNotEmpty(threeFile)) {
                saveExtraFile(
                    relationid,
                    ArrayUtil.get(threeFile, 0),
                    ArrayUtil.get(threeFile, 1),
                    ArrayUtil.get(threeFile, 2));
              }
            }
          }
        }
      }
    }
  }

  @Override
  public void addZDYFile(String relationId, String customName, List<SupplierFileDTO> files) {
    Assert.notEmpty(relationId);
    Assert.notEmpty(customName);
    dao.deleteByRelationId(relationId, customName);
    for (SupplierFileDTO file : CollUtil.emptyIfNull(files)) {
      saveExtraFile(relationId, customName, file.getName(), file.getUrl());
    }
  }

  private void saveExtraFile(String relationid, String relationName, String name, String url) {
    ExtraFile ef = new ExtraFile();
    ef.setCreateTime(System.currentTimeMillis());
    ef.setRelationId(relationid);
    ef.setState(Constants.STATE_OK);
    ef.setRelationName(relationName);
    ef.setName(name);
    ef.setUrl(url);
    if (!StringUtils.isNullOrEmpty(name)) {
      // 附件类型
      String fileType = name.substring(name.lastIndexOf(".") + 1).toLowerCase();
      String type = Constants.FILE_TYPE_PICTURE;
      if ("doc".equals(fileType)
          || "docx".equals(fileType)
          || "pdf".equals(fileType)
          || "ppt".equals(fileType)
          || "excel".equals(fileType)) {
        type = Constants.FILE_TYPE_WORD;
      }
      if ("jpg".equals(fileType)
          || "png".equals(fileType)
          || "bmp".equals(fileType)
          || "gif".equals(fileType)) {
        type = Constants.FILE_TYPE_PICTURE;
      }
      if ("rar".equals(fileType) || "zip".equals(fileType)) {
        type = Constants.FILE_TYPE_RARZIP;
      }
      ef.setType(type);
    }
    repository.save(ef);
  }

  @Transactional
  @Override
  public void copySupplierZDYFileToSupplierFb(String supplierId, String supplierFbId) {
    List<ExtraFile> eflist = dao.getFileListByRId(supplierId);
    if (CollUtil.isNotEmpty(eflist)) {
      for (ExtraFile extraFile : eflist) {
        ExtraFile newExtraFile = new ExtraFile();
        newExtraFile.setName(extraFile.getName());
        newExtraFile.setRelationName(extraFile.getRelationName());
        newExtraFile.setRelationId(supplierFbId);
        newExtraFile.setState(Constants.STATE_OK);
        newExtraFile.setType(extraFile.getType());
        newExtraFile.setUrl(extraFile.getUrl());
        newExtraFile.setCreateTime(System.currentTimeMillis());
        newExtraFile.setState(Constants.STATE_OK);
        repository.save(newExtraFile);
      }
    }
  }

  @Transactional
  @Override
  public void deleteAllZDYFile(String supplierId) {
    String hql = "delete from ExtraFile f where f.state = ? and f.relationId = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, supplierId};
    dao.executeUpdate(hql, params);
  }

  @Transactional
  @Override
  public void updateSupplierExtraFilesByFb(String supplierId, String fbid) {
    List<ExtraFile> eflist = dao.getFileListByRId(fbid);
    if (eflist != null && eflist.size() > 0) {
      for (ExtraFile ef : eflist) {
        ExtraFile efi = new ExtraFile();
        efi.setName(ef.getName());
        efi.setRelationName(ef.getRelationName());
        efi.setRelationId(supplierId);
        efi.setState(Constants.STATE_OK);
        efi.setType(ef.getType());
        efi.setUrl(ef.getUrl());
        efi.setCreateTime(System.currentTimeMillis());
        efi.setState(Constants.STATE_OK);
        repository.save(efi);
      }
      repository.flush();
    }
  }

  @Transactional
  @Override
  public void updateSupplierFbExtraFilesByFb(String oldFbId, String fbid) {
    List<ExtraFile> eflist = dao.getFileListByRId(oldFbId);
    SupplierFb supplierFb = supplierFbService.get(fbid);
    if (eflist != null && eflist.size() > 0) {
      for (ExtraFile ef : eflist) {
        ExtraFile efi = new ExtraFile();
        efi.setName(ef.getName());
        efi.setRelationName(ef.getRelationName());
        efi.setRelationId(supplierFb.getId());
        efi.setState(Constants.STATE_OK);
        efi.setType(ef.getType());
        efi.setUrl(ef.getUrl());
        efi.setCreateTime(System.currentTimeMillis());
        repository.save(efi);
      }
      repository.flush();
    }
  }

  @Override
  public List<ExtraFile> getFileListByRId(String relationId) {
    Assert.notEmpty(relationId);
    return CollUtil.emptyIfNull(dao.getFileListByRId(relationId));
  }
}
