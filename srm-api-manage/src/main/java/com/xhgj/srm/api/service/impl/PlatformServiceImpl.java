package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.xhgj.auth.service.MDMPersonService;
import com.xhgj.srm.api.dto.BidProjectDTO;
import com.xhgj.srm.api.dto.CustomPlatformModifiedDTO;
import com.xhgj.srm.api.dto.CustomPlatformModifiedDTO.BidProjectModifiedDTO;
import com.xhgj.srm.api.dto.CustomPlatformModifiedDTO.PlatformModifiedDTO;
import com.xhgj.srm.api.dto.PurchaseUserPageDTO;
import com.xhgj.srm.api.dto.platform.AddOrUpdateMenuParams;
import com.xhgj.srm.api.dto.platform.PlatformDTO;
import com.xhgj.srm.api.dto.platform.PlatformDetailsDTO;
import com.xhgj.srm.api.dto.platform.PlatformDetailsDTO.PlatformDetailsDTOBuilder;
import com.xhgj.srm.api.dto.platform.param.AbbreviationAndProjectNameUpd;
import com.xhgj.srm.api.dto.platform.param.AddParam;
import com.xhgj.srm.api.dto.platform.param.PlatformAbbreviationUpd;
import com.xhgj.srm.api.dto.platform.param.PlatformProjectNameUpd;
import com.xhgj.srm.api.service.MissionService;
import com.xhgj.srm.api.service.PlatformService;
import com.xhgj.srm.api.service.PlatformToMenuService;
import com.xhgj.srm.api.service.SupplierPerformanceService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.dao.BidProjectDao;
import com.xhgj.srm.jpa.dao.PlatformDao;
import com.xhgj.srm.jpa.entity.BidProject;
import com.xhgj.srm.jpa.entity.BidProjectToPlatform;
import com.xhgj.srm.jpa.entity.GrpPermissions;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.Platform;
import com.xhgj.srm.jpa.entity.PlatformToMenu;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.repository.BidProjectRepository;
import com.xhgj.srm.jpa.repository.BidProjectToPlatformRepository;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.repository.PermissionsRepository;
import com.xhgj.srm.jpa.repository.PlatformRepository;
import com.xhgj.srm.jpa.repository.PlatformToMenuRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import com.xhgj.srm.notice.service.PlatformRemindService;
import com.xhgj.srm.notice.vo.PlatformRemindConfigVO;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.config.BootConfig;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** Created by Geng Shy on 2023/10/18 */
@Service
public class PlatformServiceImpl implements PlatformService {

  @Resource private PlatformRepository repository;
  @Resource private PlatformDao dao;
  @Resource private UserService userService;
  @Resource private SupplierPerformanceService supplierPerformanceService;
  @Resource private PlatformToMenuRepository platformToMenuRepository;
  @Resource private PlatformToMenuService platformToMenuService;
  @Resource
  PlatformRemindService platformRemindService;
  @Resource
  UserRepository userRepository;
  @Resource
  private BootConfig bootConfig;
  @Resource
  private PermissionsRepository permissionsRepository;
  @Resource
  private BidProjectRepository bidProjectRepository;
  @Resource
  private BidProjectToPlatformRepository bidProjectToPlatformRepository;
  @Resource
  private MissionService missionService;
  @Resource
  private MissionRepository missionRepository;
  @Resource
  private MissionDispatcher missionDispatcher;
  @Resource
  private MDMPersonService mdmPersonService;
  @Resource
  private BidProjectDao bidProjectDao;

  @Override
  public BootBaseRepository<Platform, String> getRepository() {
    return repository;
  }

  @Override
  public List<PlatformDTO> getList(String code, String name,String platformAbbreviation) {
    List<PlatformDTO> result = new ArrayList<>();
    List<Platform> platforms = dao.findLikeByCodeAndName(code, name,platformAbbreviation);
    if (CollUtil.isEmpty(platforms)) {
      return result;
    }
    platforms.forEach(
        platform -> {
          String userName = userService.getNameById(platform.getDefaultPurchase());
          platform.setDefaultPurchase(userName);
          long countByPlatformCode =
              supplierPerformanceService.countByPlatformCode(platform.getCode());
          result.add(PlatformDTO.getInstance(platform, countByPlatformCode));
        });
    return result;
  }

  @Override
  public PlatformDetailsDTO getDetails(String id) {
    Optional<Platform> platformOptional = repository.findById(id);
    if (!platformOptional.isPresent()) {
      return new PlatformDetailsDTO();
    }
    Platform platform = platformOptional.get();
    PlatformDetailsDTOBuilder resultBuilder = PlatformDetailsDTO.builder();
    List<PlatformToMenu> platformToMenus =
        platformToMenuRepository.findAllByPlatformIdAndState(platform.getId(), Constants.STATE_OK);
    if (CollUtil.isNotEmpty(platformToMenus)) {
      List<String> menuIds =
          platformToMenus.stream().map(PlatformToMenu::getMenuId).collect(Collectors.toList());
      resultBuilder.permissions(menuIds);
    }
    List<PlatformRemindConfigVO> platformRemindConfig = platformRemindService.getPlatformRemindConfig(id);
    return resultBuilder.defaultPurchase(userService.getNameById(platform.getDefaultPurchase()))
        .projectName(platform.getProjectName())
        .projectManager(userService.getNameById(platform.getProjectManager()))
        .remindConfig(platformRemindConfig)
        .build();
  }

  @Override
  public boolean updatePurchase(String id, String purchaseUserId) {
    Optional<Platform> platformOptional = repository.findById(id);
    if (!platformOptional.isPresent()) {
      return false;
    }
    User user = userService.get(purchaseUserId);
    if (user == null) {
      return false;
    }
    Platform platform = platformOptional.get();
    platform.setDefaultPurchase(purchaseUserId);
    save(platform);
    return true;
  }

  @Override
  public PurchaseUserPageDTO getPurchase(String code) {
    Optional<Platform> platformOptional =
        repository.findFirstByCodeAndState(code, Constants.STATE_OK);
    if (!platformOptional.isPresent()
        || StrUtil.isBlank(platformOptional.get().getDefaultPurchase())) {
      return new PurchaseUserPageDTO();
    }
    String userId = platformOptional.get().getDefaultPurchase();
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    return new PurchaseUserPageDTO(user);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public boolean addOrUpdateMenu(AddOrUpdateMenuParams params) {
    if (StrUtil.isBlank(params.getPlatformId()) || CollUtil.isEmpty(params.getMenuIds())) {
      return false;
    }
    platformToMenuRepository.deleteAllByPlatformId(params.getPlatformId());
    platformToMenuRepository.flush();
    List<String> menuIds = params.getMenuIds();
    menuIds.forEach(
        menuId -> {
          saveMenu(params.getPlatformId(), menuId);
        });
    return true;
  }

  public void saveMenu(String platformId, String menuId) {
    if (StrUtil.isBlank(platformId) || StrUtil.isBlank(menuId)) {
      return;
    }
    PlatformToMenu platformToMenu = new PlatformToMenu();
    platformToMenu.setPlatformId(platformId);
    platformToMenu.setMenuId(menuId);
    platformToMenuRepository.save(platformToMenu);
  }

  @Override
  @Transactional
  public boolean add(AddParam param) {
    Optional<Platform> platformByCode =
        repository.findFirstByCodeAndState(param.getPlatformCode(), Constants.STATE_OK);
    Optional<Platform> platformByName =
        repository.findFirstByNameAndState(param.getPlatformName(), Constants.STATE_OK);
    if (platformByCode.isPresent() || platformByName.isPresent()) {
      throw new CheckException("平台编码或平台名称重复！");
    }

    Platform platform = new Platform();
    platform.setCode(param.getPlatformCode());
    platform.setName(param.getPlatformName());
    platform.setProjectName(param.getProjectName());
    platform.setCreateUser(param.getUserId());
    platform.setUpdateUser(param.getUserId());
    platform.setCreateTime(System.currentTimeMillis());
    platform.setUpdateTime(System.currentTimeMillis());
    platform.setState(Constants.STATE_OK);
    save(platform);
    // 平台新增默认开通所有菜单权限
    platformToMenuService.platformDefaultAuthorization(platform.getId());
    return true;
  }

  @Override
  public void updateAbbreviation(PlatformAbbreviationUpd param) {
    repository.findById(param.getId()).ifPresent(
        platform -> {
          if (!StrUtil.equals(platform.getPlatformAbbreviation(),param.getPlatformAbbreviation())) {
            repository.findFirstByplatformAbbreviationAndState(
                param.getPlatformAbbreviation(), Constants.STATE_OK).ifPresent(platformTemp -> {
              throw new CheckException("平台简称重复！");
            });
            platform.setPlatformAbbreviation(param.getPlatformAbbreviation());
            update(platform);
          }
        }
    );
  }

  /**
   * 根据平台编码获取平台信息
   * @param platform
   * @return
   */
  @Override
  public Platform findByCode(String platform) {
    return dao.findByCode(platform);
  }

  @Override
  public void updateProjectName(PlatformProjectNameUpd param, User user) {
    repository.findById(param.getId()).ifPresent(
        platform -> {
          platform.setProjectName(param.getProjectName());
          platform.setUpdateUser(user.getId());
          platform.setUpdateTime(System.currentTimeMillis());
          update(platform);
        }
    );
  }

  @Override
  @Transactional
  public void updateAbbreviationByProjectCategory(AbbreviationAndProjectNameUpd param, User user) {
    param.getList().stream().forEach(item -> {
      dao.updateAbbreviationByProjectCategory(item.getPlatformAbbreviation(), item.getProjectCategory(),
          user.getId());
    });
  }

  @Override
  public List<String> getProjectNamesByCodes(List<String> codes) {
    if (CollUtil.isEmpty(codes)) {
      return Collections.emptyList();
    }
    // fixme 老数据可能返回内容可能包含null或空值
    return dao.findProjectNamesByCodes(codes);
  }

  @Override
  public void updateProjectManager(String id, String pmUserId, User user) {
    Optional.ofNullable(repository.findById(id))
        .orElseThrow(() -> new CheckException("平台不存在！")).ifPresent(item -> {
          userService.get(pmUserId, () -> new CheckException("项目经理不存在！"));
          item.setProjectManager(pmUserId);
          item.setUpdateTime(System.currentTimeMillis());
          item.setUpdateUser(user.getId());
          update(item);
        });
  }

  @Override
  public List<PlatformDTO> searchProjectNames(String name) {
    // 过滤掉项目名称为空的数据，并且去重projectName
    List<Platform> platforms = dao.searchProjectNames(name, name);
    platforms = new ArrayList<>(platforms.stream().filter(item -> item.getProjectName() != null)
            .collect(Collectors.toMap(Platform::getProjectName, Function.identity(), (a, b) -> a))
            .values());
    return buildPlatformDTOList(platforms);
  }

  @Override
  public List<PlatformDTO> getListByProjectName(String projectName) {
    if (StrUtil.isBlank(projectName)) {
      return new ArrayList<>();
    }
    List<Platform> platforms = dao.getListByProjectName(projectName);
    return buildPlatformDTOList(platforms);
  }

  private List<PlatformDTO> buildPlatformDTOList(List<Platform> platforms) {
    if (CollUtil.isEmpty(platforms)) {
      return new ArrayList<>();
    }
    List<String> userIds =
        platforms.stream().map(Platform::getDefaultPurchase).distinct().filter(Objects::nonNull)
            .collect(Collectors.toList());
    List<String> platformCodes =
        platforms.stream().map(Platform::getCode).distinct().filter(Objects::nonNull)
            .collect(Collectors.toList());
    Map<String, Long> code2Count = supplierPerformanceService.countByPlatformCodeIn(platformCodes);

    userIds.add("-1");
    Map<String, User> id2User = userRepository.findAllById(userIds).stream()
        .collect(Collectors.toMap(User::getId, Function.identity(), (a, b) -> a));
    return platforms.stream().map(platform -> {
      User user = id2User.get(platform.getDefaultPurchase());
      String userName = user == null ? "" : user.getRealName();
      platform.setDefaultPurchase(userName);
      Long countByPlatformCode = code2Count.get(platform.getCode());
      return PlatformDTO.getInstance(platform, countByPlatformCode);
    }).collect(Collectors.toList());
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void handleCustomPlatformModified(CustomPlatformModifiedDTO modifiedDTO) {
    // 如果输入数据为空或平台编码为空，直接返回
    if (modifiedDTO == null || modifiedDTO.getPlatformModifiedDTO() == null || StrUtil.isBlank(
        modifiedDTO.getPlatformModifiedDTO().getPlatformCode())) {
      return;
    }
    if (!StrUtil.equals(modifiedDTO.getType(), Constants.PLATFORM_MODIFIED_TYPE) && !StrUtil.equals(
        modifiedDTO.getType(), Constants.PLATFORM_PROJECT_MODIFIED_ALL_TYPE)) {
      return;
    }
    PlatformModifiedDTO platformModifiedDTO = modifiedDTO.getPlatformModifiedDTO();
    Platform platform = repository.findFirstByCodeAndState(platformModifiedDTO.getPlatformCode(),
        Constants.STATE_OK).orElse(null);
    boolean isScp = StrUtil.equals(platformModifiedDTO.getToScp(), Constants.STATE_OK);
    //如果有SRM中已有的平台，同步时是否走SCP为N，开发群报错。
    // 报错文案包含环境、平台名称/编码、报错文案"SRM中已有的平台被更新为不走SCP，请核实具体情况
    if (platform != null && !isScp) {
      DingUtils.sendMsgByWarningRobot(
          "【" + bootConfig.getEnv() + "环境 " + bootConfig.getAppName()
              + "】SRM中已有的平台被更新为不走SCP，请核实具体情况【" + "平台编码："
              + platformModifiedDTO.getPlatformCode() + " 平台名称："
              + platformModifiedDTO.getPlatformName() + "】，异常信息【" + JSON.toJSONString(
              modifiedDTO) + "】，请及时处理", bootConfig.getEnv());
      return;
    }
    //仅获取“是否走SCP”为Y的数据
    if (!isScp) {
      return;
    }
    if (platform != null) {
      // 修改已有平台的基本信息
      updatePlatform(platform, platformModifiedDTO);
    } else {
      // 新增平台
      platform = new Platform();
      createNewPlatform(platform, platformModifiedDTO);
    }
    repository.save(platform);
    // 处理平台权限设置
    handlePlatformPermissions(platform.getId(), platformModifiedDTO);
    // 处理招投标项目清单
    if (StrUtil.equals(modifiedDTO.getType(), Constants.PLATFORM_PROJECT_MODIFIED_ALL_TYPE)) {
      handleBidProject(modifiedDTO, platform);
    }

  }

  // 处理招投标项目的新增或修改
  private void handleBidProject(CustomPlatformModifiedDTO modifiedDTO, Platform platform) {
    BidProjectModifiedDTO bidProjectModifiedDTO = modifiedDTO.getBidProjectModifiedDTO();
    // 查找招投标项目
    BidProject bidProject = bidProjectRepository.findFirstByProjectNameAndState(bidProjectModifiedDTO.getProjectName(), Constants.STATE_OK);
    if (bidProject != null) {
      // 更新招投标项目
      bidProject.setBatchYear(bidProjectModifiedDTO.getBatchYear());
      bidProject.setProjectName(bidProjectModifiedDTO.getProjectName());
      bidProject.setServiceStartTime(bidProjectModifiedDTO.getServiceStartTime());
      bidProject.setProjectManager(getPersonNamesByJobNos(bidProjectModifiedDTO.getProjectManagerJobNumber()));
      bidProject.setPerformanceCompany(getCompanyNamesByCodes(bidProjectModifiedDTO.getPerformanceCompanyCode()));
      bidProject.setCooperationCompany(getCompanyNamesByCodes(bidProjectModifiedDTO.getCooperationCompanyCode()));
      bidProject.setUpdateTime(System.currentTimeMillis());
    } else {
      // 新增招投标项目
      bidProject = new BidProject();
      bidProject.setBatchYear(bidProjectModifiedDTO.getBatchYear());
      bidProject.setProjectName(bidProjectModifiedDTO.getProjectName());
      bidProject.setServiceStartTime(bidProjectModifiedDTO.getServiceStartTime());
      bidProject.setProjectManager(getPersonNamesByJobNos(bidProjectModifiedDTO.getProjectManagerJobNumber()));
      bidProject.setPerformanceCompany(getCompanyNamesByCodes(bidProjectModifiedDTO.getPerformanceCompanyCode()));
      bidProject.setCooperationCompany(getCompanyNamesByCodes(bidProjectModifiedDTO.getCooperationCompanyCode()));
      bidProject.setCreateTime(System.currentTimeMillis());
      bidProject.setUpdateTime(System.currentTimeMillis());
      bidProject.setState(Constants.STATE_OK);
    }
    // 保存招投标项目
    bidProjectRepository.save(bidProject);
    // 如果平台和招投标项目尚未关联，则进行关联
    if (!bidProjectToPlatformRepository.existsByPlatformIdAndBidProjectId(platform.getId(), bidProject.getId())) {
      BidProjectToPlatform bidProjectToPlatform = new BidProjectToPlatform();
      bidProjectToPlatform.setBidProjectId(bidProject.getId());
      bidProjectToPlatform.setPlatformId(platform.getId());
      bidProjectToPlatformRepository.save(bidProjectToPlatform);
    }
  }


  // 处理平台的权限设置
  private void handlePlatformPermissions(String platformId, PlatformModifiedDTO platformModifiedDTO) {
    List<String> permissionNameList = ListUtil.toList("基本信息", "工商信息", "收款信息", "资质证照", "合同管理");
    // 根据权限配置动态添加权限项
    if (StrUtil.equals(platformModifiedDTO.getAuthProductManage(), Constants.STATE_OK)) {
      permissionNameList.addAll(ListUtil.of( "我的物料", "新增物料", "批量新增", "待提交物料", "我新增的品牌", "我的申请"));
    }
    if (StrUtil.equals(platformModifiedDTO.getAuthProductAreaStock(), Constants.STATE_OK)) {
      permissionNameList.addAll(ListUtil.of("库存管理"));
    }
    if (StrUtil.equals(platformModifiedDTO.getAuthOrderPerformance(), Constants.STATE_OK)) {
      permissionNameList.addAll(ListUtil.of("订单管理", "订单报备", "订单履约", "退货管理"));
    }
    if (StrUtil.equals(platformModifiedDTO.getAuthCustomerBill(), Constants.STATE_OK)) {
      permissionNameList.addAll(ListUtil.of("客户开票", "可开票订单", "开票申请"));
    }
    if (StrUtil.equals(platformModifiedDTO.getAuthRecordDrawTicket(), Constants.STATE_OK)) {
      permissionNameList.addAll(ListUtil.of("开票付款", "发票录入", "我的发票", "可付款订单", "付款申请"));
    }
    // 查找权限ID列表
    List<String> permissionIdList = permissionsRepository.findAllByNameInAndState(permissionNameList, Constants.STATE_OK)
        .stream().map(GrpPermissions::getId).collect(Collectors.toList());
    // 删除现有平台关联的权限数据
    platformToMenuRepository.deleteAllByPlatformId(platformId);
    platformToMenuRepository.flush();
    // 保存新的平台权限关联
    permissionIdList.forEach(menuId -> saveMenu(platformId, menuId));
  }


  // 更新已有平台的信息
  private void updatePlatform(Platform platform, PlatformModifiedDTO platformModifiedDTO) {
    platform.setProjectCategory(platformModifiedDTO.getProjectCategory());
    platform.setPlatformAbbreviation(platformModifiedDTO.getProjectAbbr());
    platform.setName(platformModifiedDTO.getPlatformName());
    platform.setCode(platformModifiedDTO.getPlatformCode());
    platform.setDockingMode(platformModifiedDTO.getDockingMode());
    String purchaserId = Optional.ofNullable(userService.getByCode(platformModifiedDTO.getPurchaserJobNumber()))
        .map(User::getId).orElse(StrUtil.EMPTY);
    platform.setDefaultPurchase(purchaserId);
    platform.setUpdateTime(System.currentTimeMillis());
  }

  // 创建一个新的平台
  private void createNewPlatform(Platform platform, PlatformModifiedDTO platformModifiedDTO) {
    platform.setProjectCategory(platformModifiedDTO.getProjectCategory());
    platform.setPlatformAbbreviation(platformModifiedDTO.getProjectAbbr());
    platform.setName(platformModifiedDTO.getPlatformName());
    platform.setCode(platformModifiedDTO.getPlatformCode());
    platform.setDockingMode(platformModifiedDTO.getDockingMode());
    String purchaserId = Optional.ofNullable(userService.getByCode(platformModifiedDTO.getPurchaserJobNumber()))
        .map(User::getId).orElse(StrUtil.EMPTY);
    platform.setDefaultPurchase(purchaserId);
    platform.setCreateTime(System.currentTimeMillis());
    platform.setUpdateTime(System.currentTimeMillis());
    platform.setState(Constants.STATE_OK);
  }

  @Override
  public void syncCustomerPlatformListByMDM(User user) {
    if (missionRepository.existsByTypeAndState(Constants.MISSION_TYPE_NAME_CUSTOMER_PLATFORM,
        Constants.MISSION_STATE_ING)) {
      throw new CheckException("有正在同步中的任务，无法执行");
    }
    Mission mission =
        missionService.createMission(
            user, Constants.MISSION_TYPE_NAME_CUSTOMER_PLATFORM, Constants.PLATFORM_TYPE_AFTER, null, null);
    Map<String, Object> mapParam = new HashMap<>(1);
    mapParam.put("userId", user.getId());
    missionDispatcher.doDispatch(
        mission.getId(),
        JSON.toJSONString(mapParam),
        MissionTypeEnum.BATCH_TASK_SYNC_CUSTOMER_PLATFORM_LIST);
  }

  public String getCompanyNamesByCodes(String companyCodes) {
    if (StrUtil.isBlank(companyCodes)) {
      return StrUtil.EMPTY;
    }
    StringJoiner sj = new StringJoiner("、");
    for (String orgCode : companyCodes.split(",")) {
      sj.add(mdmPersonService.getOrgByCode(orgCode).getName());
    }
    return sj.toString();
  }
  public String getPersonNamesByJobNos(String jobNos) {
    if (StrUtil.isBlank(jobNos)) {
      return StrUtil.EMPTY;
    }
    StringJoiner sj = new StringJoiner("、");
    for (String jobNo : jobNos.split(",")) {
      sj.add(mdmPersonService.getPersonByJobNo(jobNo).getName());
    }
    return sj.toString();
  }

  @Override
  public PageResult<BidProjectDTO> getBidProjectList(String platformId, Integer pageNo, Integer pageSize) {
    if (StrUtil.isBlank(platformId)) {
      return PageResult.empty(pageNo, pageSize);
    }
    Page<BidProject> page = bidProjectDao.getBidProjectPage(platformId, pageNo, pageSize);
    List<BidProjectDTO> pageDataList = new ArrayList<>();
    int totalPages = page.getTotalPages();
    if (!(pageNo > totalPages)) {
      List<BidProject> bidProjectList = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      pageDataList = CollUtil.emptyIfNull(bidProjectList).stream().map(BidProjectDTO::new).collect(Collectors.toList());
    }
    return new PageResult<>(pageDataList, page.getTotalElements(), totalPages, pageNo, pageSize);
  }

  @Override
  public List<PlatformDTO> searchProjectCategory(String projectCategory) {
    List<Platform> platforms = dao.searchProjectCategory(projectCategory);
    platforms = new ArrayList<>(platforms.stream().filter(item -> item.getProjectCategory() != null)
        .collect(Collectors.toMap(Platform::getProjectCategory, Function.identity(), (a, b) -> a))
        .values());
    return buildPlatformDTOList(platforms);
  }

  @Override
  public List<BidProjectDTO> searchProjectNameByProjectCategory(String projectCategory) {
    List<BidProject> bidProjectList =
        bidProjectDao.searchProjectNameByProjectCategory(projectCategory);
    List<BidProjectDTO> bidProjectDTOList =
        CollUtil.emptyIfNull(bidProjectList).stream().filter(item -> item.getProjectName() != null)
            .map(BidProjectDTO::new).collect(Collectors.toList());
    return bidProjectDTOList;
  }
}
