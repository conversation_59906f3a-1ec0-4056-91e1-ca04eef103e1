package com.xhgj.srm.api.dto.purchase.order;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.common.enums.PurchaseApplicationTypeEnum;
import com.xhgj.srm.common.enums.WarehouseEnum;
import com.xhgj.srm.jpa.entity.PurchaseApplyForOrder;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * Created by Geng Shy on 2023/12/11
 */
@Data
public class PurchaseApplyForOrderPageVO {
  @ApiModelProperty("id")
  private String id;
  @ApiModelProperty("申请单号")
  private String applyForOrderNo;
  @ApiModelProperty("申请类型")
  private String applyForType;
  @ApiModelProperty("订货状态")
  private String orderGoodsState;
  @ApiModelProperty("创建日期")
  private Long createTime;
  @ApiModelProperty("物料编码")
  private String productCode;
  @ApiModelProperty("仓库")
  private String warehouse;
  @ApiModelProperty("品牌")
  private String brand;
  @ApiModelProperty("物料名称")
  private String productName;
  @ApiModelProperty("规格型号")
  private String model;
  @ApiModelProperty("单位")
  private String unit;
  @ApiModelProperty("单位")
  private String unitCode;
  @ApiModelProperty("申请数量")
  private BigDecimal applyForNumber;
  @ApiModelProperty("已订货数量")
  private BigDecimal orderGoodsNumber;
  @ApiModelProperty("未订货数量")
  private String unorderedQuantity;
  @ApiModelProperty("计划需求日期")
  private Long planDemandDate;
  @ApiModelProperty("采购员")
  private String purchaseMan;
  @ApiModelProperty("售达方")
  private String soldToParty;
  @ApiModelProperty("销售订单号")
  private String saleOrderNo;
  @ApiModelProperty("采购部门")
  private String purchaseDepartment;
  @ApiModelProperty("取消状态")
  private String cancellationState;
  @ApiModelProperty("下推状态")
  private String pushDownState;
  @ApiModelProperty("序号")
  private Integer serialNumber;
  @ApiModelProperty("描述")
  private String description;
  @ApiModelProperty("销售单价")
  private BigDecimal salesUnitPrice;
  @ApiModelProperty("销售需求数量")
  private BigDecimal salesDemandQuantity;
  @ApiModelProperty("MPM参考结算价")
  private BigDecimal mpmReferenceSettlementPrice;
  @ApiModelProperty("业务员")
  private String salesman;
  @ApiModelProperty("发货方式")
  private String deliveryType;
  @ApiModelProperty("客户订单号")
  private String orderNo;
  @ApiModelProperty("跟单员")
  private String merchandiser;
  @ApiModelProperty("大票项目号")
  private String largeTicketProjectNumbers;
  @ApiModelProperty("大票项目名称")
  private String largeTicketProjectName;
  @ApiModelProperty("物料行备注")
  private String materialLineRemarks;
  @ApiModelProperty("收件地址")
  private String deliveryAddress;
  @ApiModelProperty("联系方式")
  private String contactInformation;

  @ApiModelProperty("收件人")
  private String consignee;

  @ApiModelProperty("申请单备注")
  private String applicationFormRemarks;

  @ApiModelProperty("项目编号")
  private String projectNo;

  @ApiModelProperty("打印次数")
  private Integer printNumber;

  @ApiModelProperty("销售订单行项目")
  private String saleOrderProductRowId;

  @ApiModelProperty("附件客户资料")
  private List<FileDTO> customerFileList;

  @ApiModelProperty("是否急单:Y/N")
  private String isWorryOrder;

  @ApiModelProperty("是否直发：1是，0否")
  private String directShipment;

  /**
   * 业务员所在公司
   */
  @ApiModelProperty("业务员所在公司")
  private String businessCompanyName;

  /**
   * 跟单员
   */
  @ApiModelProperty("跟单员")
  private String followUpPersonName;

  /**
   * 制单员
   */
  @ApiModelProperty("制单员")
  private String makeManName;

  /**
   * 项目名称
   */
  @ApiModelProperty("项目名称")
  private String projectName;

  private PurchaseApplyForOrderPageVO() {}

  public static PurchaseApplyForOrderPageVO getInstance(
      PurchaseApplyForOrder purchaseApplyForOrder) {
    Assert.notNull(purchaseApplyForOrder);
    PurchaseApplyForOrderPageVO vo = new PurchaseApplyForOrderPageVO();
    vo.businessCompanyName = purchaseApplyForOrder.getBusinessCompanyName();
    vo.followUpPersonName = purchaseApplyForOrder.getFollowUpPersonName();
    vo.makeManName = purchaseApplyForOrder.getMakeManName();
    vo.projectName = purchaseApplyForOrder.getProjectName();
    vo.id = purchaseApplyForOrder.getId();
    vo.applyForOrderNo = purchaseApplyForOrder.getApplyForOrderNo();
    vo.applyForType = purchaseApplyForOrder.getApplyForType();
    vo.orderGoodsState = purchaseApplyForOrder.getOrderGoodsState();
    vo.productCode = purchaseApplyForOrder.getProductCode();
    vo.createTime = purchaseApplyForOrder.getCreateTime();
    vo.warehouse = purchaseApplyForOrder.getWarehouse();
    vo.brand = purchaseApplyForOrder.getBrand();
    vo.productName = purchaseApplyForOrder.getProductName();
    vo.model = purchaseApplyForOrder.getModel();
    vo.unit = purchaseApplyForOrder.getUnitName();
    vo.unitCode = purchaseApplyForOrder.getUnit();
    vo.applyForNumber = purchaseApplyForOrder.getApplyForNumber();
    vo.planDemandDate = purchaseApplyForOrder.getPlanDemandDate();
    vo.purchaseMan = purchaseApplyForOrder.getPurchaseMan();
    vo.orderGoodsNumber = purchaseApplyForOrder.getOrderGoodsNumber();
    vo.unorderedQuantity = (purchaseApplyForOrder.getApplyForNumber() == null
        || purchaseApplyForOrder.getOrderGoodsNumber() == null) ? StrUtil.EMPTY
        : NumberUtil.sub(purchaseApplyForOrder.getApplyForNumber(),
            purchaseApplyForOrder.getOrderGoodsNumber()).stripTrailingZeros().toPlainString();
    vo.soldToParty = purchaseApplyForOrder.getSoldToParty();
    vo.saleOrderNo = purchaseApplyForOrder.getSaleOrderNo();
    vo.cancellationState = purchaseApplyForOrder.getCancellationState();
    vo.pushDownState = purchaseApplyForOrder.getPushDownState();
    vo.serialNumber = purchaseApplyForOrder.getSerialNumber();
    vo.description = StrUtil.emptyIfNull(purchaseApplyForOrder.getMaterialDescription());
    vo.salesUnitPrice = purchaseApplyForOrder.getSalesUnitPrice();
    vo.salesDemandQuantity = purchaseApplyForOrder.getSalesDemandQuantity();
    vo.mpmReferenceSettlementPrice = purchaseApplyForOrder.getMpmReferenceSettlementPrice();
    vo.salesman = purchaseApplyForOrder.getSalesman();
    vo.deliveryType = purchaseApplyForOrder.getDeliveryType();
    vo.orderNo = purchaseApplyForOrder.getCustomerOrderNumber();
    vo.merchandiser = purchaseApplyForOrder.getFollowUpPersonName();
    vo.largeTicketProjectName = purchaseApplyForOrder.getProjectName();
    vo.largeTicketProjectNumbers = purchaseApplyForOrder.getProjectNo();
    vo.materialLineRemarks = StrUtil.emptyIfNull(purchaseApplyForOrder.getMaterialLineRemarks());
    vo.deliveryAddress = purchaseApplyForOrder.getDeliveryAddress();
    vo.contactInformation = purchaseApplyForOrder.getContactInformation();
    vo.consignee = purchaseApplyForOrder.getConsignee();
    vo.applicationFormRemarks = purchaseApplyForOrder.getApplicationFormRemarks();
    vo.projectNo = purchaseApplyForOrder.getProjectNo();
    vo.printNumber =
        purchaseApplyForOrder.getPrintNumber() == null ? 0 : purchaseApplyForOrder.getPrintNumber();
    vo.saleOrderProductRowId = purchaseApplyForOrder.getSaleOrderProductRowId();
    vo.setIsWorryOrder(purchaseApplyForOrder.getIsWorryOrder());
    vo.setDirectShipment(purchaseApplyForOrder.getDirectShipment());
    return vo;
  }
}
