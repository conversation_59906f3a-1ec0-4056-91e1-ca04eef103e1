package com.xhgj.srm.api.dto.supplier;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ProvisionalSupplierParam {

  @ApiModelProperty("供应商id")
  private String supplierId;

  @ApiModelProperty("mdm编码")
  @NotBlank(message = "mdm编码必传！")
  private String mdmCode;

  @NotBlank(message = "企业名称必传！")
  @ApiModelProperty("企业名称")
  private String supplierName;
}
