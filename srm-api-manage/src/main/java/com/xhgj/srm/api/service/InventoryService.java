package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.InventoryDTO;
import com.xhgj.srm.dto.InventoryQueryForm;
import com.xhgj.srm.jpa.entity.Inventory;
import com.xhgj.srm.jpa.entity.User;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.List;
import java.util.Map;

/**
 * InventoryService
 */
public interface InventoryService extends BootBaseService<Inventory, String> {

  PageResult<InventoryDTO> getAllInventoryList(InventoryQueryForm param);

  List<InventoryDTO> getCurrentInventoryList(InventoryQueryForm param);

  void export(InventoryQueryForm param, User user);

  Long getExportCount(InventoryQueryForm param);

  Map<String,Long> getLastSyncTime();

  /**
   * 组装拆卸单 获取库存列表
   */
  void forceRefresh(List<InventoryDTO> list);
}
