package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xhgj.cloud.dock.provider.support.dto.DockSupplier;
import com.xhgj.cloud.dock.provider.support.dto.DockSupplier.DockSupplierBuilder;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.api.dto.FrontNormalSupplierDTO;
import com.xhgj.srm.api.dto.PurchaseUserVo;
import com.xhgj.srm.api.dto.SupplierBlackPageDataDTO;
import com.xhgj.srm.api.dto.SupplierDataInUserDTO;
import com.xhgj.srm.api.dto.supplier.SupplierPayTypeDataDTO;
import com.xhgj.srm.api.dto.supplierUser.SupplierDataInUserV2DTO;
import com.xhgj.srm.api.dto.SupplierInformation;
import com.xhgj.srm.api.dto.SupplierOrderDTO;
import com.xhgj.srm.api.dto.SupplierOrderPageDTO;
import com.xhgj.srm.api.dto.SupplierPerformanceDTO;
import com.xhgj.srm.api.dto.supplierUser.SupplierEOrderAcceptance;
import com.xhgj.srm.api.dto.supplierUser.SupplierEOrderPerformanceDetail;
import com.xhgj.srm.api.dto.supplierUser.SupplierOrderAcceptance;
import com.xhgj.srm.api.dto.supplierUser.SupplierUserMenuSaveForm;
import com.xhgj.srm.api.factory.MapStructFactory;
import com.xhgj.srm.api.service.SupplierChangeRecordService;
import com.xhgj.srm.api.service.SupplierPerformanceService;
import com.xhgj.srm.api.utils.ManageSecurityUtil;
import com.xhgj.srm.api.vo.EarliestPaymentTermVo;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhgj.srm.common.enums.supplierUser.SupplierChangeLogEnum;
import com.xhgj.srm.common.enums.supplierUser.SupplierUserSource;
import com.xhgj.srm.dto.bundle.LandingContractBundleDto;
import com.xhgj.srm.dto.supplier.SupplierChangeCreateForm;
import com.xhgj.srm.jpa.entity.Platform;
import com.xhgj.srm.jpa.entity.PurchaseOrderPaymentTerms;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.repository.LandingMerchantContractRepository;
import com.xhgj.srm.jpa.repository.PlatformRepository;
import com.xhgj.srm.jpa.repository.PurchaseOrderPaymentTermsRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierPerformanceRepository;
import com.xhgj.srm.jpa.repository.SupplierUserRepository;
import com.xhgj.srm.map.domain.IgnoreFieldContext;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDiscountDTO;
import com.xhgj.srm.request.dto.partner.PartnerIcpDTO;
import com.xhgj.srm.request.service.third.oms.OMSService;
import com.xhgj.srm.request.service.third.xhgj.XhgjDockRequest;
import com.xhgj.srm.request.vo.SupplierRateDetailVO;
import com.xhgj.srm.api.dto.SupplierTypeAndCountDTO;
import com.xhgj.srm.api.dto.SupplierUserDataDTO;
import com.xhgj.srm.api.dto.aborad.AboardUpdateDTO;
import com.xhgj.srm.api.dto.aborad.SupplierAboardQuery;
import com.xhgj.srm.api.dto.supplier.AbroadSupplierDTO;
import com.xhgj.srm.api.dto.supplier.BaseSupplierDTO;
import com.xhgj.srm.api.dto.supplier.BaseSupplierMainDataDTO;
import com.xhgj.srm.api.dto.supplier.ChinaSupplierDTO;
import com.xhgj.srm.api.dto.supplier.DeleteProvisionalSupplierParam;
import com.xhgj.srm.api.dto.supplier.InteriorSupplierPageDTO;
import com.xhgj.srm.api.dto.supplier.OpenOrderReceivePermissionCountDTO;
import com.xhgj.srm.api.dto.supplier.PersonSupplierDTO;
import com.xhgj.srm.api.dto.supplier.ProvisionalSupplierPageDTO;
import com.xhgj.srm.api.dto.supplier.ProvisionalSupplierPageQuery;
import com.xhgj.srm.api.dto.supplier.ProvisionalSupplierParam;
import com.xhgj.srm.api.dto.supplier.SupplierAccountAndOrderReceivingInfo;
import com.xhgj.srm.api.dto.supplier.SupplierChinaQuery;
import com.xhgj.srm.api.dto.supplier.SupplierExportParam;
import com.xhgj.srm.api.dto.supplier.SupplierMainDataAbroadDTO;
import com.xhgj.srm.api.dto.supplier.SupplierMainDataChinaDTO;
import com.xhgj.srm.api.dto.supplier.SupplierMainDataPersonDTO;
import com.xhgj.srm.api.dto.supplier.SupplierPersonQuery;
import com.xhgj.srm.api.dto.supplier.UpdatePartnerDTO;
import com.xhgj.srm.api.dto.supplier.change.SupplerChangeInfoDTO;
import com.xhgj.srm.api.dto.supplier.search.BaseSupplierSearchResult;
import com.xhgj.srm.api.dto.workbench.MySupplierData;
import com.xhgj.srm.api.enums.SupplierRelativeKeyType;
import com.xhgj.srm.api.service.AssessService;
import com.xhgj.srm.api.service.BrandService;
import com.xhgj.srm.api.service.ChangeRecordService;
import com.xhgj.srm.api.service.ContractService;
import com.xhgj.srm.api.service.EntryRegistrationLandingMerchantService;
import com.xhgj.srm.api.service.EntryRegistrationService;
import com.xhgj.srm.api.service.FileService;
import com.xhgj.srm.api.service.FinancialService;
import com.xhgj.srm.api.service.GroupService;
import com.xhgj.srm.api.service.LandingMerchantContractService;
import com.xhgj.srm.api.service.MissionService;
import com.xhgj.srm.api.service.PlatformService;
import com.xhgj.srm.api.service.SearchSchemeService;
import com.xhgj.srm.api.service.SupplierInGroupService;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.api.service.SupplierTempService;
import com.xhgj.srm.api.service.SupplierTemplateService;
import com.xhgj.srm.api.service.SupplierToMenuService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.api.service.XhgjService;
import com.xhgj.srm.util.ImportExcelUtil;
import com.xhgj.srm.api.utils.ManageHttpUtil;
import com.xhgj.srm.api.utils.SupplierInGroupUtil;
import com.xhgj.srm.api.utils.TemplateUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.SupplierAccountAndOrderReceivingQuery;
import com.xhgj.srm.common.enums.AssessStateEnum;
import com.xhgj.srm.common.enums.AssessTypeEnum;
import com.xhgj.srm.common.enums.FileReviewStateEnum;
import com.xhgj.srm.common.enums.PayTypeSAPEnums;
import com.xhgj.srm.common.enums.ProvinceSAPEnum;
import com.xhgj.srm.common.enums.SupplierBlockRangeEnum;
import com.xhgj.srm.common.enums.SupplierTemplateTypeEnum;
import com.xhgj.srm.common.enums.entryregistration.EntryRegistrationDiscountTypeEnum;
import com.xhgj.srm.common.utils.CommonlyUseUtil;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.common.utils.MissionUtil;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.dto.financial.FinancialDTO;
import com.xhgj.srm.jpa.dao.BrandDao;
import com.xhgj.srm.jpa.dao.ContactDao;
import com.xhgj.srm.jpa.dao.ExtraFileDao;
import com.xhgj.srm.jpa.dao.FileDao;
import com.xhgj.srm.jpa.dao.FinancialDao;
import com.xhgj.srm.jpa.dao.GroupDao;
import com.xhgj.srm.jpa.dao.MissionDao;
import com.xhgj.srm.jpa.dao.OrderDao;
import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.dao.SupplierChangeInfoDao;
import com.xhgj.srm.jpa.dao.SupplierDao;
import com.xhgj.srm.jpa.dao.SupplierInGroupDao;
import com.xhgj.srm.jpa.dao.SupplierPerformanceDao;
import com.xhgj.srm.jpa.dao.SupplierUserDao;
import com.xhgj.srm.jpa.dao.UserCheckDao;
import com.xhgj.srm.jpa.dao.UserDao;
import com.xhgj.srm.jpa.entity.Assess;
import com.xhgj.srm.jpa.entity.BaseSupplier;
import com.xhgj.srm.jpa.entity.EntryRegistrationDiscount;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.entity.EntryRegistrationOrder;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.Financial;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.LandingMerchantContract;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierBizInfo;
import com.xhgj.srm.jpa.entity.SupplierChangeInfo;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.SupplierInGroupTemp;
import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhgj.srm.jpa.entity.SupplierTemp;
import com.xhgj.srm.jpa.entity.SupplierToMenu;
import com.xhgj.srm.jpa.entity.SupplierUser;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.UserCheck;
import com.xhgj.srm.jpa.repository.EntryRegistrationDiscountRepository;
import com.xhgj.srm.jpa.repository.EntryRegistrationLandingMerchantRepository;
import com.xhgj.srm.jpa.repository.FinancialRepository;
import com.xhgj.srm.jpa.repository.MissionRepository;
import com.xhgj.srm.jpa.repository.SupplierBizInfoRepository;
import com.xhgj.srm.jpa.repository.SupplierInGroupRepository;
import com.xhgj.srm.jpa.repository.SupplierRepository;
import com.xhgj.srm.jpa.repository.UserRepository;
import com.xhgj.srm.mission.common.MissionTypeEnum;
import com.xhgj.srm.mission.dispatcher.MissionDispatcher;
import com.xhgj.srm.registration.dto.entryregistration.EntryRegistrationDiscountDetailDTO;
import com.xhgj.srm.registration.entity.EntryRegistrationEntity;
import com.xhgj.srm.registration.repository.EntryRegistrationRepository;
import com.xhgj.srm.request.dto.mdm.InternalPartnerContentDTO;
import com.xhgj.srm.request.dto.mdm.InternalPartnerPageDTO;
import com.xhgj.srm.request.dto.partner.BusinessInfoDTO;
import com.xhgj.srm.request.dto.partner.PartnerDTO;
import com.xhgj.srm.request.dto.partner.SupplierFileDTO;
import com.xhgj.srm.request.service.third.erp.sap.SapSupplierRequest;
import com.xhgj.srm.request.service.third.mdm.MdmRequest;
import com.xhgj.srm.sender.mq.sender.BatchTaskMqSender;
import com.xhgj.srm.service.LandingContractBundleService;
import com.xhgj.srm.service.ShareEntryRegistrationService;
import com.xhgj.srm.service.ShareSupplierInGroupService;
import com.xhgj.srm.vo.record.SupplierChangeRecordVo.DayRecord;
import com.xhgj.srm.vo.record.SupplierChangeRecordVo.SwitchRecord;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.core.common.util.dict.BootDictEnumUtil;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Service
public class SupplierServiceImpl implements SupplierService {

  @Autowired private SupplierDao dao;

  @Autowired private UserDao userDao;

  @Autowired private BrandDao brandDao;

  @Autowired private ContactDao contactDao;

  @Autowired private FileDao fileDao;

  @Autowired private ExtraFileDao extraFileDao;

  @Autowired private FinancialDao financialDao;

  @Autowired private UserCheckDao userCheckDao;

  @Autowired private SupplierUserDao supplierUserDao;
  @Autowired private GroupDao groupDao;

  @Autowired private UserService userService;

  @Autowired private BrandService brandService;

  @Autowired private FinancialService financialService;

  @Autowired private SupplierRepository supplierRepository;

  @Autowired private FinancialRepository financialRepository;

  @Autowired private ManageHttpUtil httpUtil;

  @Autowired private SearchSchemeDao searchSchemeDao;

  @Autowired private ExportUtil ex;

  @Autowired private UserRepository userRepository;

  @Autowired private MissionUtil missionUtil;
  @Autowired private MissionDao missionDao;
  @Autowired private MissionRepository missionRepository;
  @Autowired private BatchTaskMqSender batchTaskMqSender;

  @Autowired private GroupService groupService;

  @Autowired private SupplierInGroupService supplierInGroupService;

  @Autowired private ContractService contractService;
  @Autowired private SupplierChangeInfoDao supplierChangeInfoDao;

  @Autowired private MissionService missionService;

  @Autowired private ImportExcelUtil importExcelUtil;
  @Autowired private AssessService assessService;
  @Autowired private SupplierTempService supplierTempService;
  @Autowired private SupplierTemplateService supplierTemplateService;
  @Autowired private SupplierInGroupRepository supplierInGroupRepository;
  @Autowired private SupplierBizInfoRepository bizInfoRepository;
  @Autowired private SupplierInGroupDao supplierInGroupDao;
  @Autowired private XhgjService xhgjService;
  @Autowired private ChangeRecordService changeRecordService;
  @Autowired private SearchSchemeService searchSchemeService;
  @Autowired private SupplierPerformanceDao supplierPerformanceDao;
  @Resource private EntryRegistrationService entryRegistrationService;
  @Resource private EntryRegistrationLandingMerchantService entryRegistrationLandingMerchantService;
  @Resource private PlatformService platformService;
  @Resource FileService fileService;
  private final String baseUrl;
  @Autowired private OrderDao orderDao;
  @Autowired private HttpUtil thirdHttpUtil;
  @Autowired private LandingMerchantContractService landingMerchantContractService;
  @Resource private SupplierToMenuService supplierToMenuService;
  @Resource private SupplierInGroupUtil supplierInGroupUtil;
  @Autowired private MdmRequest mdmRequest;
  @Resource private SapSupplierRequest sapSupplierRequest;

  @Resource private EntryRegistrationLandingMerchantRepository entryRegistrationLandingMerchantRepository;

  @Resource
  EntryRegistrationRepository entryRegistrationRepository;
  @Resource
  ShareSupplierInGroupService shareSupplierInGroupService;
  @Resource
  OMSService omsService;

  @Autowired private EntryRegistrationDiscountRepository entryRegistrationDiscountRepository;
  @Autowired private MissionDispatcher missionDispatcher;
  @Resource
  private ShareEntryRegistrationService shareEntryRegistrationService;
  @Resource
  private XhgjDockRequest xhgjDockRequest;
  @Resource
  SupplierUserRepository supplierUserRepository;
  @Resource
  SupplierPerformanceService supplierPerformanceService;
  @Resource
  SupplierPerformanceRepository supplierPerformanceRepository;
  @Resource
  ManageSecurityUtil manageSecurityUtil;
  @Resource
  LandingContractBundleService landingContractBundleService;
  @Resource
  LandingMerchantContractRepository landingMerchantContractRepository;
  @Resource
  SupplierChangeRecordService supplierChangeRecordService;
  @Resource
  PlatformRepository platformRepository;
  @Resource
  private SupplierOrderRepository supplierOrderRepository;
  @Resource
  private PurchaseOrderPaymentTermsRepository purchaseOrderPaymentTermsRepository;
  public SupplierServiceImpl(SrmConfig srmConfig) {
    this.baseUrl = srmConfig.getUploadUrl();
  }

  @Override
  public BootBaseRepository<Supplier, String> getRepository() {
    return supplierRepository;
  }


  @Override
  public void updateERPInfo(String supplierId, String erpId, String erpCode) {
    Supplier sup =
        supplierRepository
            .findById(supplierId)
            .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    if (!StringUtils.isNullOrEmpty(erpId)) {
      sup.setErpid(erpId);
    }
    if (!StringUtils.isNullOrEmpty(erpCode)) {
      sup.setErpCode(erpCode);
    }
    if (StringUtils.isNullOrEmpty(sup.getErpSuccess())) {
      sup.setErpSuccess(Constants.YES);
      sup.setSynState(Constants.SUPPLIER_SYN_STATE_SUCCESS);
    }
    supplierRepository.saveAndFlush(sup);
  }

  @Override
  public void pushToErp(String supplierIds, String type) {
    if (!Constants.SUPPLIERTYPE_CHINA.equals(type) && !Constants.SUPPLIERTYPE_ABROAD.equals(type)) {
      throw new CheckException("类型有误,不能推送至 ERP");
    }
    String result = "";
    if (!StringUtils.isNullOrEmpty(supplierIds)) {
      String[] suppliers = supplierIds.split(",");
      for (String id : suppliers) {
        Supplier sup =
            supplierRepository
                .findById(id)
                .orElseThrow(() -> CheckException.noFindException(Supplier.class, id));
        if (sup != null && Constants.COMMONSTATE_OK.equals(sup.getState())) {
          String mes = "";
          if (Constants.SUPPLIER_SYN_STATE_SUCCESS.equals(sup.getSynState())
              || (Constants.SUPPLIER_SYN_STATE_FALSE.equals(sup.getSynState())
                  && !StringUtils.isNullOrEmpty(sup.getErpCode())
                  && !StringUtils.isNullOrEmpty(sup.getErpid()))) {
            mes = synSupplierToErp(id, "update", type);
          } else if (Constants.SUPPLIER_SYN_STATE_NOERPCODE.equals(sup.getSynState())
              || (Constants.SUPPLIER_SYN_STATE_FALSE.equals(sup.getSynState())
                  && StringUtils.isNullOrEmpty(sup.getErpCode())
                  && StringUtils.isNullOrEmpty(sup.getErpid()))) {
            mes = synSupplierToErp(id, "add", type);
          }
          if (!StringUtils.isNullOrEmpty(mes)) {
            result = result + "[" + sup.getEnterpriseName() + " " + mes + "]";
          }
        }
      }
    }
    if (!StringUtils.isNullOrEmpty(result)) {
      throw new CheckException(result);
    }
  }

  @Transactional
  @Override
  public String synSupplierToErp(String supplierId, String type, String resource) {
    String resMsg = "";
    JSONObject erpJson = httpUtil.synSupplierToErp(supplierId, type, resource);
    if (erpJson == null) {
      resMsg = "同步失败";
    }
    log.info("同步供应商返回信息:" + erpJson.toString());
    String recode = erpJson.getString("code");
    if (!"0".equals(recode)) {
      if ("1".equals(recode)) {
        String msg = erpJson.getString("msg");
        if (msg.contains("名称不唯一")) {
          resMsg = "同步失败,名称不唯一";
        } else {
          resMsg = msg;
        }
      }
    } else {
      Supplier supplier = get(supplierId);
      if (erpJson.get("data") != null && "add".equals(type)) {
        JSONObject dataJson = erpJson.getJSONObject("data");
        String id = dataJson.getString("erpid");
        String erpCode = dataJson.getString("erpcode");
        supplier.setErpid(id);
        supplier.setErpCode(erpCode);
        supplier.setErpSuccess(Constants.YES);
        supplier.setSynState(Constants.SUPPLIER_SYN_STATE_SUCCESS);
        supplierRepository.save(supplier);
      } else if (erpJson.get("data") != null) {
        supplier.setErpSuccess(Constants.YES);
        supplier.setSynState(Constants.SUPPLIER_SYN_STATE_SUCCESS);
        supplierRepository.save(supplier);
      }
    }
    return resMsg;
  }

  @Override
  public PageResult<SupplierBlackPageDataDTO> getSupplierBlackPage(
      String mdmCode,
      String enterpriseName,
      String enterpriseLevel,
      String useGroup,
      String enterpriseNature,
      String industry,
      String purchaserName,
      String brands,
      String mobile,
      String contacts,
      String schemeId,
      String userId,
      int pageNo,
      int pageSize) {
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeDao.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_SUPPLIER_BLACK);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeDao.get(schemeId);
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          mdmCode = StrUtil.blankToDefault(mdmCode ,searchJo.containsKey("mdmCode") ?
              searchJo.getString("mdmCode") : "");
          enterpriseName =
              StrUtil.blankToDefault(enterpriseName,searchJo.containsKey("enterpriseName") ?
                  searchJo.getString("enterpriseName") : "");
          enterpriseNature =
              StrUtil.blankToDefault( enterpriseNature,searchJo.containsKey("enterpriseNature")
                  ? searchJo.getString("enterpriseNature")
                  : "");
          industry = StrUtil.blankToDefault(industry,searchJo.containsKey("industry") ?
              searchJo.getString("industry") : "");
          brands = StrUtil.blankToDefault(brands,searchJo.containsKey("brands") ?
              searchJo.getString("brands") : "");
          mobile = StrUtil.blankToDefault(mobile,searchJo.containsKey("mobile") ?
              searchJo.getString("mobile") : "");
          contacts = StrUtil.blankToDefault(contacts,searchJo.containsKey("contacts") ?
              searchJo.getString("contacts") : "");
          purchaserName =
              StrUtil.blankToDefault( purchaserName,searchJo.containsKey("purchaserName") ?
                  searchJo.getString("purchaserName") : "");
          enterpriseLevel =
              StrUtil.blankToDefault(enterpriseLevel, searchJo.containsKey("enterpriseLevel") ?
                  searchJo.getString("enterpriseLevel") : "");
        }
      }
    }
    User user = null;
    if (!StringUtils.isNullOrEmpty(userId)) {
      user = userService.get(userId);
    }
    if (user != null && user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      useGroup = "";
    }
    Page<String> page =
        dao.getSupplierBlackPage(
            mdmCode,
            enterpriseName,
            enterpriseLevel,
            useGroup,
            enterpriseNature,
            industry,
            purchaserName,
            brands,
            mobile,
            contacts,
            pageNo,
            pageSize);
    List<SupplierBlackPageDataDTO> dataList = new ArrayList<>();
    int totalPages = page.getTotalPages();
    if (pageNo <= totalPages) {
      List<String> list = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      list.forEach(
          id -> {
            SupplierInGroup supplierInGroup = supplierInGroupService.get(id);
            SupplierBlackPageDataDTO data = new SupplierBlackPageDataDTO(supplierInGroup);
            String bUserId = supplierInGroup.getShieldingPeople();
            if (!StringUtils.isNullOrEmpty(bUserId)) {
              User inUser =
                  userService.get(
                      bUserId, () -> CheckException.noFindException(User.class, bUserId));
              data.setShieldingPeople(inUser.getRealName());
            }
            dataList.add(data);
          });
    }
    return new PageResult<>(dataList, page.getTotalElements(), totalPages, pageNo, pageSize);
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void blockSupplierById(
      Group group, String supplierInGroupId, User user, String reason, String range) {
    Assert.notNull(group, "groupCode为空");
    Assert.notEmpty(supplierInGroupId, "supplierIds为空");
    Assert.notEmpty(reason, "拉黑原因为空");
    SupplierInGroup supplierInGroup = supplierInGroupService.get(supplierInGroupId);
    SupplierBlockRangeEnum supplierBlockRangeEnum = SupplierBlockRangeEnum.fromKey(range);
    if (supplierBlockRangeEnum == null) {
      throw new CheckException("拉黑范围类型错误！");
    }
    if (supplierInGroup == null) {
      throw new CheckException("不存在的供应商");
    }
    if (!userService.hasOperatePermission(
        user.getId(), supplierInGroup.getPurchaserId(), Constants.USER_PERMISSION_BLOCK_SUPPLIER)) {
      throw new CheckException("没有权限拉黑该供应商");
    }
    if (!Constants.AUDIT_STATE_MANAGESUCCESS.equals(supplierInGroup.getAuditState())
        && !StringUtils.isNullOrEmpty(supplierInGroup.getAuditState())) {
      throw new CheckException("当前供应商存在审核暂时无法拉黑");
    }
    Assess assess =
        assessService.findFirstByTargetIdAndAssessTypeAssessStateAndGroupId(
            supplierInGroupId,
            AssessTypeEnum.SUPPLIER_BLOCK.getKey(),
            AssessStateEnum.UN_ASSESS.getKey(),
            group.getId());
    if (assess != null) {
      throw new CheckException("此供应商正在拉黑中，无法继续拉黑");
    }
    // 设置审核人
    UserCheck userCheck =
        userCheckDao.getUserCheckByUser(
            user.getId(), Constants.USERCHECKTYPE_SHIELD, supplierInGroup.getEnterpriseLevel());
    supplierInGroup.setReason(reason);
    supplierInGroup.setBlockRange(supplierBlockRangeEnum.getKey());
    supplierInGroup.setUpdateTime(System.currentTimeMillis());
    supplierInGroup.setShieldingPeople(user.getId());
    if (userCheck != null) {
      User checkUser = userDao.getUserByErpCode(userCheck.getUserErpCode());
      if (checkUser != null) {
        supplierInGroup.setShieldManager(checkUser.getId());
        supplierInGroupService.save(supplierInGroup);
        assessService.createSupplierBlockAssess(
            supplierInGroupId, checkUser.getId(), user.getId(), group, supplierBlockRangeEnum);
      } else {
        makeSupplierBlock(supplierInGroupId, supplierBlockRangeEnum);
      }
    } else {
      makeSupplierBlock(supplierInGroupId, supplierBlockRangeEnum);
    }
    // 修改erp中供应商状态(暂不启用)
    // supplierService.pushSupplierStateToErp(supplier.getId());
  }

  @Override
  @Transactional
  public void makeSupplierBlock(String supplierInGroupId, SupplierBlockRangeEnum rangeEnum) {
    SupplierInGroup supplierInGroup = supplierInGroupService.get(supplierInGroupId);
    if (supplierInGroup == null) {
      throw new CheckException("不存在的供应商");
    }
    if (rangeEnum == null) {
      throw new CheckException("拉黑范围非法");
    }
    supplierInGroup.setState(Constants.COMMONSTATE_BLACKLIST);
    supplierInGroup.setBlockRange(rangeEnum.getKey());
    supplierInGroupService.save(supplierInGroup);
    Supplier supplier = supplierInGroup.getSupplier();
    Group group = supplierInGroup.getGroup();
    if (supplier == null
        || group == null
        || StrUtil.isBlank(supplier.getMdmCode())
        || StrUtil.isBlank(group.getErpCode())) {
      throw new CheckException("数据异常，请联系管理员");
    }
    boolean blacklistingSuppliers;
    try {
      blacklistingSuppliers =
          sapSupplierRequest.blacklistingSuppliers(
              supplier.getMdmCode(), group.getErpCode(), rangeEnum);
    } catch (CheckException e){
      throw e;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e));
      throw new CheckException("SAP系统响应异常，拉黑失败");
    }
    if (Boolean.FALSE.equals(blacklistingSuppliers)) {
      throw new CheckException("SAP系统响应拉黑失败，请联系管理员");
    }
  }

  @Override
  public void updateSupplierLevel(String supplierInGroupId, String level) {
    SupplierInGroup supplier = supplierInGroupRepository.findById(supplierInGroupId).orElse(null);
    if (supplier == null) {
      throw new CheckException("不存在的供应商");
    }
    supplier.setEnterpriseLevel(level);
    supplierInGroupService.save(supplier);
  }

  @Override
  public void pushSupplierStateToErp(String supplierId) {
    SupplierInGroup supplier =
        supplierInGroupService.get(
            supplierId, () -> CheckException.noFindException(SupplierInGroup.class, supplierId));
    String erpId = supplier.getErpId();
    if (StringUtils.isNullOrEmpty(erpId)) {
      throw new CheckException("该供应商 erpId 为空，不能推送至 ERP。");
    }
    String state = supplier.getState();
    // 获取状态对应的 ERP 操作类型
    String operationType = Constants.STATE_TO_ERP_OPERATION_TYPE_MAP.get(state);
    if (StringUtils.isNullOrEmpty(operationType)) {
      throw new CheckException("未识别的供应商状态：【" + state + "】");
    }
    JSONObject jo = httpUtil.delOrBanSupplier(erpId, operationType);
    if (jo != null) {
      log.info(jo.toString());
      String code = jo.get("code").toString();
      if (!"0".equals(code)) {
        throw new CheckException("操作成功,同步erp失败");
      }
    } else {
      throw new CheckException("操作成功,同步erp失败");
    }
  }

  @Override
  public Supplier updateAboard(AboardUpdateDTO updateParam) {
    String supplierId = updateParam.getSupplierId();
    Supplier sup =
        supplierRepository
            .findById(supplierId)
            .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    updateParam.updateAboard(sup);
    supplierRepository.save(sup);
    // 删除原有财务信息
    financialService.deleteFinancialInSupplier(sup.getId());
    // 存入
    List<FinancialDTO> financialDTOS = updateParam.getFinancial();
    if (financialDTOS != null && financialDTOS.size() > 0) {
      financialService.addFinancial(financialDTOS, sup);
    }
    String type = "update";
    // 同步供应商信息至erp
    if (Constants.NO.equals(sup.getErpSuccess())
        || StringUtils.isNullOrEmpty(sup.getErpSuccess())) {
      type = "add";
    }
    JSONObject jo = httpUtil.synSupplierToErp(sup.getId(), type, Constants.SUPPLIERTYPE_ABROAD);
    if (jo == null) {
      throw new CheckException("操作成功,同步erp失败");
    }
    log.info("[===供应商同步至erp返回参数====]:" + jo.toString());
    String code = jo.getString("code");
    if (!"0".equals(code)) {
      if ("1".equals(code)) {
        String msg = jo.getString("msg");
        if (msg.contains("名称不唯一")) {
          throw new CheckException("供应商在erp已存在，请联系管理员");
        }
        throw new CheckException("操作成功,同步erp失败");
      }
    } else {
      if (jo.get("data") != null && "add".equals(type)) {
        JSONObject jsonData = jo.getJSONObject("data");
        String id = jsonData.getString("erpid");
        String erpCode = jsonData.getString("erpcode");
        Supplier newsup = dao.getSupplierById(sup.getId());
        newsup.setErpid(id);
        newsup.setErpCode(erpCode);
        newsup.setErpSuccess(Constants.YES);
        newsup.setSynState(Constants.SUPPLIER_SYN_STATE_SUCCESS);
        supplierRepository.saveAndFlush(newsup);
      }
    }
    return sup;
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void blockAboardById(String userId, String supplierInGroupId) {
    if (StringUtils.isNullOrEmpty(supplierInGroupId)) {
      throw new CheckException("接口请求有误");
    }
    if (StringUtils.isNullOrEmpty(userId)) {
      throw new CheckException("接口请求有误");
    }
    User user;
    SupplierInGroup supplier =
        supplierInGroupService.get(
            supplierInGroupId,
            () -> CheckException.noFindException(SupplierInGroup.class, supplierInGroupId));
    user = userService.get(userId);
    if (user != null) {
      supplier.setShieldingPeople(user.getId());
    }
    supplier.setState(Constants.COMMONSTATE_LOCK);
    supplierInGroupService.save(supplier);
    if (!StringUtils.isNullOrEmpty(supplier.getErpId())) {
      JSONObject jo = httpUtil.delOrBanSupplier(supplier.getErpId(), "banYes");
      if (jo != null) {
        log.info("[===供应商禁启用至erp返回参数====]:" + jo.toString());
        String code = jo.get("code").toString();
        if (!"0".equals(code)) {
          throw new CheckException("操作成功,同步erp失败");
        }
      } else {
        throw new CheckException("操作成功,同步erp失败");
      }
    }
  }

  @Override
  public void onlineAboardById(String supplierInGroupId) {
    if (StringUtils.isNullOrEmpty(supplierInGroupId)) {
      throw new CheckException("接口请求有误");
    }
    SupplierInGroup supplier =
        supplierInGroupService.get(
            supplierInGroupId,
            () -> CheckException.noFindException(SupplierInGroup.class, supplierInGroupId));
    supplier.setState(Constants.COMMONSTATE_OK);
    supplierInGroupService.save(supplier);
    if (!StringUtils.isNullOrEmpty(supplier.getErpId())) {
      JSONObject jo = httpUtil.delOrBanSupplier(supplier.getErpId(), "banNo");
      if (jo != null) {
        log.info("[===供应商禁启用至erp返回参数====]:" + jo.toString());
        String code = jo.get("code").toString();
        if (!"0".equals(code)) {
          throw new CheckException("操作成功,同步erp失败");
        }
      } else {
        throw new CheckException("操作成功,同步erp失败");
      }
    }
  }

  @Override
  public PageResult<FrontNormalSupplierDTO> getNormalSupplierPage(
      String userId,
      String createCode,
      String enterpriseName,
      String mdmCode,
      String schemeId,
      int pageNo,
      int pageSize) {
    // 查询方案
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeDao.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_SUPPLIER_USER);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeDao.get(schemeId);
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          mdmCode = StrUtil.blankToDefault(mdmCode,searchJo.containsKey("mdmCode") ?
              searchJo.getString(
              "mdmCode") : "");
          enterpriseName =
              StrUtil.blankToDefault(enterpriseName,searchJo.containsKey("enterpriseName") ?
                  searchJo.getString(
                  "enterpriseName") : "");
        }
      }
    }
    User user;
    if (!StringUtils.isNullOrEmpty(userId)) {
      user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
      if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)
          || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
        createCode = "";
      }
    }
    return PageResultBuilder.buildPageResult(
        dao.getFrontNormalSupplierPage(
            userId, createCode, enterpriseName, mdmCode, pageNo, pageSize),
        (objects) -> {
          FrontNormalSupplierDTO frontNormalSupplierDTO = new FrontNormalSupplierDTO();
          frontNormalSupplierDTO.setId((String) objects[0]);
          frontNormalSupplierDTO.setEnterName((String) objects[1]);
          frontNormalSupplierDTO.setMdmCode((String) objects[2]);
          frontNormalSupplierDTO.setAccountNum(
              Long.parseLong(String.valueOf(ObjectUtil.defaultIfNull(objects[3], "0"))));
          return frontNormalSupplierDTO;
        });
  }


  @Override
  @Deprecated
  public SupplierDataInUserDTO getSupplierUserList(String supplierId) {
    if (StringUtils.isNullOrEmpty(supplierId)) {
      throw new CheckException("接口请求有误");
    }
    Supplier supplier =
        supplierRepository
            .findById(supplierId)
            .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    List<SupplierUser> supplierUsers = supplierUserDao.getSupplierUserListBySid(supplierId);
    List<SupplierToMenu> menus = supplierToMenuService.findAllBySupplierId(supplier.getId());
    List<String> menuIds = new ArrayList<>();
    if (CollUtil.isNotEmpty(menus)) {
      menuIds = menus.stream().map(SupplierToMenu::getMenuId).collect(Collectors.toList());
    }
    SupplierDataInUserDTO supplierDataInUserDTO = new SupplierDataInUserDTO(supplier, menuIds);
    List<SupplierUserDataDTO> supplierUserDataDTOS = new ArrayList<>();
    if (supplierUsers != null && supplierUsers.size() > 0) {
      for (SupplierUser supplierUser : supplierUsers) {
        SupplierUserDataDTO supplierUserDataDTO = new SupplierUserDataDTO(supplierUser, null);
        supplierUserDataDTOS.add(supplierUserDataDTO);
      }
      supplierDataInUserDTO.setSupplierUserDataDTOList(supplierUserDataDTOS);
    }
    Optional<BusinessInfoDTO> tianYanInfo =
        xhgjService.getTianYanInfo(supplier.getMdmCode(), supplier.getEnterpriseName());
    String corporate = tianYanInfo.map(BusinessInfoDTO::getLegalPersonName).orElse(StrUtil.EMPTY);
    supplierDataInUserDTO.setCorporate(corporate);
    supplierDataInUserDTO.setUscc(
        tianYanInfo.map(BusinessInfoDTO::getCreditCode).orElse(StrUtil.EMPTY));
    List<SupplierPerformance> oldSupplierPerformances =
        CollUtil.emptyIfNull(
            supplierPerformanceDao.getListBySupplierId(Collections.singletonList(supplierId)));
    //同一平台类型的数据只展示最新时间的一条
    List<SupplierPerformance> supplierPerformances = oldSupplierPerformances.stream()
        .sorted(Comparator.comparing((SupplierPerformance sp) -> sp.getCreateTime() == null ?
            Long.MIN_VALUE : sp.getCreateTime()).reversed())
        .collect(Collectors.groupingBy(SupplierPerformance::getPlatformCode))
        .values().stream()
        .map(list -> list.stream().findFirst().orElseThrow(() -> new CheckException("平台类型为空")))
        .collect(Collectors.toList());
    List<SupplierPerformanceDTO> supplierPerformanceDTOS = this.buildSupplierPerformanceDTO(supplierPerformances);
    supplierDataInUserDTO.setSupplierPerformanceDTOList(supplierPerformanceDTOS);
    supplierDataInUserDTO.setLicense(
        supplierInGroupRepository.findFirstBySupplierIdAndGroupIdAndState(supplierId,
        groupDao.getGroupByErpCode(Constants.HEADQUARTERS_CODE).getId(),Constants.STATE_OK).map(SupplierInGroup::getLicenseUrl).orElse(""));

    // 下单平台
    // 根据公司ID获取全部合同
    List<LandingMerchantContract> lists =
        landingMerchantContractService.getAllContractsBasedOnCompanyID(supplier.getId());
    List<SupplierInformation> supplierInformations = new ArrayList<>();
    if (lists != null) {
      for (LandingMerchantContract contract : lists) {
        SupplierInformation supplierInformation = new SupplierInformation();
        EntryRegistrationEntity entryRegistrationOrder = null;
        // 入驻报备单
        if (StrUtil.isNotBlank(contract.getEntryRegistrationOrderId())) {
          entryRegistrationOrder =
              entryRegistrationRepository.byId(contract.getEntryRegistrationOrderId());
        }
        EntryRegistrationLandingMerchant merchant = null;
        // 落地商
        if (entryRegistrationOrder != null) {
          if (StrUtil.isNotBlank(entryRegistrationOrder.getId())) {
            merchant =
                entryRegistrationLandingMerchantService.getEntryRegistrationOrderId(
                    entryRegistrationOrder.getId());
          }
          supplierInformation.setAccountPeriod(entryRegistrationOrder.getAccountPeriod());
          supplierInformation.setNotes(entryRegistrationOrder.getNotes());
          supplierInformation.setDeposit(entryRegistrationOrder.getDeposit());
          supplierInformation.setDepositState(contract.getDepositState());
          supplierInformation.setTaxRate(merchant.getTaxRate());

          // 根据平台编码获取平台信息
          supplierInformation.setPlatformName(entryRegistrationOrder.getPlatformNames());
          // 身份证
          fileService.findFirstByRelationIdAndRelationType(merchant.getId(),
              Constants.FILE_TYPE_LANDING_MERCHANT_ID_CARD_PHOTO).ifPresent(file -> {
            supplierInformation.setLegalPersonIdCard(new FileDTO(file));
          });

          // 产品资质书
          List<File> fileListByIdAndType =
              fileService.getFileList(merchant.getId(),
                  Constants.FILE_TYPE_LANDING_MERCHANT_PRODUCT_QUALIFICATION);
          List<FileDTO> fileDTOS = new ArrayList<>();
          if (fileListByIdAndType != null) {
            fileListByIdAndType.forEach(file -> {
              fileDTOS.add(new FileDTO(file));
            });
          }
          supplierInformation.setProductQualification(fileDTOS);
          supplierInformations.add(supplierInformation);
        }
      }
      supplierDataInUserDTO.setSupplierInformations(supplierInformations);
    }

    return supplierDataInUserDTO;
  }

  @Override
  public SupplierDataInUserV2DTO getSupplierUserListV2(String supplierId) {
    if (StringUtils.isNullOrEmpty(supplierId)) {
      throw new CheckException("供应商id不能为空");
    }
    Supplier supplier =
        supplierRepository
            .findById(supplierId)
            .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    List<SupplierUser> supplierUsers = supplierUserDao.getSupplierUserListBySidAsc(supplierId);
    // 获取后台用户id
    List<String> createManBack = supplierUsers.stream()
        .filter(item -> SupplierUserSource.BACKEND.getCode().equals(item.getSource()))
        .map(SupplierUser::getCreateMan).filter(StrUtil::isNotBlank).distinct()
        .collect(Collectors.toList());
    createManBack.add("-1");
    Map<String, User> id2User = userRepository.findAllById(createManBack).stream()
        .collect(Collectors.toMap(User::getId, Function.identity(), (key1, key2) -> key1));
    // 获取前台用户id
    List<String> createManFront = supplierUsers.stream()
        .filter(item -> SupplierUserSource.FRONTEND.getCode().equals(item.getSource()))
        .map(SupplierUser::getCreateMan).filter(StrUtil::isNotBlank).distinct()
        .collect(Collectors.toList());
    Map<String, SupplierUser> id2SupplerUser = supplierUserRepository.findAllById(createManFront).stream()
        .collect(Collectors.toMap(SupplierUser::getId, Function.identity(), (key1, key2) -> key1));
    createManFront.add("-1");
    List<SupplierToMenu> menus = supplierToMenuService.findAllBySupplierId(supplier.getId());
    List<String> menuIds = new ArrayList<>();
    if (CollUtil.isNotEmpty(menus)) {
      menuIds = menus.stream().map(SupplierToMenu::getMenuId).collect(Collectors.toList());
    }
    SupplierDataInUserV2DTO res = new SupplierDataInUserV2DTO(supplier, menuIds);
    List<SupplierUserDataDTO> supplierUserDataDTOS = new ArrayList<>();
    for (SupplierUser supplierUser : supplierUsers) {
      String createManName = "";
      if (SupplierUserSource.BACKEND.getCode().equals(supplierUser.getSource())) {
        User user = id2User.get(supplierUser.getCreateMan());
        if (user != null) {
          createManName = user.getRealName();
        }
      } else {
        SupplierUser user = id2SupplerUser.get(supplierUser.getCreateMan());
        if (user != null) {
          createManName = user.getRealName();
        }
      }
      SupplierUserDataDTO supplierUserDataDTO = new SupplierUserDataDTO(supplierUser, createManName);
      supplierUserDataDTOS.add(supplierUserDataDTO);
    }
    res.setSupplierUserDataDTOList(supplierUserDataDTOS);
    Optional<BusinessInfoDTO> tianYanInfo =
        xhgjService.getTianYanInfo(supplier.getMdmCode(), supplier.getEnterpriseName());
    String corporate = tianYanInfo.map(BusinessInfoDTO::getLegalPersonName).orElse(StrUtil.EMPTY);
    res.setCorporate(corporate);
    res.setUscc(
        tianYanInfo.map(BusinessInfoDTO::getCreditCode).orElse(StrUtil.EMPTY));
    res.setLicense(
        supplierInGroupRepository.findFirstBySupplierIdAndGroupIdAndState(supplierId,
            groupDao.getGroupByErpCode(Constants.HEADQUARTERS_CODE).getId(),Constants.STATE_OK).map(SupplierInGroup::getLicenseUrl).orElse(""));
    return res;
  }

  private String getStepDiscountRatio(LandingMerchantContract contract,
      BigDecimal totalPriceBySupplier,
      List<EntryRegistrationDiscount> entryRegistrationDiscountList) {
    BigDecimal discountRatio = BigDecimal.ZERO;
    boolean shouldCheckDiscount = StrUtil.isBlank(contract.getEntryRegistrationOrderId()) ||
        (StrUtil.isNotBlank(contract.getFileReviewState()) &&
            StrUtil.equals(FileReviewStateEnum.THROUGH_THE.getKey(),
                contract.getFileReviewState())) || BooleanUtil.isTrue(contract.getRegistrationRateUpdateStatus());
    //供应商账号履约信息中的比例无需从第三方取，
    // 如果有阶梯比例，则根据当前履约金额自行计算展示，如果没有阶梯比例只需展示初始比例即可
    //报备单推送的合同，折扣比例暂不写入，等对应的合同附件上传归档后写入
    if (shouldCheckDiscount) {
      for (EntryRegistrationDiscount entryDiscount : entryRegistrationDiscountList) {
        if (StrUtil.equals(EntryRegistrationDiscountTypeEnum.STEP_DISCOUNT_RATIO.getKey(),
            entryDiscount.getType()) && NumberUtil.isGreaterOrEqual(
            BigDecimalUtil.formatForStandard(totalPriceBySupplier),
            BigDecimalUtil.formatForStandard(entryDiscount.getPerformanceAmount()))) {
          discountRatio = entryDiscount.getDiscountRatio();
        }
      }
    }
    return BigDecimalUtil.formatForStandard(discountRatio).toPlainString();
  }

  private String getOaUserName(String oaId) {
    String name = "";
    if (StrUtil.isNotBlank(oaId)) {
      JSONObject userJson = httpUtil.getOAUserInfoById(oaId);
      if (userJson != null) {
        name = userJson.containsKey("name") ? String.valueOf(userJson.get("name")) : "";
      }
    }
    return name;
  }

  @Override
  public String getNewSupplierCode() {
    String codeStr = dao.isSupplierCode(); // 查询最大的一条记录
    int codeNum = Integer.parseInt(codeStr);
    NumberFormat f = new DecimalFormat("000000");
    return f.format(codeNum + 1);
  }

  @Override
  public void relieveSupplierById(String supplierIds, String userId) {
    if (StringUtils.isNullOrEmpty(supplierIds)) {
      throw new CheckException("supplierIds为空");
    }
    if (StringUtils.isNullOrEmpty(userId)) {
      throw new CheckException("userId为空");
    }
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    if (ObjectUtil.notEqual(Constants.USERNAME, user.getName())) {
      throw new CheckException("您没有移除权限");
    }
    String[] supplierId = supplierIds.split(",");
    for (String id : supplierId) {
      SupplierInGroup supplierInGroup =
          supplierInGroupService.get(
              id, () -> CheckException.noFindException(SupplierInGroup.class, id));
      boolean blacklistingSuppliers = false;
      if (supplierInGroup != null) {
        supplierInGroup.setState(Constants.COMMONSTATE_OK);
        supplierInGroup.setBlockRange("");
        supplierInGroupService.save(supplierInGroup);
        Supplier supplier = supplierInGroup.getSupplier();
        Group group = supplierInGroup.getGroup();
        if (supplier == null
            || group == null
            || StrUtil.isBlank(supplier.getMdmCode())
            || StrUtil.isBlank(group.getErpCode())) {
          throw new CheckException("数据异常，请联系管理员");
        }
        try {
          blacklistingSuppliers =
              sapSupplierRequest.blacklistingSuppliers(
                  supplier.getMdmCode(),
                  group.getErpCode(),
                  null);
        } catch (CheckException e){
          throw e;
        } catch (Exception e) {
          log.error(ExceptionUtil.stacktraceToString(e));
          throw new CheckException("SAP系统响应异常，更新失败");
        }
      }
      if (Boolean.FALSE.equals(blacklistingSuppliers)) {
        throw new CheckException("SAP系统响应解除黑名单失败，请联系管理员");
      }
    }
  }

  @SneakyThrows
  @Override
  public void exportNormalSupplier(SupplierExportParam supplierExportParam) {
    String userId = supplierExportParam.getUserId();
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    if (user == null) {
      throw new CheckException("用户为空");
    }
    // 设置任务编号
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("userId", userId);
    if (CollUtil.isNotEmpty(supplierExportParam.getSupplierIds())) {
      jsonObject.put("supplierIds", supplierExportParam.getSupplierIds());
    } else {
      jsonObject.put("userId", StringUtils.emptyIfNull(supplierExportParam.getUserId()));
      jsonObject.put("erpCode", StringUtils.emptyIfNull(supplierExportParam.getErpCode()));
      jsonObject.put("userGroup", StringUtils.emptyIfNull(supplierExportParam.getUserGroup()));
      jsonObject.put(
          "enterpriseNature", StringUtils.emptyIfNull(supplierExportParam.getEnterpriseNature()));
      jsonObject.put(
          "enterpriseName", StringUtils.emptyIfNull(supplierExportParam.getEnterpriseName()));
      jsonObject.put(
          "enterpriseLevel", StringUtils.emptyIfNull(supplierExportParam.getEnterpriseLevel()));
      jsonObject.put(
          "purchaserName", StringUtils.emptyIfNull(supplierExportParam.getPurchaserName()));
      jsonObject.put("industry", StringUtils.emptyIfNull(supplierExportParam.getIndustry()));
      jsonObject.put("brands", StringUtils.emptyIfNull(supplierExportParam.getBrands()));
      jsonObject.put("mobile", StringUtils.emptyIfNull(supplierExportParam.getMobile()));
      jsonObject.put("contacts", StringUtils.emptyIfNull(supplierExportParam.getContacts()));
      jsonObject.put("isOpen", StringUtils.emptyIfNull(supplierExportParam.getIsOpen()));
      jsonObject.put("erpState", StringUtils.emptyIfNull(supplierExportParam.getErpState()));
      jsonObject.put("startDate", StringUtils.emptyIfNull(supplierExportParam.getStartDate()));
      jsonObject.put("endDate", StringUtils.emptyIfNull(supplierExportParam.getEndDate()));
      jsonObject.put("schemeId", StringUtils.emptyIfNull(supplierExportParam.getSchemeId()));
    }
    // 新增任务
    Mission mission = Mission.createStartingMission(
        missionUtil.getMissionCode(user.getCode()),
        "导出-供应商导出",
        userId,
        Constants.PLATFORM_TYPE_AFTER
    );
    missionRepository.save(mission);
    batchTaskMqSender.toHandleBatchTask(
        mission.getId(), jsonObject.toString(), Constants_Batch.BATCH_TASK_DCGYS);
  }

  @Override
  public void updateSupplierSynState() {
    List<Supplier> supplierList = dao.getEnterpriseByType();
    if (supplierList != null && supplierList.size() > 0) {
      for (Supplier supplier : supplierList) {
        if (supplier != null) {
          if (Constants.SUPPLIERTYPE_CHINA.equals(supplier.getSupType())) {
            if (Constants.COMMONSTATE_TEMPORARYSTORAGE.equals(supplier.getState())) {
              supplier.setSynState(Constants.SUPPLIER_SYN_STATE_TEMP);
            } else {
              if (SupplierLevelEnum.STRATEGIC.getCode().equals(supplier.getEnterpriseLevel())
                  || SupplierLevelEnum.HIGH_QUALITY.getCode().equals(supplier.getEnterpriseLevel())
                  || SupplierLevelEnum.GENERAL.getCode().equals(supplier.getEnterpriseLevel())) {
                // 协议
                List<File> xyList =
                    fileDao.getFileListBySId(supplier.getId(), Constants.FILE_TYPE_CGXY);
                if (xyList != null && xyList.size() > 0) {
                  if (!StringUtils.isNullOrEmpty(supplier.getId())
                      && !StringUtils.isNullOrEmpty(supplier.getErpCode())) {
                    supplier.setSynState(Constants.SUPPLIER_SYN_STATE_SUCCESS);
                  } else {
                    supplier.setSynState(Constants.SUPPLIER_SYN_STATE_NOERPCODE);
                  }
                } else {
                  supplier.setSynState(Constants.SUPPLIER_SYN_STATE_NOXE);
                }
              } else {
                if (!StringUtils.isNullOrEmpty(supplier.getId())
                    && !StringUtils.isNullOrEmpty(supplier.getErpCode())) {
                  supplier.setSynState(Constants.SUPPLIER_SYN_STATE_SUCCESS);
                } else {
                  supplier.setSynState(Constants.SUPPLIER_SYN_STATE_NOERPCODE);
                }
              }
            }
          } else if (Constants.SUPPLIERTYPE_ABROAD.equals(supplier.getSupType())) {
            if (Constants.COMMONSTATE_TEMPORARYSTORAGE.equals(supplier.getState())) {
              supplier.setSynState(Constants.SUPPLIER_SYN_STATE_TEMP);
            } else {
              if (!StringUtils.isNullOrEmpty(supplier.getId())
                  && !StringUtils.isNullOrEmpty(supplier.getErpCode())) {
                supplier.setSynState(Constants.SUPPLIER_SYN_STATE_SUCCESS);
              } else {
                supplier.setSynState(Constants.SUPPLIER_SYN_STATE_NOERPCODE);
              }
            }
          }
          supplierRepository.saveAndFlush(supplier);
        }
      }
    }
  }

  @Override
  public void dealNoXESupplier() {
    List<Supplier> supplierList =
        dao.getEnterpriseBySynState(
            Constants.SUPPLIER_SYN_STATE_NOXE, Constants.NORMAL_SUPPLIER_SCORE);
    if (CollUtil.isNotEmpty(supplierList)) {
      for (Supplier supplier : supplierList) {
        try {
          List<File> fileList = fileDao.getFileListByRIdAndAgree(supplier.getId());
          if (fileList != null && fileList.size() > 0) {
            supplier.setSynState(Constants.SUPPLIER_SYN_STATE_SUCCESS);
            supplierRepository.save(supplier);
          }
        } catch (Exception e) {
          log.error(e.toString());
        }
      }
    }
  }

  @Override
  public void updateSupplierFinancialInfo() {
    List<Supplier> supplierList = dao.getEnterpriseByType();
    if (supplierList != null && supplierList.size() > 0) {
      for (Supplier supplier : supplierList) {
        if (!StringUtils.isNullOrEmpty(supplier.getBankName())) {
          Financial financial = new Financial();
          financial.setBankName(
              !StringUtils.isNullOrEmpty(supplier.getBankName()) ? supplier.getBankName() : "");
          financial.setSettleCurrency(
              !StringUtils.isNullOrEmpty(supplier.getSettleCurrency())
                  ? supplier.getSettleCurrency()
                  : "");
          financial.setInvoiceType(
              !StringUtils.isNullOrEmpty(supplier.getInvoiceType())
                  ? supplier.getInvoiceType()
                  : "");
          financial.setTaxRate(
              !StringUtils.isNullOrEmpty(supplier.getTaxRate()) ? supplier.getTaxRate() : "");
          financial.setTaxNo(
              !StringUtils.isNullOrEmpty(supplier.getTaxNo()) ? supplier.getTaxNo() : "");
          financial.setBankAccount(
              !StringUtils.isNullOrEmpty(supplier.getBankAccount())
                  ? supplier.getBankAccount()
                  : "");
          financial.setBankNum(
              !StringUtils.isNullOrEmpty(supplier.getBankNum()) ? supplier.getBankNum() : "");
          financial.setBankCode(
              !StringUtils.isNullOrEmpty(supplier.getBankCode()) ? supplier.getBankCode() : "");
          financial.setSupplier(supplier);
          financial.setSupplierId(supplier.getId());
          financial.setSwiftCode(
              !StringUtils.isNullOrEmpty(supplier.getSwiftcode()) ? supplier.getSwiftcode() : "");
          financial.setState(Constants.STATE_OK);
          financial.setCreateTime(System.currentTimeMillis());
          financialRepository.save(financial);
        }
      }
    }
  }

  @SneakyThrows
  @Override
  public void updateExcelSupplierManage(MultipartFile file, String userId) {
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    if (user == null) {
      throw new CheckException("用户为空");
    }
    if (file != null) {
      String fileName = file.getOriginalFilename();
      if (fileName == null || fileName.isEmpty()) {
        throw new CheckException("文件异常,请查看文件");
      }
      // 新增任务
      Mission mission = Mission.createStartingMission(
          missionUtil.getMissionCode(user.getCode()),
          "批改-批量修改供应商负责采购",
          userId,
          Constants.PLATFORM_TYPE_AFTER,
          file.getOriginalFilename(),
          importExcelUtil.saveExcel(file)
      );
      missionRepository.save(mission);
      batchTaskMqSender.toHandleBatchTask(
          mission.getId(), null, Constants_Batch.BATCH_TASK_PGGYSFZCZ);
    } else {
      throw new CheckException("无上传文件");
    }
  }

  @SneakyThrows
  @Override
  public void updateSupplierRegisteredAddress(MultipartFile file, String userId) {
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    if (user == null) {
      throw new CheckException("用户为空");
    }
    if (file != null) {
      String fileName = file.getOriginalFilename();
      if (fileName == null || fileName.isEmpty()) {
        throw new CheckException("文件异常,请查看文件");
      }
      // 设置任务编号
      // 新增任务
      Mission mission = Mission.createStartingMission(
          missionUtil.getMissionCode(user.getCode()),
          "批改-批量修改供应商注册地址",
          userId,
          Constants.PLATFORM_TYPE_AFTER,
          file.getOriginalFilename(),
          importExcelUtil.saveExcel(file)
      );
      missionRepository.save(mission);
      batchTaskMqSender.toHandleBatchTask(
          mission.getId(), null, Constants_Batch.BATCH_TASK_SUPPLIER_REGISTERED_ADDRESS);
    } else {
      throw new CheckException("无上传文件");
    }
  }

  @SneakyThrows
  @Override
  public void updateSupplierEmpower(MultipartFile file, String userId) {
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    if (user == null) {
      throw new CheckException("用户为空");
    }
    if (file != null) {
      String fileName = file.getOriginalFilename();
      if (fileName == null || fileName.isEmpty()) {
        throw new CheckException("文件异常,请查看文件");
      }
      // 设置任务编号
      // 新增任务
      Mission mission = Mission.createStartingMission(
          missionUtil.getMissionCode(user.getCode()),
          "批改-批量修改供应商授权",
          userId,
          Constants.PLATFORM_TYPE_AFTER,
          file.getOriginalFilename(),
          importExcelUtil.saveExcel(file)
      );
      missionRepository.save(mission);
      batchTaskMqSender.toHandleBatchTask(
          mission.getId(), null, Constants_Batch.BATCH_TASK_SUPPLIER_EMPOWER);
    } else {
      throw new CheckException("无上传文件");
    }
  }

  @Override
  public SupplierOrderPageDTO getOrderSupplierPage(
      String userId,
      String enterpriseName,
      String erpCode,
      String platforms,
      String schemeId,
      Integer pageNo,
      Integer pageSize) {
    SupplierOrderPageDTO supplierOrderPageDTO = new SupplierOrderPageDTO();
    // 查询方案
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeDao.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_SUPPLIER_ORDER);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeDao.get(schemeId);
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          enterpriseName =
              StrUtil.blankToDefault(enterpriseName,searchJo.containsKey("enterpriseName") ?
                  searchJo.getString(
                  "enterpriseName") :
 "");
          erpCode = StrUtil.blankToDefault(erpCode,searchJo.containsKey("erpCode") ?
              searchJo.getString("erpCode") : "");
        }
      }
    }
    List<String> platformArr = new ArrayList<>();
    if (!StringUtils.isNullOrEmpty(platforms)) {
      platformArr = Arrays.asList(platforms.split(","));
    }
    Page<String> page =
        dao.getOrderSupplierPage(enterpriseName, erpCode, platformArr, pageNo, pageSize);
    List<SupplierOrderDTO> pageDataList = new ArrayList<>();
    int totalPages = page.getTotalPages();
    if (!(pageNo > totalPages)) {
      List<String> supplierIdList = page.getContent();
      PageUtil.setOneAsFirstPageNo();
      for (String s : supplierIdList) {
        Supplier supplier = get(s);
        SupplierOrderDTO data = new SupplierOrderDTO(supplier);
        pageDataList.add(data);
      }
    }
    supplierOrderPageDTO.setSupplierOrderDTOPageResult(
        new PageResult<>(pageDataList, page.getTotalElements(), totalPages, pageNo, pageSize));
    long count = dao.getSupplierOrderOpenCount();
    long supplierOrderOpenCount =
        supplierRepository.countByOpenSupplierOrderAndState(Boolean.TRUE, Constants.STATE_OK);
    supplierOrderPageDTO.setOpenCount(count);
    supplierOrderPageDTO.setSupplierOrderOpenCount(supplierOrderOpenCount);
    return supplierOrderPageDTO;
  }

  @Override
  public OpenOrderReceivePermissionCountDTO getOpenOrderReceivePermissionCount() {
    return new OpenOrderReceivePermissionCountDTO(
        dao.getSupplierOrderOpenCount(),
        supplierRepository.countByOpenSupplierOrderAndState(Boolean.TRUE, Constants.STATE_OK));
  }

  @Override
  public PageResult<SupplierAccountAndOrderReceivingInfo>
      getPageSupplierAccountAndOrderReceivingInfo(
          String userId,
          String userGroup,
          String platforms,
          Boolean openOrder,
          Boolean openSupplierOrder,
          String enterpriseName,
          String mdmCode,
          String uscc,
          String schemeId,
          Integer pageNo,
          Integer pageSize) {
    // 查询方案
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeService.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_SUPPLIER_CHINA);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        SupplierAccountAndOrderReceivingQuery query =
            JSON.parseObject(
                search.getContent(), new TypeReference<SupplierAccountAndOrderReceivingQuery>() {});
        if (query != null) {
          mdmCode = StrUtil.isNotBlank(mdmCode)?mdmCode:query.getMdmCode();
          enterpriseName =
              StrUtil.isNotBlank(enterpriseName)?enterpriseName:query.getEnterpriseName();
          platforms = StrUtil.isNotBlank(platforms)?platforms:query.getPlatforms();
          openOrder = openOrder != null ? openOrder:query.getOpenOrder() ;
          openSupplierOrder =
              openSupplierOrder != null
                  ? openSupplierOrder:query.getOpenSupplierOrder()
                  ;
        }
      }
    }
    User user;
    if (!StringUtils.isNullOrEmpty(userId)) {
      user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
      if (user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
        userGroup = "";
      }
    }
    return PageResultBuilder.buildPageResult(
        dao.getOrderSupplierPage(
            enterpriseName,
            StrUtil.split(StrUtil.emptyIfNull(platforms), CharUtil.COMMA, true, true),
            userGroup,
            openOrder,
            openSupplierOrder,
            mdmCode,
            uscc,
            pageNo,
            pageSize),
        (objects) -> {
          SupplierAccountAndOrderReceivingInfo supplierAccountAndOrderReceivingInfo =
              new SupplierAccountAndOrderReceivingInfo();
          String id = (String) objects[0];
          supplierAccountAndOrderReceivingInfo.setId(id);
          supplierAccountAndOrderReceivingInfo.setAccountCount(
              Long.parseLong(String.valueOf(ObjectUtil.defaultIfNull(objects[1], "0"))));
          Supplier supplier = get(id);
          supplierAccountAndOrderReceivingInfo.setMdmCode(supplier.getMdmCode());
          supplierAccountAndOrderReceivingInfo.setEnterpriseName(supplier.getEnterpriseName());
          supplierAccountAndOrderReceivingInfo.setOpenOrder(
              ObjectUtil.equal(supplier.getIsOpenOrder(), Constants.YES));
          supplierAccountAndOrderReceivingInfo.setOpenSupplierOrder(
              supplier.getOpenSupplierOrder());
          supplierAccountAndOrderReceivingInfo.setOrderReceiveTimeLimit(
              supplier.getOrderReceiveTimeLimit());
          supplierAccountAndOrderReceivingInfo.setPlatform(supplier.getPlatformRemove());
          supplierAccountAndOrderReceivingInfo.setUscc(supplier.getUscc());
          return supplierAccountAndOrderReceivingInfo;
        });
  }

  @Override
  public PageResult<SupplierAccountAndOrderReceivingInfo>
      getPageSupplierAccountAndOrderReceivingInfoSearch(
          String userId,
          String userGroup,
          String platforms,
          Boolean openOrder,
          Boolean openSupplierOrder,
          String enterpriseName,
          String mdmCode,
          String uscc,
          String oldSupplierType,
          String schemeId,
          Integer pageNo,
          Integer pageSize) {
    Group group = groupService.getGroupByErpCode(userGroup);
    // 查询方案
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeService.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_SUPPLIER_CHINA);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        SupplierAccountAndOrderReceivingQuery query =
            JSON.parseObject(
                search.getContent(), new TypeReference<SupplierAccountAndOrderReceivingQuery>() {});
        if (query != null) {
          mdmCode = StrUtil.isNotBlank(mdmCode)?mdmCode:query.getMdmCode();
          enterpriseName =
              StrUtil.isNotBlank(enterpriseName)?enterpriseName: query.getEnterpriseName()
                  ;
          platforms = StrUtil.isNotBlank(platforms)?platforms:query.getPlatforms();
          openOrder = openOrder != null ? openOrder:query.getOpenOrder() ;
          openSupplierOrder =
              openSupplierOrder != null
                  ? openSupplierOrder:query.getOpenSupplierOrder()
                  ;
          oldSupplierType =
              oldSupplierType != null ? oldSupplierType:query.getOldSupplierType() ;
        }
      }
    }
    User user;
    if (!StringUtils.isNullOrEmpty(userId)) {
      user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
      if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)
          || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
        userGroup = "";
      }
    }
    return PageResultBuilder.buildPageResult(
        dao.getOrderSupplierPageSearch(
            enterpriseName,
            StrUtil.split(StrUtil.emptyIfNull(platforms), CharUtil.COMMA, true, true),
            userGroup,
            openOrder,
            openSupplierOrder,
            mdmCode,
            uscc,
            oldSupplierType,
            pageNo,
            pageSize),
        (objects) -> {
          SupplierAccountAndOrderReceivingInfo supplierAccountAndOrderReceivingInfo =
              new SupplierAccountAndOrderReceivingInfo();
          String id = (String) objects[0];
          supplierAccountAndOrderReceivingInfo.setId(id);
          Supplier supplier = get(id);
          Optional<SupplierInGroup> supplierInGroupOptional =
              supplierInGroupRepository.findFirstBySupplierIdAndGroupIdAndState(
                  supplier.getId(), group.getId(), Constants.STATE_OK);
          supplierInGroupOptional.ifPresent(
              supplierInGroup -> {
                supplierAccountAndOrderReceivingInfo.setAccountPeriod(
                    supplierInGroup.getAccountPeriod());
              });
          supplierAccountAndOrderReceivingInfo.setPaymentType(supplier.getPayType());
          supplierAccountAndOrderReceivingInfo.setMdmCode(supplier.getMdmCode());
          supplierAccountAndOrderReceivingInfo.setEnterpriseName(supplier.getEnterpriseName());
          supplierAccountAndOrderReceivingInfo.setOpenOrder(
              ObjectUtil.equal(supplier.getIsOpenOrder(), Constants.YES));
          supplierAccountAndOrderReceivingInfo.setOpenSupplierOrder(
              supplier.getOpenSupplierOrder());
          supplierAccountAndOrderReceivingInfo.setOrderReceiveTimeLimit(
              supplier.getOrderReceiveTimeLimit());
          supplierAccountAndOrderReceivingInfo.setPlatform(supplier.getPlatformRemove());
          supplierAccountAndOrderReceivingInfo.setUscc(supplier.getUscc());
          supplierAccountAndOrderReceivingInfo.setSupType(supplier.getSupType());
          return supplierAccountAndOrderReceivingInfo;
        });
  }

  @Override
  public void exportSupplierAccountAndOrderReceivingInfo(
      SupplierAccountAndOrderReceivingQuery query) {
    String userId = query.getUserId();
    Assert.notEmpty(userId);
    String schemeId = query.getSchemeId();
    // 查询方案
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeService.getDefaultSearchScheme(userId, Constants.SEARCH_TYPE_SUPPLIER_CHINA);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        SupplierAccountAndOrderReceivingQuery queryScheme =
            JSON.parseObject(
                search.getContent(), new TypeReference<SupplierAccountAndOrderReceivingQuery>() {});
        if (queryScheme != null) {
          query.setMdmCode(StrUtil.isNotBlank(query.getMdmCode())?query.getMdmCode(): queryScheme.getMdmCode());
          query.setEnterpriseName(StrUtil.isNotBlank(query.getEnterpriseName())?
              query.getEnterpriseName(): queryScheme.getEnterpriseName());
          query.setPlatforms(StrUtil.isNotBlank(query.getPlatforms())?query.getPlatforms():
              queryScheme.getPlatforms());
          query.setOpenOrder(query.getOpenOrder()!=null? query.getOpenOrder():
              queryScheme.getOpenOrder());
          query.setOpenSupplierOrder(query.getOpenSupplierOrder()!=null? query.getOpenSupplierOrder():
              queryScheme.getOpenSupplierOrder());
        }
      }
    }
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)
        || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      query.setUserGroup(StrUtil.EMPTY);
    }
    Mission mission =
        missionService.createMission(user, "导出-供应商账号信息", Constants.PLATFORM_TYPE_AFTER, null, null);
    Map<String, Object> params = new HashMap<>(3);
    params.put("query", query);
    missionDispatcher.doDispatch(
        mission.getId(),
        JSON.toJSONString(params),
        MissionTypeEnum.BATCH_TASK_EXPORT_SUPPLIER_ACCOUNT_INFO);
  }

  @Override
  public void configSupplierOrder(
      String supplierId,
      String isOpenOrder,
      Boolean openSupplierOrder,
      String platforms,
      Integer receiveTimeLimit) {
    if (StringUtils.isNullOrEmpty(supplierId)) {
      throw new CheckException("接口请求有误");
    }
    if (StringUtils.isNullOrEmpty(isOpenOrder)) {
      throw new CheckException("状态有误");
    }
    if (openSupplierOrder == null) {
      throw new CheckException("是否开启供应商订单接待必传");
    }
    Supplier supplier =
        supplierRepository
            .findById(supplierId)
            .orElseThrow(() -> CheckException.noFindException(Supplier.class, supplierId));
    if (supplier != null) {
      supplier.setIsOpenOrder(isOpenOrder);
      if (openSupplierOrder) {
        supplier.setOrderReceiveTimeLimit(receiveTimeLimit);
      }
      if (!StringUtils.isNullOrEmpty(platforms)) {
        supplier.setPlatform(platforms);
      }
      supplier.setOpenSupplierOrder(openSupplierOrder);
      supplierRepository.save(supplier);
    }
  }

  @Override
  public MySupplierData getMySupplierData(String userId, String userGroup) {
    Assert.notEmpty(userId);
    Assert.notEmpty(userGroup);
    MySupplierData mySupplierData = new MySupplierData();
    // 判断是否是管理员
    User user = userService.getUserById(userId);
    if (Constants.USERNAME.equals(user.getName())) {
      return mySupplierData;
    }
    // 校验组织编码是否正确
    Group group = groupService.getGroupByErpCode(userGroup);
    if (group == null) {
      throw new CheckException("【" + userGroup + "】组织不存在");
    }
    List<String> supplierInGroupIdList =
        CollUtil.emptyIfNull(
            supplierInGroupService.getMyResponsibleSuppliersId(userId, group.getId()));
    // 我为负责人的供应商总数
    long myResponsibleSupplierTotal = supplierInGroupIdList.size();
    mySupplierData.setMyResponsibleSupplierTotal(myResponsibleSupplierTotal);
    // 获得各类供应商的数量
    List<SupplierTypeAndCountDTO> supplierTypeAndCountDtoList =
        supplierInGroupService.getVariousSuppliersCount(
            userId, group.getId(), myResponsibleSupplierTotal);
    mySupplierData.setSupplierTypeAndCountDtoList(supplierTypeAndCountDtoList);
    String erpId = user.getErpId();
    // 获得一年内仅采购1次的供应商个数
    int purchaseOnlyOnce = 0;
    // 有采购记录的供应商个数
    int suppliersPurchasingRecordsCount = 0;
    int noPurchaseCount;
    // 获得现在时间前一年的时间戳
    long timestampPreviousYear = DateUtil.offset(DateUtil.date(), DateField.YEAR, -1).getTime();
    //修复内存溢出，限制数量
    int batchSize = 1000;
    int offset = 0;
    while (offset < supplierInGroupIdList.size()){
      // 获取当前批次的订单ID子列表
      List<String> currentBatchIds = supplierInGroupIdList.subList(offset,
          Math.min(offset + batchSize, supplierInGroupIdList.size()));
      // 获得采购人负责的供应商的采购次数
      List<Object[]> supplierPurchasesCount =
          CollUtil.emptyIfNull(
              contractService.getSupplierPurchasesCount(
                  erpId, timestampPreviousYear, currentBatchIds));
      for (Object[] supplierAndPurchaseCount : supplierPurchasesCount) {
        suppliersPurchasingRecordsCount++;
        if (Integer.parseInt(supplierAndPurchaseCount[1].toString()) == 1) {
          purchaseOnlyOnce++;
        }
      }
      //更新偏移量，准备处理下一组数据
      offset += batchSize;
    }
    noPurchaseCount =
        NumberUtil.sub(new Number[] {myResponsibleSupplierTotal, suppliersPurchasingRecordsCount})
            .intValue();
    mySupplierData.setPurchaseOnlyOnce(purchaseOnlyOnce);
    mySupplierData.setNoPurchaseCount(noPurchaseCount);
    return mySupplierData;
  }

  @Override
  public void exportSupplierInGroup(
      String userId,
      String userGroup,
      List<String> supplierInGroupIdList,
      String type,
      SupplierChinaQuery chinaQuery,
      SupplierPersonQuery personQuery,
      SupplierAboardQuery abroadQuery) {
    Assert.notEmpty(userId);
    Assert.notEmpty(userGroup);
    User user = userService.getUserById(userId);
    Group group = groupService.getGroupByErpCode(userGroup);
    if (group == null) {
      throw new CheckException("编码为【" + userGroup + "】的组织不存在，请联系管理员");
    }
    Mission mission =
        missionService.createMission(user, "导出-供应商列表", Constants.PLATFORM_TYPE_AFTER, null, null);
    HashMap<String, Object> params = new HashMap<>(3);
    params.put("userId", userId);
    params.put("groupId", group.getId());
    params.put("supplierInGroupIdList", supplierInGroupIdList);
    // 筛选国内供应商导出
    if (!StringUtils.isNullOrEmpty(type)) {
      if (Constants.SUPPLIERTYPE_CHINA_CN.equals(type)) {
        chinaQuery.setUserId(userId);
        chinaQuery.setUserGroup(userGroup);
        List<String> domesticList = supplierInGroupService.getSupplierDomesticList(chinaQuery);
        params.put(
            "supplierInGroupIdList", domesticList.size() > 0 ? domesticList : new ArrayList<>());
      }
      // 筛选个人供应商导出
      if (Constants.SUPPLIERTYPE_PERSONAL_CN.equals(type)) {
        personQuery.setUserId(userId);
        personQuery.setUserGroup(userGroup);
        List<String> personList = supplierInGroupService.getSupplierPerson(personQuery);
        params.put("supplierInGroupIdList", personList.size() > 0 ? personList : new ArrayList<>());
      }
      // 筛选海外供应商导出
      if (Constants.SUPPLIERTYPE_ABROAD_CN.equals(type)) {
        abroadQuery.setUserId(userId);
        abroadQuery.setUserGroup(userGroup);
        List<String> supplierAbroad = supplierInGroupService.getSupplierAbroad(abroadQuery);
        params.put(
            "supplierInGroupIdList",
            supplierAbroad.size() > 0 ? supplierAbroad : new ArrayList<>());
      }
    }

    batchTaskMqSender.toHandleBatchTask(
        mission.getId(),
        JSON.toJSONString(params),
        Constants_Batch.BATCH_TASK_SUPPLIER_IN_GROUP_OUT);
  }

  @SneakyThrows
  @Override
  public void importSupplierInGroup(MultipartFile file, String userId) {
    User user = userService.getUserById(userId);
    String savePath = importExcelUtil.saveExcel(file);
    Mission mission =
        missionService.createMission(
            user, "导入-供应商列表", Constants.PLATFORM_TYPE_AFTER, savePath, file.getOriginalFilename());
    batchTaskMqSender.toHandleBatchTask(
        mission.getId(), JSON.toJSONString(user), Constants_Batch.BATCH_TASK_SUPPLIER_IN_GROUP_IN);
  }

  @Override
  public List<String> searchAllIdByName(String nameOrUsedName, String supType) {
    Assert.notEmpty(nameOrUsedName);
    Assert.isTrue(
        StringUtils.isNullOrEmpty(supType) || Constants.SUPPLIERTYPE.containsKey(supType));
    return dao.getAllIdByNameLikeOrUsedName(nameOrUsedName, supType);
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public <T extends BaseSupplierDTO> Supplier createSuppler(T dto, User user, Group group) {
    Assert.notNull(dto);
    Assert.notNull(user);
    Assert.notNull(group);
    String relativeKey = dto.getRelativeKey();
    SupplierRelativeKeyType relativeKeyType = dto.getRelativeKeyType();
    Supplier supplier;
    String supplierName = dto.getName();
    SupplierTemplateTypeEnum supplierTemplateTypeEnum;
    if (dto instanceof ChinaSupplierDTO) {
      supplierTemplateTypeEnum = SupplierTemplateTypeEnum.CHINA;
    } else if (dto instanceof AbroadSupplierDTO) {
      supplierTemplateTypeEnum = SupplierTemplateTypeEnum.ABROAD;
    } else {
      throw new IllegalArgumentException("此方法目前仅用于国内和海外供应商");
    }
    String uscc;
    String industry;
    String country;
    String province;
    String city;
    if (dto instanceof ChinaSupplierDTO) {
      uscc = ((ChinaSupplierDTO) dto).getUscc();
      industry = ((ChinaSupplierDTO) dto).getIndustry();
      province = ((ChinaSupplierDTO) dto).getProvince();
      city = ((ChinaSupplierDTO) dto).getCity();
      country = "中国";
    } else {
      uscc = null;
      industry = null;
      province = null;
      city = null;
      country = ((AbroadSupplierDTO) dto).getCountry();
    }
    if (StringUtils.isNullOrEmpty(relativeKey) && relativeKeyType == null) {
      // 如果关联参数都为空，则代表是根据搜索内容新增
      supplier = new Supplier();
      supplier.setEnterpriseName(supplierName);
      //  需要再次确认该供应商在 SRM、MDM 和天眼查内不存在
      validateNameBeforeAdd(supplierName, supplierTemplateTypeEnum.getType(), true);
      // 根据模板校验
      TemplateUtil.validateObjByTemplateMap(
          dto, supplierTemplateService.getSupplierRequiredMap(group, supplierTemplateTypeEnum));
      supplier.setUscc(uscc);
      // 行业在下面赋值
      supplier.setCountry(country);
      supplier.setSupType(supplierTemplateTypeEnum.getType());
      // 省市在下面赋值
      supplier.setCreateGroup(group.getName());
      supplier.setCreateCode(group.getCode());
      supplier.setCreateMan(user.getId());
      supplier.setCreateTime(System.currentTimeMillis());
      // 根据内容新增的供应商都需要 MDM 进行主数据审核
      supplier.setState(Constants.COMMONSTATE_CHECKING);
      supplier.setPayType(dto.getPayType());
      supplier.setPayTypeOther(dto.getPayTypeOther());
      if (dto instanceof ChinaSupplierDTO) {
        supplier.setRegion(((ChinaSupplierDTO) dto).getRegion());
      }
      supplierRepository.save(supplier);
    } else {
      // 否则就是根据搜索结果新增
      if (StringUtils.isNullOrEmpty(relativeKey) || relativeKeyType == null) {
        throw new CheckException("根据搜索结果新增传参异常，请联系管理员！");
      }
      if (Objects.equals(relativeKeyType, SupplierRelativeKeyType.SRM)) {
        supplier =
            supplierRepository
                .findById(relativeKey)
                .filter(s -> Objects.equals(s.getState(), Constants.COMMONSTATE_OK))
                .orElseThrow(() -> CheckException.noFindException(Supplier.class, relativeKey));
      } else if (Objects.equals(relativeKeyType, SupplierRelativeKeyType.MDM)) {
        //  根据 MDM 编码查找，并打上供应商标签
        // 由调用方判断，判断依据：存在 MDM 编码
        // 若组织内供应商不需要审核，则直接打上标签
        // 若需要审核，则待通过审核后再打
        PartnerDTO partner =
            xhgjService
                .getPartnerByCode(relativeKey)
                .orElseThrow(
                    () ->
                        new CheckException("无法根据主数据编码【" + relativeKey + "】在 MDM 找到合作商，请核实或联系管理员！"));
        supplier = createByPartner(partner, user, group, supplierTemplateTypeEnum.getType(), false);
        supplier.setPayType(dto.getPayType());
        supplier.setPayTypeOther(dto.getPayTypeOther());
        if (dto instanceof ChinaSupplierDTO) {
          supplier.setRegion(((ChinaSupplierDTO) dto).getRegion());
        }
      } else if (Objects.equals(relativeKeyType, SupplierRelativeKeyType.TIAN_YAN_CHA)) {
        // 2022-11-07 为避免天眼查接口模糊查询特性导致用户输入错别字的情况下
        // 绑定到实际上已经存在于 SRM 或 MDM 中存在的供应商，此处需要使用前端提交来的企业名称额外验证
        List<? extends BaseSupplierSearchResult> repeatList;
        if (dto instanceof ChinaSupplierDTO) {
          repeatList = supplierInGroupService.searchChinaSupplier(relativeKey, group);
        } else {
          repeatList = supplierInGroupService.searchAbroadSupplier(relativeKey, group);
        }
        Optional<? extends BaseSupplierSearchResult> first =
            repeatList.stream()
                // 排除天眼查结果
                .filter(
                    r ->
                        !Objects.equals(
                            r.getRelativeKeyType(), SupplierRelativeKeyType.TIAN_YAN_CHA))
                // 确认是否有名称完全相符的
                .filter(r -> Objects.equals(r.getName(), relativeKey))
                .findFirst();
        if (first.isPresent()) {
          // 如果有完全相符的，再确认是否已存在于当前组织
          BaseSupplierSearchResult baseSupplierSearchResult = first.get();
          if (!StringUtils.isNullOrEmpty(baseSupplierSearchResult.getSupplierInGroupId())) {
            throw new CheckException(
                "【" + baseSupplierSearchResult.getRelativeKey() + "】已存在于当前组织，请仔细核对！");
          }
          // 将关键信息替换为完全匹配的
          dto.setRelativeKey(baseSupplierSearchResult.getRelativeKey());
          dto.setRelativeKeyType(baseSupplierSearchResult.getRelativeKeyType());
          return createSuppler(dto, user, group);
        }
        // 根据天眼查标识获取合作商信息
        // 业务审核通过后，根据【state 正常但没有主数据编码】为判断依据，在 MDM 新增合作商
        PartnerDTO partner =
            xhgjService
                .getPartnerByTianYanCha(relativeKey)
                .orElseThrow(
                    () ->
                        new CheckException(
                            "无法根据标识【" + relativeKey + "】在 MDM 找到企业天眼查信息，请核实或联系管理员！"));
        supplier = createByPartner(partner, user, group, supplierTemplateTypeEnum.getType(), true);
      } else {
        throw new CheckException("未知关联类型【" + relativeKeyType + "】，请联系管理员！");
      }
    }
    if (!Objects.equals(relativeKeyType, SupplierRelativeKeyType.MDM)) {
      // 若原供应商的省市信息缺失，则从前端参数中读取
      if (StringUtils.isNullOrEmpty(supplier.getCity())
          || StringUtils.isNullOrEmpty(supplier.getProvince())) {
        if (dto instanceof ChinaSupplierDTO) {

          if (StringUtils.isNullOrEmpty(province)) {
            throw new CheckException("省份必填！");
          }
          if (StringUtils.isNullOrEmpty(city)) {
            throw new CheckException("城市必填！");
          }
        }
        supplier.setProvince(province);
        supplier.setCity(city);
      }
      // 行业同上
      if (StringUtils.isNullOrEmpty(supplier.getIndustry())) {
        if (dto instanceof ChinaSupplierDTO) {
          if (StringUtils.isNullOrEmpty(industry)) {
            throw new CheckException("行业必填！");
          }
        }
        supplier.setIndustry(industry);
      }
      supplier.setPayType(dto.getPayType());
      supplier.setPayTypeOther(dto.getPayTypeOther());
      if (dto instanceof ChinaSupplierDTO) {
        supplier.setRegion(((ChinaSupplierDTO) dto).getRegion());
      }
      // 保存供应商网址信息
      if(CollUtil.isNotEmpty(dto.getWebsiteRegistrationList())){
        // 直接转换（空集合会转为 "[]"）
        String json = JSON.toJSONString(dto.getWebsiteRegistrationList());
        supplier.setWebsiteRegistration(json);
      }
      if(!StringUtils.isNullOrEmpty(dto.getWebsiteRegistrationSyncTime())){
        supplier.setWebsiteRegistrationSyncTime(dto.getWebsiteRegistrationSyncTime());
      }
      supplierRepository.save(supplier);
    }
    return supplier;
  }

  private Supplier createByPartner(
      PartnerDTO dto, User user, Group group, String type, boolean needCheckMdm) {
    Supplier supplier;
    String mdmCode = dto.getMdmCode();
    Supplier supplierByMdmCode =
        Optional.ofNullable(mdmCode)
            .filter(StrUtil::isNotBlank)
            .map(dao::getByMdmCode)
            .orElse(null);
    if(supplierByMdmCode!=null){
      supplier = supplierByMdmCode;
      if(!Objects.equals(type,supplier.getSupType())){
        throw new CheckException("该供应商主数据异常，供应商类型不匹配（如之前是国内供应商，但现在变成了国际供应商），请联系技术人员进一步沟通处理方案！");
      }
    }else{
      //  需要再次确认该供应商在 SRM、MDM 和天眼查内不存在
      validateNameBeforeAdd(dto.getPartnerName(), type, needCheckMdm);
      supplier = new Supplier();
      supplier.setCreateGroup(group.getName());
      supplier.setCreateCode(group.getCode());
      supplier.setCreateMan(user.getId());
      supplier.setCreateTime(System.currentTimeMillis());
      supplier.setSupType(type);
      supplier.setMdmCode(mdmCode);
      supplier.setState(Constants.COMMONSTATE_OK);
    }
    supplier.setEnterpriseName(dto.getPartnerName());
    supplier.setUscc(dto.getCreditCode());
    supplier.setIndustry(dto.getIndustry());
    supplier.setCountry(dto.getCountry());
    supplier.setCity(dto.getCity());
    supplier.setProvince(dto.getProvince());
    return supplierRepository.save(supplier);
  }

  @Override
  public Supplier createPersonSupplier(PersonSupplierDTO dto, User user, Group group) {
    Assert.notNull(dto);
    Assert.notNull(user);
    Assert.notNull(group);
    String personName = dto.getPersonName();
    String mobile = dto.getMobile();
    if (StrUtil.isBlank(personName) || StrUtil.isBlank(mobile)) {
      throw new CheckException("个人供应商姓名或联系方式不能为空！");
    }
    Supplier supplier = dao.getSupplierByEnterNameAndMobile(personName, mobile);
    // 如果该个人供应商不存在就创建
    if (supplier == null) {
      supplier = new Supplier();
      PartnerDTO partnerDTO = xhgjService.getPersonPartner(personName, mobile).orElse(null);
      supplier.setEnterpriseName(personName);
      supplier.setMobile(mobile);
      supplier.setCreateGroup(group.getName());
      supplier.setCreateCode(group.getCode());
      supplier.setCreateMan(user.getId());
      supplier.setCreateTime(System.currentTimeMillis());
      supplier.setState(Constants.COMMONSTATE_CHECKING);
      supplier.setSupType(Constants.SUPPLIERTYPE_PERSONAL);
      supplier.setCountry("中国");
      save(supplier);
      if (partnerDTO != null) {
        if (ObjectUtil.notEqual(partnerDTO.getPartnerName(), personName)
            || ObjectUtil.notEqual(partnerDTO.getMobile(), mobile)) {
          throw new CheckException(
              "联系方式和姓名与 MDM 不匹配，姓名：【"
                  + partnerDTO.getPartnerName()
                  + "】，联系方式：【"
                  + partnerDTO.getMobile()
                  + "】");
        }
        supplier.setState(Constants.STATE_OK);
        supplier.setMdmCode(partnerDTO.getMdmCode());
        xhgjService.markPartnerSupplier(partnerDTO.getMdmCode());
      } else {
        // 主数据审核记录
        Assess supplierMainAddAssess =
            assessService.createSupplierMainAddAssess(supplier.getId(), user.getId(), group);
        String mdmAssessId = xhgjService.createPartnerAssess(supplier, user);
        supplierMainAddAssess.setExternalId(mdmAssessId);
        assessService.save(supplierMainAddAssess);
      }
    }
    return supplier;
  }

  @Override
  public PageResult<SupplerChangeInfoDTO> getSupplierChangeInfo(
      String supplierId, String userGroup, int pageNo, int pageSize) {
    PageResult<SupplierChangeInfo> pageResult =
        PageResultBuilder.buildPageResult(
            supplierChangeInfoDao.getSupplierChangeInfoPage(
                supplierId, userGroup, pageNo, pageSize));
    return pageResult.map(
        supplierChangeInfo -> {
          String updateMan;
          String updateManId =
              Optional.ofNullable(supplierChangeInfo.getSupplierFb())
                  .map(SupplierFb::getCreateMan)
                  .orElse(StrUtil.EMPTY);
          updateMan =
              Optional.ofNullable(userService.get(updateManId))
                  .map(User::getRealName)
                  .orElse(StrUtil.EMPTY);
          return new SupplerChangeInfoDTO(supplierChangeInfo, updateMan);
        });
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void updateSupplierMainData(BaseSupplierMainDataDTO dto, User user, Group group) {
    String id = dto.getId();
    Supplier supplier = get(id, () -> CheckException.noFindException(Supplier.class, id));
    Assess assessing = assessService.getFirstAssessingByTargetId(id);
    if (assessing != null) {
      String assessType = assessing.getAssessType();
      AssessTypeEnum assessTypeEnum =
          BootDictEnumUtil.getEnumByKey(AssessTypeEnum.class, assessType)
              .orElseThrow(
                  () -> new CheckException("存在进行中的审核【" + assessing.getId() + "】，但类型异常，请联系管理员！"));
      throw new CheckException("该供应商存在进行中的【" + assessTypeEnum.getValue() + "】审核，无法进行编辑操作！");
    }
    SupplierTemp temp = supplierTempService.createByDTO(dto, group);
    SupplierTemp oldTemp = supplierTempService.createBySupplier(supplier);
    Assess assess =
        assessService.createSupplierMainUpdateAssess(
            temp.getId(), supplier.getId(), oldTemp.getId(), user.getId(), group);
    Supplier updateSupplier = new Supplier();
    MapStructFactory.INSTANCE.updateSupplier(temp, updateSupplier, null);
    updateSupplier.setMdmCode(supplier.getMdmCode());
    String mdmAssessId = xhgjService.createPartnerAssess(updateSupplier, user);
    assess.setExternalId(mdmAssessId);
    assessService.save(assess);
  }

  @Override
  public SupplierMainDataChinaDTO getChinaSupplierByAssess(String assessId) {
    BaseSupplier supplier = getBaseSupplierInGroupByAssess(assessId);
    return new SupplierMainDataChinaDTO(supplier);
  }

  @Override
  public SupplierMainDataAbroadDTO getAbroadSupplierByAssess(String assessId) {
    BaseSupplier supplier = getBaseSupplierInGroupByAssess(assessId);
    return new SupplierMainDataAbroadDTO(supplier);
  }

  @Override
  public void makeSupplierReal(String id, String mdmCode) {
    if (StringUtils.isNullOrEmpty(mdmCode)) {
      throw new CheckException("MDM 主数据编码缺失！");
    }
    Supplier supplier = get(id, () -> CheckException.noFindException(Supplier.class, id));
    if (!Objects.equals(supplier.getState(), Constants.COMMONSTATE_CHECKING)) {
      throw new CheckException("供应商状态异常，可能已经审核过，请核实或联系管理员！");
    }
    supplier.setState(Constants.STATE_OK);
    supplier.setMdmCode(mdmCode);
    supplierRepository.save(supplier);
    // 推送所有关联组织的供应商（99% 只会获取到一个供应商）
    supplierInGroupService
        .getAllInGroupERPCodeBySupplier(id)
        .forEach(
            groupCode ->
                xhgjService.createSupplierAddTask(
                    buildDockSupplier(id, groupCode, supplier.getCreateMan())));
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void updateSupplierInGroupByTemp(String id, String tempId, String assessCreateMan) {
    Supplier target = get(id, () -> CheckException.noFindException(Supplier.class, id));
    SupplierTemp temp =
        supplierTempService.get(
            tempId, () -> CheckException.noFindException(SupplierInGroupTemp.class, tempId));
    changeRecordService.createChangeRecordInBatch(
        supplierInGroupService.getAllInGroupIdBySupplier(target.getId()),
        target,
        temp,
        assessCreateMan);
    MapStructFactory.INSTANCE.updateSupplier(temp, target, IgnoreFieldContext.create("id"));
    supplierRepository.save(target);
    if (Constants.SUPPLIER_TYPE_CHINA_ABROAD_LIST.contains(target.getSupType())) {
      //srm修改供应商企业名称，mdm审核通过后账户名称更新
      financialService.updateFinancialRecord(id,target.getEnterpriseName());
      // 同步MDM任务，然后MDM同步至SAP
      xhgjDockRequest.createSupplierAddTask(
          shareSupplierInGroupService.buildDockSupplierWithBank(target.getId()));
    }
  }

  @Override
  public void makeSupplierDelete(String id) {
    Supplier supplier = get(id, () -> CheckException.noFindException(Supplier.class, id));
    boolean hasMDMCode = StrUtil.isNotBlank(supplier.getMdmCode());
    if (!Objects.equals(supplier.getState(), Constants.COMMONSTATE_CHECKING) && hasMDMCode) {
      throw new CheckException("供应商状态异常，可能已经审核过，请核实或联系管理员！");
    }
    supplier.setState(Constants.STATE_DELETE);
    supplierRepository.save(supplier);
  }

  @Override
  public void validateNameBeforeAdd(String name, String type, boolean needCheckMdm) {
    if (StrUtil.hasBlank(name, type)) {
      throw new CheckException("参数缺失，请核实或联系管理员！");
    }
    if (!Constants.SUPPLIERTYPE.containsKey(type)) {
      throw new CheckException("供应商类型参数【" + type + "】异常，请联系管理员！");
    }
    String trimName = StrUtil.trimToNull(name);
    // 1. 验证 SRM 是否存在正常的
    if (!StringUtils.isNullOrEmpty(
        dao.getIdByNameOrUsedNameAndState(trimName, type, Constants.COMMONSTATE_OK))) {
      throw new CheckException("已存在供应商，无法重复新增");
    }
    // 2. 验证 SRM 是否存在审核中的
    String checkingSupplierId =
        dao.getIdByNameOrUsedNameAndState(trimName, type, Constants.COMMONSTATE_CHECKING);
    if (!StringUtils.isNullOrEmpty(checkingSupplierId)) {
      Assess assessing = assessService.getFirstAssessingByTargetId(checkingSupplierId);
      if (assessing != null) {
        String assessTypeName =
            BootDictEnumUtil.getValueByKey(AssessTypeEnum.class, assessing.getAssessType());
        throw new CheckException("该供应商" + assessTypeName + "审核中，请稍后再试");
      } else {
        log.error("供应商【" + checkingSupplierId + "】状态为审核中但无法找到对应的审核数据！");
        throw new CheckException("该供应商审核中，但无法获取审核类型，请联系管理员！");
      }
    }
    // 3.  验证 MDM 是否存在
    if (needCheckMdm) {
      xhgjService
          .getPartnerByName(trimName, type)
          .ifPresent(
              (o) -> {
                throw new CheckException("该供应商存在于 MDM，请稍后再试");
              });
    }
  }

  @Override
  public void synSupplierInGroup() {
    List<Supplier> suppliers = dao.getNormalAndBlackEnterprise();
    if (CollUtil.isNotEmpty(suppliers)) {
      for (Supplier supplier : suppliers) {
        SupplierInGroup supplierInGroup =
            supplierInGroupDao.getByGroupAndSupplier(supplier.getId(), Constants.GROUP_WANJU_CODE);
        if (supplierInGroup == null) {
          try {
            supplierInGroup = new SupplierInGroup();
            supplierInGroup.setSupplier(supplier);
            supplierInGroup.setSupplierId(supplier.getId());
            supplierInGroup.setCreateMan(supplier.getCreateMan());
            supplierInGroup.setCreateTime(supplier.getCreateTime());
            supplierInGroup.setAuditState(Constants.AUDIT_STATE_MANAGESUCCESS);
            supplierInGroup.setState(supplier.getState());
            supplierInGroup.setSynState(supplier.getSynState());
            // 基本信息
            supplierInGroup.setLicenseUrl(StringUtils.emptyIfNull(supplier.getLicenseUrl()));
            supplierInGroup.setDetails(StringUtils.emptyIfNull(supplier.getDetails()));
            supplierInGroup.setEnterpriseLevel(supplier.getEnterpriseLevel());
            supplierInGroup.setEnterpriseNature(supplier.getEnterpriseNature());
            supplierInGroup.setIntegrity(StringUtils.emptyIfNull(supplier.getIntegrity()));
            // erp信息
            supplierInGroup.setErpCode(StringUtils.emptyIfNull(supplier.getErpCode()));
            supplierInGroup.setErpSuccess(StringUtils.emptyIfNull(supplier.getErpSuccess()));
            supplierInGroup.setErpId(StringUtils.emptyIfNull(supplier.getErpid()));
            // 组织信息
            Group group = groupDao.getCurGroupByErpCode(Constants.GROUP_WANJU_CODE);
            supplierInGroup.setGroup(group);
            supplierInGroup.setGroupId(group.getId());
            // 采购信息
            supplierInGroup.setManageId(supplier.getManageId());
            if (!StringUtils.isNullOrEmpty((supplier.getPurchaserId()))) {
              User user = userRepository.findById(supplier.getPurchaserId()).orElse(null);
              if (user != null) {
                supplierInGroup.setPurchaser(user);
              }
            }
            // 拉黑信息
            supplierInGroup.setReason(StringUtils.emptyIfNull(supplier.getReason()));
            supplierInGroup.setShieldingPeople(
                StringUtils.emptyIfNull(supplier.getShieldingPeople()));
            supplierInGroup.setShieldManager(StringUtils.emptyIfNull(supplier.getShieldManager()));
            supplierInGroup.setShieldState(StringUtils.emptyIfNull(supplier.getShieldState()));
            // 统一财务信息
            supplierInGroup.setTaxRate(StringUtils.emptyIfNull(supplier.getTaxRate()));
            supplierInGroup.setAccountPeriod(StringUtils.emptyIfNull(supplier.getAccountPeriod()));
            supplierInGroup.setInvoiceType(StringUtils.emptyIfNull(supplier.getInvoiceType()));
            supplierInGroup.setSettleCurrency(
                StringUtils.emptyIfNull(supplier.getSettleCurrency()));
            supplierInGroupRepository.save(supplierInGroup);
            // 工商信息
            SupplierBizInfo supplierBizInfo = new SupplierBizInfo();
            supplierBizInfo.setSupplierId(StringUtils.emptyIfNull(supplier.getId()));
            supplierBizInfo.setCorporate(StringUtils.emptyIfNull(supplier.getCorporate()));
            supplierBizInfo.setBusinessScope(StringUtils.emptyIfNull(supplier.getBusinessScope()));
            supplierBizInfo.setEnglishName(StringUtils.emptyIfNull(supplier.getEnglishName()));
            supplierBizInfo.setInsNum(StringUtils.emptyIfNull(supplier.getInsNum()));
            supplierBizInfo.setManageType(StringUtils.emptyIfNull(supplier.getManageType()));
            supplierBizInfo.setPaidCapital(StringUtils.emptyIfNull(supplier.getPaidCapital()));
            supplierBizInfo.setPeopleNum(StringUtils.emptyIfNull(supplier.getPeopleNum()));
            supplierBizInfo.setRegAddress(StringUtils.emptyIfNull(supplier.getRegAddress()));
            supplierBizInfo.setRegAuthority(StringUtils.emptyIfNull(supplier.getRegAuthority()));
            supplierBizInfo.setRegNo(StringUtils.emptyIfNull(supplier.getRegNo()));
            supplierBizInfo.setTaxNumber(StringUtils.emptyIfNull(supplier.getTaxNumber()));
            supplierBizInfo.setTaxQualification(
                StringUtils.emptyIfNull(supplier.getTaxQualification()));
            supplierBizInfo.setStartDate(supplier.getStartDate());
            supplierBizInfo.setEndDate(supplier.getEndDate());
            supplierBizInfo.setDate(supplier.getDate());
            bizInfoRepository.save(supplierBizInfo);
          } catch (Exception e) {
            log.error("同步【" + supplier.getEnterpriseName() + "】供应商失败：" + e);
          }
        }
      }
    }
  }

  @Override
  public void synSupplierInGroupFile() {
    List<Supplier> suppliers = dao.getNormalAndBlackEnterprise();
    if (CollUtil.isNotEmpty(suppliers)) {
      for (Supplier supplier : suppliers) {
        SupplierInGroup supplierInGroup =
            supplierInGroupDao.getByGroupAndSupplier(supplier.getId(), Constants.GROUP_WANJU_CODE);
        if (supplierInGroup != null) {
          try {
            // 经营品牌
            brandDao.updateBrandSupplierInGroup(supplierInGroup.getId(), supplier.getId());
            // 财务信息
            financialDao.updateFinancialSupplierInGroup(supplierInGroup.getId(), supplier.getId());
            // 联系人
            contactDao.updateContactSupplierInGroup(supplierInGroup.getId(), supplier.getId());
            // 代理证书
            fileDao.updateFileSupplierInGroup(
                supplierInGroup.getId(), supplier.getId(), Constants.FILE_TYPE_DLZS);
            // 协议
            String xyFileType = null;
            if (!StringUtils.isNullOrEmpty(supplier.getEnterpriseLevel())
                && (SupplierLevelEnum.STRATEGIC.getCode().equals(supplier.getEnterpriseLevel())
                    || SupplierLevelEnum.HIGH_QUALITY.getCode().equals(supplier.getEnterpriseLevel())
                    || SupplierLevelEnum.GENERAL.getCode().equals(supplier.getEnterpriseLevel()))) {
              // 采购协议
              xyFileType = Constants.FILE_TYPE_CGXY;
            }
            fileDao.updateFileSupplierInGroup(
                supplierInGroup.getId(), supplier.getId(), xyFileType);
            // 附件
            for (String type : Constants.FILE_TYPE_TO_NAME.keySet()) {
              fileDao.updateFileSupplierInGroup(supplierInGroup.getId(), supplier.getId(), type);
            }
            // 自定义附件
            extraFileDao.updateExtraFileSupplierInGroup(supplierInGroup.getId(), supplier.getId());
          } catch (Exception e) {
            log.error("同步【" + supplier.getEnterpriseName() + "】供应商附件失败：" + e);
          }
        }
      }
    }
  }

  @Override
  public DockSupplier buildDockSupplier(String id, Group group, String noticePersonId) {
    Supplier supplier = get(id, () -> CheckException.noFindException(Supplier.class, id));
    DockSupplierBuilder builder = DockSupplier.builder();
    builder
        .mdmCode(supplier.getMdmCode())
        .name(supplier.getEnterpriseName())
        .country(
            StrUtil.isNotEmpty(supplier.getCountry())
                ? xhgjService.getCountryCodeByName(supplier.getCountry())
                : null)
        .province(
            StrUtil.isNotEmpty(supplier.getProvince())
                ? ProvinceSAPEnum.getCodeByName(supplier.getProvince())
                : null)
        // 20240717 个人供应商该字段传联系方式
        .uscc(Objects.equals(
            supplier.getSupType(),Constants.SUPPLIERTYPE_PERSONAL) ?
            supplier.getMobile() : supplier.getUscc())
        .paymentType(
            StrUtil.isNotEmpty(supplier.getPayType())
                ? PayTypeSAPEnums.getCodeByName(supplier.getPayType())
                : null)
        .supType(supplier.getSupType())
        .groupCode(group.getErpCode());
    if (group != null) {
      supplierInGroupService.fillDockSupplierInGroupData(id, group, builder, noticePersonId);
    }
    return builder.build();
  }

  @Override
  public DockSupplier buildDockSupplier(String id, String groupCode, String noticePersonId) {
    Group group;
    if (!StringUtils.isNullOrEmpty(groupCode)) {
      group = groupService.getGroupByErpCode(groupCode);
    } else {
      group = null;
    }
    return shareSupplierInGroupService.buildDockSupplier(id, group, noticePersonId);
  }

  @Transactional(rollbackFor = Exception.class)
  @Override
  public void mdmUpdateSupplierMainData(UpdatePartnerDTO partnerDTO) {
    String mdmCode = partnerDTO.getMdmCode();
    Supplier supplier = dao.getByMdmCode(mdmCode);
    if (supplier != null) {
      changeRecordService.createChangeRecordInBatch(
          supplierInGroupService.getAllInGroupIdBySupplier(supplier.getId()),
          supplier,
          partnerDTO,
          null);
      supplier.setEnterpriseName(partnerDTO.getPartnerName());
      supplier.setUscc(partnerDTO.getCreditCode());
      supplier.setProvince(partnerDTO.getProvince());
      supplier.setCity(partnerDTO.getCity());
      supplier.setMobile(partnerDTO.getMobile());
      supplier.setCountry(partnerDTO.getCountry());
      supplier.setIndustry(partnerDTO.getIndustry());
      save(supplier);
      if (Constants.SUPPLIER_TYPE_CHINA_ABROAD_PERSONAL_LIST.contains(supplier.getSupType())) {
        //crm发起的客户数据修改或mdm修改供应商企业名称，mdm审核通过后账户名称更新
        financialService.updateFinancialRecord(supplier.getId(),supplier.getEnterpriseName());
        // 同步MDM任务，然后MDM同步至SAP
        xhgjDockRequest.createSupplierAddTask(
            shareSupplierInGroupService.buildDockSupplierWithBank(supplier.getId()));
      }
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public BusinessInfoDTO sysnTianYan(User user, String supplierId) {
    Supplier supplier =
        get(supplierId, () -> CheckException.noFindException(Supplier.class, supplierId));
    String mdmCode = supplier.getMdmCode();
    if (StrUtil.isBlank(mdmCode)) {
      throw new CheckException("【" + supplierId + "】 mdm 编码缺失，请联系管理员");
    }
    BusinessInfoDTO infoDTO =
        xhgjService
            .syncTianYan(user.getRealName(), mdmCode)
            .orElseThrow(() -> new CheckException("同步天眼查无数据"));
    supplier.setManageType(infoDTO.getRegStatus());
    supplier.setStartDate(infoDTO.getFromTime());
    supplier.setEndDate(infoDTO.getToTime());
    supplier.setPaidCapital(infoDTO.getActualCapital());
    supplier.setInsNum(infoDTO.getSocialStaffNum());
    supplier.setTaxNumber(infoDTO.getTaxNumber());
    supplier.setUsedName(infoDTO.getHistoryNames());
    supplier.setRegAuthority(infoDTO.getRegInstitute());
    supplier.setOrgCode(infoDTO.getOrgNumber());
    supplier.setPeopleNum(infoDTO.getStaffNumRange());
    supplier.setBusinessScope(infoDTO.getBusinessScope());
    supplier.setEnglishName(infoDTO.getProperty3());
    supplier.setRegNo(infoDTO.getRegNumber());
    supplier.setCorporate(infoDTO.getLegalPersonName());
    supplier.setDate(infoDTO.getEstiblishTime());
    supplier.setRegCapital(infoDTO.getRegCapital());
    supplier.setRegAddress(infoDTO.getRegLocation());
    supplier.setIndustry(infoDTO.getIndustry());
    supplierRepository.save(supplier);
    return infoDTO;
  }

  @Override
  public BusinessInfoDTO getTianYanInfo(String supplierId, String supplierName) {
    if (StrUtil.isBlank(supplierId) && StrUtil.isBlank(supplierName)) {
      throw new CheckException("参数非法");
    }
    String mdmCode;
    String name;
    if (StrUtil.isNotEmpty(supplierId)) {
      Supplier supplier =
          get(supplierId, () -> CheckException.noFindException(Supplier.class, supplierId));
      if(Objects.equals(supplier.getState(),Constants.COMMONSTATE_OK)){
        mdmCode = supplier.getMdmCode();
        if (StrUtil.isBlank(mdmCode)) {
          throw new CheckException("【" + supplierId + "】 mdm 编码缺失，请联系管理员");
        }
        name = StrUtil.EMPTY;
      }else{
        mdmCode = StrUtil.EMPTY;
        name = supplier.getEnterpriseName();
      }
    } else {
      mdmCode = StrUtil.EMPTY;
      name = supplierName;
    }
    return xhgjService.getTianYanInfo(mdmCode, name).orElse(new BusinessInfoDTO());
  }

  @Override
  public SupplierMainDataPersonDTO getPersonSupplierMainDataByAssess(String assessId) {
    BaseSupplier supplier = getBaseSupplierInGroupByAssess(assessId);
    return new SupplierMainDataPersonDTO(supplier);
  }

  @Override
  public Supplier getByMdmCode(String mdmCode) {
    return supplierRepository.getFirstByMdmCodeAndState(mdmCode, Constants.STATE_OK);
  }

  @Override
  public String getEnterpriseNameByMdmCode(String mdmCode) {
    return dao.getNameByMdmCode(mdmCode);
  }

  @Override
  public List<Supplier> getCreteCodeIsEmpty() {
    return dao.getCreteCodeIsEmpty();
  }

  @Override
  public void handleSupplierMdmCode() {
    List<Supplier> allMdmCodeIsNull =
        CollUtil.emptyIfNull(supplierRepository.findAllByMdmCodeIsNullAndState(Constants.STATE_OK));
    List<String> failList = new ArrayList<>();
    allMdmCodeIsNull.forEach(
        supplier -> {
          String enterpriseName = supplier.getEnterpriseName();
          String type = supplier.getSupType();
          try {
            PartnerDTO partnerDTO = xhgjService.getPartnerByName(enterpriseName, type).orElse(null);
            if (partnerDTO != null) {
              supplier.setMdmCode(partnerDTO.getMdmCode());
              save(supplier);
              if (StrUtil.isNotEmpty(supplier.getIntegrity())
                  && Integer.parseInt(supplier.getIntegrity()) < 90) {
                supplier.setProvince(partnerDTO.getProvince());
                supplier.setCity(partnerDTO.getCity());
                supplier.setIndustry(partnerDTO.getIndustry());
                supplier.setUscc(partnerDTO.getCreditCode());
                save(supplier);
              }
            } else {
              failList.add(enterpriseName);
            }
          } catch (Exception e) {
            failList.add(enterpriseName);
          }
        });
    if (CollUtil.isNotEmpty(failList)) {
      log.error("同步 mdm 编码失败的供应商：");
      log.error(JSON.toJSONString(failList));
      throw new CheckException("同步失败的供应商【" + CollUtil.join(failList, ",") + "】");
    }
  }

  /**
   * 根据审核 id 获取供应商基类实体，由审核类型确认
   *
   * @param assessId 审核 id，必传
   */
  private BaseSupplier getBaseSupplierInGroupByAssess(String assessId) {
    if (StringUtils.isNullOrEmpty(assessId)) {
      throw new CheckException("审核 id 必传！");
    }
    Assess assess =
        assessService.get(assessId, () -> CheckException.noFindException(Assess.class, assessId));
    AssessTypeEnum assessTypeEnum =
        BootDictEnumUtil.getEnumByKey(AssessTypeEnum.class, assess.getAssessType())
            .orElseThrow(() -> new CheckException("审核【" + assessId + "】的类型异常，请联系管理员！"));
    String id;
    BaseSupplier baseSupplier;
    if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_MAIN_ADD)) {
      // 新增，根据审核目标 id 获取主表实体对象
      id = assess.getTargetId();
      baseSupplier = get(id, () -> CheckException.noFindException(Supplier.class, id));
    } else if (Objects.equals(assessTypeEnum, AssessTypeEnum.SUPPLIER_MAIN_UPDATE)) {
      // 修改，根据审核源 id 获取副本表实体对象
      id = assess.getSourceId();
      baseSupplier =
          supplierTempService.get(
              id, () -> CheckException.noFindException(SupplierInGroupTemp.class, id));
    } else {
      throw new CheckException("传入审核【" + assessId + "】的类型异常，请联系管理员！");
    }
    return baseSupplier;
  }


  @Override
  public String getCodeBySupplierId(String id) {
    Assert.notEmpty(id);
    return getOptional(id)
        .map(Supplier::getMdmCode)
        .orElseThrow(() -> new CheckException("未找到对应的供应商Id！"));
  }

  @SneakyThrows
  @Override
  public void importLandingMerchantPerformanceInfo(MultipartFile file, String userId) {
    Assert.notEmpty(userId);
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    if (user == null) {
      throw new CheckException("用户为空");
    }
    if (file != null) {
      String fileName = file.getOriginalFilename();
      if (ObjectUtils.isEmpty(fileName) || fileName.length() == 0) {
        throw new CheckException("文件异常,请查看文件");
      }
      // 设置任务编号
      // 新增任务
      Mission mission = Mission.createStartingMission(
          missionUtil.getMissionCode(user.getCode()),
          "导入-落地商履约信息",
          user.getId(),
          Constants.PLATFORM_TYPE_AFTER,
          file.getOriginalFilename(),
          importExcelUtil.saveExcel(file));
      missionRepository.save(mission);
      batchTaskMqSender.toHandleBatchTask(
          mission.getId(), null, Constants_Batch.BATCH_TASK_LANDING_MERCHANT_PERFORMANCE_INFO_IN);
    } else {
      throw new CheckException("无上传文件");
    }
  }

  @Override
  public Optional<List<Supplier>> findAllBySupplierByMDMCode(String mdmCode) {
    if (StrUtil.isBlank(mdmCode)) {
      return Optional.empty();
    }
    return supplierRepository.findAllByMdmCodeAndState(mdmCode, Constants.STATE_OK);
  }

  @Override
  public Optional<Supplier> findFirstByEnterpriseName(String enterpriseName) {
    if (StrUtil.isBlank(enterpriseName)) {
      return Optional.empty();
    }
    return supplierRepository.findFirstByEnterpriseNameAndState(enterpriseName, Constants.STATE_OK);
  }

  @Override
  public void saveProvisionalSupplier(User user, ProvisionalSupplierParam param) {
    // admin用户权限才能进行操作
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)
        || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      String supplierId = param.getSupplierId();
      String mdmCode = param.getMdmCode();
      String supplierName = param.getSupplierName();
      Supplier supplier;
      Supplier supplierByName = dao.getByMdmCodeOrName(StrUtil.EMPTY, supplierName);
      if (ObjectUtil.isNotEmpty(supplierByName)) {
        throw new CheckException("企业名称已存在");
      }
      if (StrUtil.isBlank(supplierId)) {
        Supplier supplierByMdmCode = getByMdmCode(mdmCode);
        if (ObjectUtil.isNotEmpty(supplierByMdmCode)) {
          throw new CheckException("MDM编码已存在");
        }
        supplier = new Supplier();
        supplier.setMdmCode(mdmCode);
        supplier.setCreateTime(System.currentTimeMillis());
        supplier.setCreateMan(user.getId());
        supplier.setSupType(Constants.SUPPLIER_TYPE_PROVISIONAL);
      } else {
        supplier =
            Optional.ofNullable(get(supplierId))
                .orElseThrow(() -> new CheckException("无法找到修改的供应商" + supplierId + "信息！"));
      }
      supplier.setState(Constants.STATE_OK);
      supplier.setEnterpriseName(supplierName);
      save(supplier);
    } else {
      throw new CheckException("只有admin账号可以进行添加一次性供应商！");
    }
  }

  @Override
  public PageResult<ProvisionalSupplierPageDTO> getProvisionalSupplierPage(
      User user, ProvisionalSupplierPageQuery query, Pageable toPageable) {
    String schemeId = query.getSchemeId();
    String supplierName = query.getEnterpriseName();
    String mdmCode = query.getMdmCode();
    Long createTimeStart = query.getStartTime();
    Long createTimeEnd = query.getEndTime();
    // 查询方案
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeDao.getDefaultSearchScheme(
              user.getId(), Constants.SEARCH_TYPE_SUPPLIER_PROVISIONAL_PAGE);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeDao.get(schemeId);
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          mdmCode = StrUtil.blankToDefault(mdmCode,searchJo.containsKey("mdmCode") ?
              searchJo.getString(
              "mdmCode") : "");
          supplierName =
              StrUtil.blankToDefault(supplierName,searchJo.containsKey("enterpriseName") ?
                  searchJo.getString("enterpriseName") : "");
          createTimeStart = ObjectUtil.defaultIfNull(createTimeStart,
              searchJo.containsKey("startTime") ? searchJo.getLong("startTime") : null);
          createTimeEnd = ObjectUtil.defaultIfNull(createTimeEnd,searchJo.containsKey("endTime") ? searchJo.getLong("endTime") : null);
        }
      }
    }

    return PageResultBuilder.buildPageResult(
        dao.getProvisionalSupplierPage(
            Constants.SUPPLIER_TYPE_PROVISIONAL,
            supplierName,
            mdmCode,
            createTimeStart,
            createTimeEnd,
            toPageable),
        ProvisionalSupplierPageDTO::new);
  }

  @Override
  public void deleteProvisionalSupplier(User user, DeleteProvisionalSupplierParam param) {
    List<String> supplierIdList = param.getSupplierIdList();
    // admin用户权限才能进行操作
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)
        || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      for (String supplierId : supplierIdList) {
        Supplier supplier =
            Optional.ofNullable(get(supplierId))
                .orElseThrow(() -> new CheckException("无法找到" + supplierId + "供应商信息！"));
        delete(supplier.getId());
      }
    } else {
      throw new CheckException("只有admin账号可以进行添加一次性供应商！");
    }
  }

  @Override
  public void updateBlockRange(String id, String blockRange) {
    Assert.notBlank(id);
    Assert.notBlank(blockRange);
    SupplierInGroup supplierInGroup =
        supplierInGroupService.get(
            id, () -> CheckException.noFindException(SupplierInGroup.class, id));
    if (!Constants.COMMONSTATE_BLACKLIST.equals(supplierInGroup.getState())) {
      throw new CheckException("该供应商不处于拉黑的状态！");
    }
    SupplierBlockRangeEnum supplierBlockRangeEnum = SupplierBlockRangeEnum.fromKey(blockRange);
    if (supplierBlockRangeEnum == null) {
      throw new CheckException("拉黑范围不合法！");
    }
    supplierInGroup.setBlockRange(supplierBlockRangeEnum.getKey());
    supplierInGroupService.save(supplierInGroup);
    // TODO: 2023/12/20 调用sap新增或修改供应商接口，像“冻结”字段内传x
    Supplier supplier = supplierInGroup.getSupplier();
    Group group = supplierInGroup.getGroup();
    if (supplier == null
        || group == null
        || StrUtil.isBlank(supplier.getMdmCode())
        || StrUtil.isBlank(group.getErpCode())) {
      throw new CheckException("数据异常，请联系管理员");
    }
    boolean updateBlockRange;
    try {
      updateBlockRange =
          sapSupplierRequest.blacklistingSuppliers(
              supplier.getMdmCode(), group.getErpCode(), supplierBlockRangeEnum);
    } catch (CheckException e){
      throw e;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e));
      throw new CheckException("SAP系统响应异常，更新供应商拉黑范围失败");
    }
    if (Boolean.FALSE.equals(updateBlockRange)) {
      throw new CheckException("SAP系统响应更新供应商拉黑范围失败，请联系管理员");
    }


  }

  @Override
  public void synInteriorSupplier(User user) {
    String userId = user.getId();
    // 获取mdm同步数据
    List<InternalPartnerContentDTO> internalPartnerContentDTOS =
        mdmRequest
            .getInternalPartnerPage()
            .map(InternalPartnerPageDTO::getContent)
            .orElseThrow(() -> new CheckException("mdm同步数据不存在！"));
    // 遍历存储
    for (InternalPartnerContentDTO internalPartnerContentDTO : internalPartnerContentDTOS) {
      String mdmCode = internalPartnerContentDTO.getMdmCode();
      String city = internalPartnerContentDTO.getCity();
      String partnerName = internalPartnerContentDTO.getPartnerName();
      Long createTime = internalPartnerContentDTO.getCreateTime();
      String province = internalPartnerContentDTO.getProvince();
      String uScc = internalPartnerContentDTO.getUScc();
      // 通过mdm编码获取
      Supplier supplier = supplierRepository.getFirstByMdmCodeAndState(mdmCode, Constants.STATE_OK);
      if (ObjectUtil.isEmpty(supplier)) {
        supplier = new Supplier();
        supplier.setCreateTime(createTime);
        supplier.setCreateMan(userId);
      }
      supplier.setEditManId(userId);
      supplier.setEditTime(System.currentTimeMillis());
      supplier.setMdmCode(mdmCode);
      supplier.setSupType(Constants.SUPPLIER_TYPE_INTERNAL);
      supplier.setEnterpriseName(partnerName);
      supplier.setProvince(province);
      supplier.setCity(city);
      supplier.setUscc(uScc);
      supplier.setSynState(Constants.SUPPLIER_SYN_STATE_SUCCESS);
      supplier.setState(Constants.STATE_OK);
      save(supplier);
    }
  }

  @Override
  public PageResult<InteriorSupplierPageDTO> getInteriorSupplierPage(
      User user, ProvisionalSupplierPageQuery query, Pageable toPageable) {
    String schemeId = query.getSchemeId();
    String supplierName = query.getEnterpriseName();
    String mdmCode = query.getMdmCode();
    Long createTimeStart = query.getStartTime();
    Long createTimeEnd = query.getEndTime();
    // 查询方案
    if (StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search =
          searchSchemeDao.getDefaultSearchScheme(
              user.getId(), Constants.SEARCH_TYPE_SUPPLIER_INTERIOR_PAGE);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (!StringUtils.isNullOrEmpty(schemeId)) {
      SearchScheme search = searchSchemeDao.get(schemeId);
      if (search != null && !StringUtils.isNullOrEmpty(search.getContent())) {
        JSONObject searchJo = JSONObject.parseObject(search.getContent());
        if (searchJo != null) {
          mdmCode = StrUtil.blankToDefault(mdmCode,searchJo.containsKey("mdmCode") ? searchJo.getString(
              "mdmCode") : "");
          supplierName =
              StrUtil.blankToDefault(supplierName,searchJo.containsKey("enterpriseName") ?
                  searchJo.getString("enterpriseName") : "");
          createTimeStart =
              ObjectUtil.defaultIfNull(createTimeStart,searchJo.containsKey("startTime") ? searchJo.getLong("startTime") : null);
          createTimeEnd =  ObjectUtil.defaultIfNull(createTimeEnd,searchJo.containsKey("endTime") ? searchJo.getLong("endTime") : null);
        }
      }
    }
    Long editTime =
        Optional.ofNullable(dao.getInteriorSupplierByEditTime(Constants.SUPPLIER_TYPE_INTERNAL))
            .map(Supplier::getEditTime)
            .orElse(null);
    return PageResultBuilder.buildPageResult(
        dao.getInteriorSupplierPage(
            Constants.SUPPLIER_TYPE_INTERNAL,
            supplierName,
            mdmCode,
            createTimeStart,
            createTimeEnd,
            toPageable),
        supplier -> new InteriorSupplierPageDTO(supplier, editTime));
  }

  @Override
  public BusinessInfoDTO getSupplierInfo(String supplierId, String supplierName) {
    if (StrUtil.isBlank(supplierName)) {
      return null;
    }
    Supplier supplier =
        supplierRepository
            .findFirstByEnterpriseNameAndState(supplierName, Constants.STATE_OK)
            .orElse(null);
    if (supplier == null) {
      return xhgjService.getTianYanInfo(StrUtil.EMPTY, supplierName).orElse(new BusinessInfoDTO());
    } else {
      SupplierInGroup supplierInGroupInfo;
      Optional<List<SupplierInGroup>> supplierInGroupOptional =
          supplierInGroupRepository.findAllBySupplierIdAndState(
              supplier.getId(), Constants.STATE_OK);
      if (!supplierInGroupOptional.isPresent()) {
        supplierInGroupInfo = new SupplierInGroup();
      } else {
        List<SupplierInGroup> supplierInGroups = supplierInGroupOptional.get();
        supplierInGroupInfo = supplierInGroups.get(0);
      }
      supplierId = supplier.getId();
      BusinessInfoDTO dto = new BusinessInfoDTO();
      dto.setCompanyName(supplier.getEnterpriseName());
      dto.setAbbreviation(supplierInGroupInfo.getAbbreviation());
      dto.setCreditCode(supplier.getUscc());
      dto.setLegalPersonName(supplier.getCorporate());
      dto.setRegion(supplier.getRegion());
      dto.setRegLocation(
          StrUtil.isEmpty(supplier.getRegAddress())
              ? supplierInGroupInfo.getRegAddress()
              : supplier.getRegAddress());
      dto.setProvince(supplier.getProvince());
      dto.setCity(supplier.getCity());
      dto.setLegalPersonName(
          StrUtil.isNotEmpty(supplier.getCorporate())
              ? supplier.getCorporate()
              : supplierInGroupInfo.getCorporate());
      List<String> oldEnterpriseNatures =
          CollUtil.emptyIfNull(
              StrUtil.split(supplierInGroupInfo.getEnterpriseNature(), ',', true, true));
      //              .stream()
      //              .map(Constants.ENTERPRISENATURE::get)
      //              .collect(Collectors.toList());
      dto.setEnterpriseNatures(oldEnterpriseNatures);
      dto.setLicense(new SupplierFileDTO("", supplierInGroupInfo.getLicenseUrl()));
      dto.setPayType(
          supplierInGroupInfo.getSupplier() != null
              ? supplierInGroupInfo.getSupplier().getPayType()
              : "");
      dto.setPartnershipTypes(supplierInGroupInfo.getPartnershipTypes());
      dto.setPayTypeOther(supplier.getPayTypeOther());
      dto.setWebsiteRegistrationSyncTime(supplier.getWebsiteRegistrationSyncTime());
      // 网站备案信息
      List<PartnerIcpDTO> parterIcpList = new ArrayList<>();
      if(StrUtil.isNotEmpty(supplier.getWebsiteRegistration())){
        try {
          ObjectMapper mapper = new ObjectMapper();
          parterIcpList  =  new ObjectMapper().readValue(
              supplier.getWebsiteRegistration(),
              mapper.getTypeFactory().constructCollectionType(List.class, PartnerIcpDTO.class)
          );
        }catch (Exception e){
          log.error( "解析网站备案信息异常",e);
      }
      }
      dto.setParterIcpList(parterIcpList);
      return dto;
    }
  }

  /** 提取字符串中的数字 */
  private String extractingNumbers(String input) {
    Pattern pattern = Pattern.compile("\\d+"); // \\d matches any digit, + means one or more
    Matcher matcher = pattern.matcher(input);
    StringBuilder result = new StringBuilder();
    while (matcher.find()) {
      result.append(matcher.group());
    }
    return result.toString();
  }

  @Override
  public boolean isDisposableSupplier(String supplierId) {
    Supplier supplier =
        get(supplierId, () -> CheckException.noFindException(Supplier.class, supplierId));
    return Objects.equals(supplier.getSupType(), Constants.SUPPLIER_TYPE_PROVISIONAL);
  }

  @Override
  public Map<String, String> getSupplierType(String supplierId, String supplierName) {
    if (StrUtil.isBlank(supplierId) && StrUtil.isBlank(supplierName)) {
      throw new CheckException("缺少必须的参数");
    }
    Supplier supplier;
    if (StrUtil.isNotBlank(supplierId)) {
      supplier = get(supplierId, () -> CheckException.noFindException(Supplier.class, supplierId));
    } else {
      supplier =
          supplierRepository
              .findFirstByEnterpriseNameAndState(supplierName, Constants.STATE_OK)
              .orElseThrow(() -> new CheckException("未找到供应商:" + supplierName));
    }
    String supplierTypeCode = supplier.getSupType();
    String supplierTypeName = Constants.SUPPLIERTYPE.get(supplierTypeCode);
    return new HashMap<String, String>() {
      {
        put("supplierTypeCode", supplierTypeCode);
        put("supplierTypeName", supplierTypeName);
      }
    };
  }

  @Override
  public List<Supplier> getNameAndSupType(List<String> name, String supType) {
    return CollUtil.emptyIfNull(dao.getNameList(name,supType));
  }

  @Override
  public List<PurchaseUserVo> getPurchaseUserByName(String name,Integer limit) {
    if (StrUtil.isBlank(name)) {
      return new ArrayList<>();
    }
    List<User> userList =
        CollUtil.defaultIfEmpty(userDao.findUserByRealName(name, limit), new ArrayList<>());
    return userList.stream().map(user -> {
      PurchaseUserVo vo = new PurchaseUserVo();
      vo.setId(user.getId());
      vo.setRealName(user.getRealName());
      vo.setCode(StrUtil.blankToDefault(user.getCode(), user.getErpId()));
      return vo;
    }).collect(Collectors.toList());
  }

  @Override
  public void batchUpdateSupplierDiscountToOms() {
    List<String> supplierIdList =
        supplierRepository.findAllByIsOpenOrderAndState(Constants.STATE_OK, Constants.STATE_OK).stream()
            .map(Supplier::getId).collect(Collectors.toList());
    for (String supplierId : supplierIdList) {
      try {
        List<SupplierPerformance> oldSupplierPerformances =
            CollUtil.emptyIfNull(supplierPerformanceDao.getListBySupplierId(Collections.singletonList(supplierId)));
        //同一平台类型的数据只展示最新时间的一条
        List<SupplierPerformance> supplierPerformances = oldSupplierPerformances.stream().sorted(
                Comparator.comparing(
                    (SupplierPerformance sp) -> sp.getCreateTime() == null ? Long.MIN_VALUE : sp.getCreateTime()).reversed())
            .collect(Collectors.groupingBy(SupplierPerformance::getPlatformCode)).values().stream()
            .map(list -> list.stream().findFirst().orElseThrow(() -> new CheckException("平台类型为空")))
            .collect(Collectors.toList());
        Supplier supplier = get(supplierId);
        for (SupplierPerformance supplierPerformance : supplierPerformances) {
          try {
            LandingMerchantContract contract = Optional.ofNullable(landingMerchantContractService.get(supplierPerformance.getLandingContractId()))
                .orElseGet(LandingMerchantContract::new);
            //合作比例
            List<EntryRegistrationDiscount> entryRegistrationDiscountList =
                StrUtil.isNotBlank(contract.getId()) ? entryRegistrationDiscountRepository.findByLandingContractIdAndState(
                    contract.getId(), Constants.STATE_OK) : Collections.emptyList();
            List<String> platformCodeList = Collections.singletonList(supplierPerformance.getPlatformCode());
            boolean shouldDiscountToOmsClear =
                StrUtil.isNotBlank(contract.getEntryRegistrationOrderId()) && !StrUtil.equals(
                    contract.getFileReviewState(), FileReviewStateEnum.THROUGH_THE.getKey());
            if (shouldDiscountToOmsClear) {
              shareEntryRegistrationService.batchUpdateDiscountToOmsClear(get(supplierId),
                  Collections.singletonList(supplierPerformance.getPlatformCode()));
            } else {
              List<EntryRegistrationDiscountDTO> discounts = entryRegistrationDiscountList.stream().map(item -> {
                EntryRegistrationDiscountDTO dto = new EntryRegistrationDiscountDTO();
                dto.setId(item.getId());
                dto.setType(item.getType());
                dto.setPerformanceAmount(item.getPerformanceAmount());
                dto.setDiscountRatio(item.getDiscountRatio());
                dto.setBrandId(item.getBrandId());
                dto.setBrandName(item.getBrandName());
                return dto;
              }).collect(Collectors.toList());
              shareEntryRegistrationService.batchUpdateDiscountToOms(supplier.getEnterpriseName(),
                  supplier.getId(), platformCodeList, discounts);
            }
          } catch (Exception e) {
            log.error("批处理供应商账号的折扣比例履约信息出错: {}", e.getMessage());
          }
        }
      } catch (Exception e) {
        log.error("批处理供应商账号的折扣比例供应商出错: {}", e.getMessage());
      }
    }
  }

  @Override
  public SupplierOrderAcceptance getSupplierUserListCommon(String supplierId) {
    Supplier supplier = supplierRepository.findById(supplierId)
        .orElseThrow(() -> new CheckException("供应商不存在"));
    SupplierOrderAcceptance supplierOrderAcceptance = new SupplierOrderAcceptance();
    supplierOrderAcceptance.setOrderReceiveTimeLimit(supplier.getOrderReceiveTimeLimit());
    supplierOrderAcceptance.setOpenSupplierOrder(supplier.getOpenSupplierOrder());
    return supplierOrderAcceptance;
  }

  @Override
  public SupplierEOrderAcceptance getSupplierUserListECommerce(String supplierId) {
    Supplier supplier = supplierRepository.findById(supplierId)
        .orElseThrow(() -> new CheckException("供应商不存在"));
    SupplierEOrderAcceptance supplierEOrderAcceptance = new SupplierEOrderAcceptance();
    supplierEOrderAcceptance.setOpenOrder(false);
    if (Constants.STATE_OK.equals(supplier.getIsOpenOrder())) {
      supplierEOrderAcceptance.setOpenOrder(true);
    }
    List<SupplierPerformance> supplierPerformancesAll =
        CollUtil.emptyIfNull(
            supplierPerformanceDao.getListBySupplierId(Collections.singletonList(supplierId)));
    //同一平台类型的数据只展示最新时间的一条
    List<SupplierPerformance> supplierPerformances = supplierPerformancesAll.stream()
        .sorted(Comparator.comparing((SupplierPerformance sp) -> sp.getCreateTime() == null ?
            Long.MIN_VALUE : sp.getCreateTime()).reversed())
        .collect(Collectors.groupingBy(SupplierPerformance::getPlatformCode))
        .values().stream()
        .map(list -> list.stream().findFirst().orElseThrow(() -> new CheckException("平台类型为空")))
        .collect(Collectors.toList());
    List<SupplierPerformanceDTO> supplierPerformanceDTOS =
        this.buildSupplierPerformanceDTO(supplierPerformances);
    supplierEOrderAcceptance.setPerformanceList(supplierPerformanceDTOS);
    return supplierEOrderAcceptance;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void switchSupplierUserPermission(String supplierId) {
    Supplier supplier = supplierRepository.findById(supplierId)
        .orElseThrow(() -> new CheckException("供应商不存在"));
    // 获取原有的权限
    boolean origin = Boolean.TRUE.equals(supplier.getOpenSupplierOrder());
    supplier.setIsOpen(Constants.STATE_OK);
//    supplierToMenuService.deleteAllBySupplierId(supplierId);
    // 可能有问题
//    brandService.deleteBrandInSupplier(supplier.getId());
    if (Boolean.TRUE.equals(!origin)) {
      supplier.openSupplierOrder();
    } else {
      supplier.closeSupplierOrder();
    }
    supplierRepository.saveAndFlush(supplier);
    SupplierChangeCreateForm createForm =
        SupplierChangeCreateForm.builder()
            .updateField(SupplierChangeLogEnum.SUPPLIER_PERMISSION.getUpdateField())
            .supplierId(supplierId)
            .createMan(manageSecurityUtil.getSrmUserDetails().getUser().getRealName())
            .oldData(new SwitchRecord(origin)).newData(new SwitchRecord(!origin))
            .type(SupplierChangeLogEnum.SUPPLIER_PERMISSION.getType()).build();
    supplierChangeRecordService.saveRecordRef(createForm);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void switchESupplierUserPermission(String supplierId) {
    Supplier supplier = supplierRepository.findById(supplierId)
        .orElseThrow(() -> new CheckException("供应商不存在"));
    // 获取原有的权限
    boolean origin = Constants.STATE_OK.equals(supplier.getIsOpenOrder());
    supplier.setIsOpen(Constants.STATE_OK);
//    supplierToMenuService.deleteAllBySupplierId(supplierId);
    // 可能有问题
//    brandService.deleteBrandInSupplier(supplier.getId());
    if (Boolean.TRUE.equals(!origin)) {
      supplier.openOrderPermission();
    } else {
      supplier.closeOrderPermission();
      supplierPerformanceService.deleteBySupplierId(supplierId);
      // 清空supplier的platform
      supplier.setPlatform("");
    }
    supplierRepository.saveAndFlush(supplier);
    SupplierChangeCreateForm createForm =
        SupplierChangeCreateForm.builder()
            .updateField(SupplierChangeLogEnum.E_COMMERCE_SUPPLIER_PERMISSION.getUpdateField())
            .supplierId(supplierId)
            .createMan(manageSecurityUtil.getSrmUserDetails().getUser().getRealName())
            .oldData(new SwitchRecord(origin)).newData(new SwitchRecord(!origin))
            .type(SupplierChangeLogEnum.E_COMMERCE_SUPPLIER_PERMISSION.getType()).build();
    supplierChangeRecordService.saveRecordRef(createForm);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void saveSupplierUserMenu(SupplierUserMenuSaveForm saveForm) {
    Supplier supplier = supplierRepository.findById(saveForm.getSupplierId())
        .orElseThrow(() -> new CheckException("供应商不存在"));
    supplierToMenuService.deleteAllBySupplierId(saveForm.getSupplierId());
    List<String> menuIds = saveForm.getMenuIds();
    if (CollUtil.isNotEmpty(menuIds)) {
      menuIds.forEach(menuId -> supplierToMenuService.save(saveForm.getSupplierId(), menuId));
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void switchSupplierUserPerformanceInfo(String supplierPerformanceId) {
    SupplierPerformance supplierPerformance =
        supplierPerformanceRepository.findById(supplierPerformanceId)
            .orElseThrow(() -> new CheckException("供应商履约信息不存在"));
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    String status = supplierPerformance.getStatus();
    boolean origin = Constants.SUPPLIER_PERFORMANCE_STATUS_EFFECT.equals(status);
    supplierPerformance.setUpdateStateTime(System.currentTimeMillis());
    supplierPerformance.setUpdateTime(System.currentTimeMillis());
    supplierPerformance.setUpdateMan(user.getId());
    if (Boolean.TRUE.equals(!origin)) {
      // 开启
      supplierPerformance.setStatus(Constants.SUPPLIER_PERFORMANCE_STATUS_EFFECT);
    }else{
      // 关闭
      supplierPerformance.setStatus(Constants.SUPPLIER_PERFORMANCE_STATUS_CLOSE);
    }
    supplierPerformanceRepository.saveAndFlush(supplierPerformance);
    SupplierChangeCreateForm createForm =
        SupplierChangeCreateForm.builder()
            .updateField(SupplierChangeLogEnum.PLATFORM_EFFECTIVE_STATUS.getUpdateField())
            .platform(supplierPerformance.getPlatformName())
            .supplierId(supplierPerformance.getSupplierId())
            .createMan(manageSecurityUtil.getSrmUserDetails().getUser().getRealName())
            .oldData(new SwitchRecord(origin)).newData(new SwitchRecord(!origin))
            .type(SupplierChangeLogEnum.PLATFORM_EFFECTIVE_STATUS.getType()).build();
    supplierChangeRecordService.saveRecordRef(createForm);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void linkContractPerformance(String supplierPerformanceId, String contractId) {
    SupplierPerformance supplierPerformance =
        supplierPerformanceRepository.findById(supplierPerformanceId)
            .orElseThrow(() -> new CheckException("供应商履约信息不存在"));
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    String originContractId = supplierPerformance.getLandingContractId();
    boolean link = StrUtil.isNotBlank(originContractId);
    // 原合同解绑
    if (link) {
      landingMerchantContractService.updateAssociationStatus(originContractId, Constants.CONTRACT_ASSOCIATION_PERFORMANCE_STATUS_NO);
    }
    // 如果contractId为空，为解绑
    if (StrUtil.isBlank(contractId)) {
      supplierPerformance.setLandingContractId(null);
      supplierPerformance.setUpdateTime(System.currentTimeMillis());
      supplierPerformance.setUpdateMan(user.getId());
      supplierPerformanceRepository.saveAndFlush(supplierPerformance);
    }else{
      // 合同绑定
      supplierPerformance.setLandingContractId(contractId);
      supplierPerformance.setUpdateTime(System.currentTimeMillis());
      supplierPerformance.setUpdateMan(user.getId());
      supplierPerformanceRepository.saveAndFlush(supplierPerformance);
      landingMerchantContractService.updateAssociationStatus(contractId, Constants.CONTRACT_ASSOCIATION_PERFORMANCE_STATUS_YES);
    }
  }

  @Override
  public SupplierEOrderPerformanceDetail getSupplierUserListPerformance(String supplierPerformanceId) {
    SupplierPerformance supplierPerformance =
        supplierPerformanceRepository.findById(supplierPerformanceId)
            .orElseThrow(() -> new CheckException("供应商履约信息不存在"));
    SupplierPerformanceDTO result =
        this.buildSupplierPerformanceDTO(Collections.singletonList(supplierPerformance)).stream()
            .findFirst().orElse(null);
    SupplierEOrderPerformanceDetail supplierEOrderPerformanceDetail = MapStructFactory.INSTANCE.toSupplierEOrderPerformanceDetail(result);
    supplierEOrderPerformanceDetail.setNeedBundle(false);
    supplierEOrderPerformanceDetail.setBundleList(new ArrayList<>());
    if (StrUtil.isNotBlank(supplierPerformance.getLandingContractId())) {
      landingMerchantContractRepository.findById(supplierPerformance.getLandingContractId())
          .ifPresent(contract -> {
            supplierEOrderPerformanceDetail.setNeedBundle(contract.getNeedBundle());
            List<LandingContractBundleDto> bundleList =
                landingContractBundleService.getLandingContractBundle(
                    Collections.singletonList(contract.getId()), true).stream()
                    .map(MapStructFactory.INSTANCE::toLandingContractBundleDto).collect(Collectors.toList());
            supplierEOrderPerformanceDetail.setBundleList(bundleList);
          });
    }
    return supplierEOrderPerformanceDetail;
  }

  @Override
  public SupplierEOrderPerformanceDetail updateSupplierUserListPerformance(
      String supplierPerformanceId, String dockingPurchaseErpCode, String dockingAssistantId, String businessLeaderId) {
    SupplierPerformance supplierPerformance =
        supplierPerformanceRepository.findById(supplierPerformanceId)
            .orElseThrow(() -> new CheckException("供应商履约信息不存在"));
    User user = manageSecurityUtil.getSrmUserDetails().getUser();
    supplierPerformance.setDockingPurchaseErpCode(dockingPurchaseErpCode);
    supplierPerformance.setDockingAssistant(dockingAssistantId);
    supplierPerformance.setBusinessLeader(businessLeaderId);
    supplierPerformance.setUpdateTime(System.currentTimeMillis());
    supplierPerformance.setUpdateMan(user.getId());
    supplierPerformanceRepository.saveAndFlush(supplierPerformance);
    return this.getSupplierUserListPerformance(supplierPerformanceId);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void updateSupplierOrderReceiveTimeLimit(String supplierId, Integer orderReceiveTimeLimit) {
    Supplier supplier = supplierRepository.findById(supplierId)
        .orElseThrow(() -> new CheckException("供应商不存在"));
    Integer origin = supplier.getOrderReceiveTimeLimit();
    supplier.setOrderReceiveTimeLimit(orderReceiveTimeLimit);
    supplierRepository.saveAndFlush(supplier);
    SupplierChangeCreateForm createForm =
        SupplierChangeCreateForm.builder().updateField(SupplierChangeLogEnum.SUPPLIER_ORDER_TIME_LIMIT.getUpdateField())
            .supplierId(supplierId)
            .createMan(manageSecurityUtil.getSrmUserDetails().getUser().getRealName())
            .oldData(new DayRecord(origin)).newData(new DayRecord(orderReceiveTimeLimit))
            .type(SupplierChangeLogEnum.SUPPLIER_ORDER_TIME_LIMIT.getType()).build();
    supplierChangeRecordService.saveRecordRef(createForm);
  }

  private List<SupplierPerformanceDTO> buildSupplierPerformanceDTO(List<SupplierPerformance> supplierPerformances) {
    List<SupplierPerformanceDTO> result = new ArrayList<>();
    for (SupplierPerformance supplierPerformance : supplierPerformances) {
      Platform platform =
          platformRepository.findFirstByCodeAndState(supplierPerformance.getPlatformCode(),
              Constants.STATE_OK).orElseThrow(() -> new CheckException("平台不存在"));
      SupplierPerformanceDTO supplierPerformanceDTO  = MapStructFactory.INSTANCE.toSupplierPerformanceDTO(supplierPerformance);
      supplierPerformanceDTO.setPlatformName(platform.getName());
      String dockingPurchaseErpCode = supplierPerformanceDTO.getDockingPurchaseErpCode();
      if (StrUtil.isNotBlank(dockingPurchaseErpCode)) {
        supplierPerformanceDTO.setDockingPurchaseErpName(
            Optional.ofNullable(userService.getByCode(dockingPurchaseErpCode))
                .map(User::getRealName)
                .orElse(StrUtil.EMPTY));
      } else {
        supplierPerformanceDTO.setDockingPurchaseErpName(StrUtil.EMPTY);
      }
      LandingMerchantContract contract =
          Optional.ofNullable(
                  landingMerchantContractService.get(supplierPerformance.getLandingContractId()))
              .orElseGet(LandingMerchantContract::new);
      BigDecimal totalPriceBySupplier =
          orderDao.getTotalPriceBySupplierAndTime(supplierPerformance.getSupplierId(),
              supplierPerformance.getPlatformCode(), contract.getEffectiveStart(),
              contract.getEffectiveEnd());
      supplierPerformanceDTO.setTypeOfCooperation(StrUtil.emptyIfNull(contract.getTypeOfCooperation()));
      // 6.5.1版本去除
      //      supplierPerformanceDTO.setRegularSupply(StrUtil.emptyIfNull(contract.getRegularSupply()));
      supplierPerformanceDTO.setStorage(StrUtil.emptyIfNull(contract.getStorage()));
      supplierPerformanceDTO.setStorageAddress(StrUtil.emptyIfNull(contract.getStorageAddress()));
      supplierPerformanceDTO.setStorageArea(contract.getStorageArea());
      supplierPerformanceDTO.setCooperationBrand(StrUtil.emptyIfNull(contract.getCooperationBrand()));
      supplierPerformanceDTO.setDeposit(contract.getDeposit());
      supplierPerformanceDTO.setDepositState(contract.getDepositState());
      supplierPerformanceDTO.setAccountingPeriod(contract.getAccountingPeriod());
      supplierPerformanceDTO.setBackToBack(contract.getBackToBack());
      supplierPerformanceDTO.setPaymentType(contract.getPaymentType());
      supplierPerformanceDTO.setPaymentTypeInput(StrUtil.emptyIfNull(contract.getPaymentTypeInput()));
      supplierPerformanceDTO.setInvoiceType(contract.getInvoiceType());
      supplierPerformanceDTO.setTaxRate(contract.getTaxRate());
      supplierPerformanceDTO.setGuaranteedAmount(contract.getGuaranteedAmount());
      supplierPerformanceDTO.setPenalty(StrUtil.emptyIfNull(contract.getPenalty()));
      supplierPerformanceDTO.setEffectiveStart(contract.getEffectiveStart());
      supplierPerformanceDTO.setEffectiveEnd(contract.getEffectiveEnd());
      List<EntryRegistrationDiscountDetailDTO> entryRegistrationDiscountInfo = new ArrayList<>();
      //合作比例
      List<EntryRegistrationDiscount> entryRegistrationDiscountList = new ArrayList<>();
      if (StrUtil.isNotBlank(contract.getId())) {
        entryRegistrationDiscountList =
            entryRegistrationDiscountRepository.findByLandingContractIdAndState(contract.getId(),
                Constants.STATE_OK);
      }
      if (CollUtil.isNotEmpty(entryRegistrationDiscountList)) {
        for (EntryRegistrationDiscount entryRegistrationDiscount : entryRegistrationDiscountList) {
          entryRegistrationDiscountInfo.add(EntryRegistrationDiscountDetailDTO.fromEntryRegistrationDiscount(entryRegistrationDiscount));
        }
      }
      supplierPerformanceDTO.setEntryRegistrationDiscountInfo(entryRegistrationDiscountInfo);

      EntryRegistrationOrder entryRegistrationOrder =
          entryRegistrationService.get(contract.getEntryRegistrationOrderId());
      if (entryRegistrationOrder != null) {
        supplierPerformanceDTO.setNotes(StrUtil.emptyIfNull(entryRegistrationOrder.getNotes()));
        String landingMerchantEntityId = Optional.ofNullable(
                entryRegistrationLandingMerchantRepository.findFirstByEntryRegistrationOrderIdAndState(
                    entryRegistrationOrder.getId(), Constants.STATE_OK))
            .map(EntryRegistrationLandingMerchant::getId).orElse(StrUtil.EMPTY);
        // 身份证
        List<File> fileList = fileService.getFileList(landingMerchantEntityId,
            Constants.FILE_TYPE_LANDING_MERCHANT_ID_CARD_PHOTO);
        List<FileDTO> fileDTOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(fileList)) {
          fileList.forEach(file -> fileDTOList.add(new FileDTO(file, baseUrl)));
        }
        supplierPerformanceDTO.setIdCardPhoto(fileDTOList);
        // 产品资质书
        List<File> fileListByIdAndType =
            fileService.getFileList(landingMerchantEntityId,
                Constants.FILE_TYPE_LANDING_MERCHANT_PRODUCT_QUALIFICATION);
        List<FileDTO> fileDTOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(fileListByIdAndType)) {
          fileListByIdAndType.forEach(file -> fileDTOS.add(new FileDTO(file, baseUrl)));
        }
        supplierPerformanceDTO.setProductQualification(fileDTOS);
        // 补充附件
        supplierPerformanceDTO.setSupplyFile(fileService.getFileList(entryRegistrationOrder.getId(),
                Constants.FILE_TYPE_REGISTRATION_ORDER_SUPPLEMENT).stream()
            .map(file -> new FileDTO(file, baseUrl)).collect(Collectors.toList()));
      }
      //获取当前比例
      String discountRatio =
          getStepDiscountRatio(contract, totalPriceBySupplier, entryRegistrationDiscountList);
      supplierPerformanceDTO.setRate(discountRatio);
      supplierPerformanceDTO.setLandingContractNo(
          StrUtil.isNotBlank(contract.getContractNo()) ? contract.getContractNo() : StrUtil.EMPTY);

      supplierPerformanceDTO.setPerformanceAmount(
          CommonlyUseUtil.BigDecimalValue(
              totalPriceBySupplier.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString()));
      supplierPerformanceDTO.setBusinessLeaderId(supplierPerformance.getBusinessLeader());
      supplierPerformanceDTO.setDockingAssistantId(supplierPerformance.getDockingAssistant());
      supplierPerformanceDTO.setBusinessLeader(
          getOaUserName(supplierPerformance.getBusinessLeader()));
      supplierPerformanceDTO.setDockingAssistant(
          getOaUserName(supplierPerformance.getDockingAssistant()));
      SupplierRateDetailVO dto =
          omsService.getSupplierRateDetail(supplierPerformance.getSupplierId(), supplierPerformance.getPlatformCode());
      if (dto != null) {
        supplierPerformanceDTO.setSupplierRateDetailInfo(dto.getSupplierRateDetailInfo());
        supplierPerformanceDTO.setOrderRateBrandDetailInfo(dto.getOrderRateBrandDetailInfo());
      } else {
        supplierPerformanceDTO.setSupplierRateDetailInfo(new ArrayList<>());
        supplierPerformanceDTO.setOrderRateBrandDetailInfo(new ArrayList<>());
      }
      result.add(supplierPerformanceDTO);
    }
    return result;
  }

  @Override
  public SupplierPayTypeDataDTO getSupplierPayTypeData(String supplierId, String userGroup) {
    String payType =
        supplierRepository.findById(supplierId).map(Supplier::getPayType).orElse(StrUtil.EMPTY);
    String accountPeriod =
        Optional.ofNullable(supplierInGroupDao.getByGroupAndSupplier(supplierId, userGroup)).map(SupplierInGroup::getAccountPeriod).orElse(StrUtil.EMPTY);
    return new SupplierPayTypeDataDTO(accountPeriod,payType);
  }

  @Override
  public EarliestPaymentTermVo getEarliestPaymentTerm(List<String> codes) {
    if (CollUtil.isEmpty(codes)) {
      return new EarliestPaymentTermVo();
    }
    SupplierOrder newest =
        supplierOrderRepository.findNewestByCodes(codes, Constants.STATE_OK);
    if (Objects.isNull(newest)) {
      return new EarliestPaymentTermVo();
    }
    List<PurchaseOrderPaymentTerms> purchaseOrderPaymentTerms =
        purchaseOrderPaymentTermsRepository.findAllByPurchaseOrderIdAndState(newest.getId(),
            Constants.STATE_OK);

    return new EarliestPaymentTermVo(newest,purchaseOrderPaymentTerms);
  }
}
