package com.xhgj.srm.api.dto.purchase.order;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class PurchaseOrderLargeTicketInfoDTO {
  @ApiModelProperty("开票状态")
  private String invoiceStatus;
  @ApiModelProperty("回款状态")
  private String paymentState;

  public PurchaseOrderLargeTicketInfoDTO(String invoiceStatus, String paymentState) {
    this.invoiceStatus = invoiceStatus;
    this.paymentState = paymentState;
  }
}
