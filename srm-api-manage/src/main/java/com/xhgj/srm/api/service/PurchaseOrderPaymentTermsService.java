package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPaymentTermsAddParam;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPaymentTermsUpdateParam;
import com.xhgj.srm.jpa.entity.PurchaseOrderPaymentTerms;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import java.util.List;

public interface PurchaseOrderPaymentTermsService extends
    BootBaseService<PurchaseOrderPaymentTerms, String> {

  void updatePaymentTermsByPurchaserOrderId(PurchaseOrderPaymentTermsUpdateParam param);

  /**
   * 检查采购订单付款条件入参是否合法
   * 合法：每个付款条件的满足条件不能重复，当满足条件为null时，表示“订单审核通过”；
   * 满足条件：{@link com.xhgj.srm.common.enums.PaymentTermsEnum}
   *
   * @throws CheckException 当不合法时抛出
   */
  void checkPurchaseOrderPaymentTerms(List<PurchaseOrderPaymentTermsAddParam> params)
      throws CheckException;

  /**
   * 保存付款条件信息
   */
  void savePurchaseOrderPaymentTerms(List<PurchaseOrderPaymentTermsAddParam> paymentTerms,
      String purchaseOrderId);
}
