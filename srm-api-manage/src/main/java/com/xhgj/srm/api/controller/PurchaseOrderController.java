package com.xhgj.srm.api.controller;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.xhgj.srm.api.constants.ConstantsLockByUserForType;
import com.xhgj.srm.api.controller.v2Mix.PaymentApplyRecordV2MixController;
import com.xhgj.srm.api.dto.PurchaseOrderListDTO;
import com.xhgj.srm.api.dto.PurchaseOrderPageQuery;
import com.xhgj.srm.api.dto.PurchaseOrderProductListDTO;
import com.xhgj.srm.api.dto.PurchaseOrderProductTableHeaderQuery;
import com.xhgj.srm.api.dto.PurchaseOrderTableHeaderQuery;
import com.xhgj.srm.api.dto.SingleBaseParam;
import com.xhgj.srm.api.dto.SupplierOrderInternalRemarkParam;
import com.xhgj.srm.api.dto.UpdateSupplierOrderBaseInfoDTO;
import com.xhgj.srm.api.dto.purchase.order.AddPurchaseOrderDeliveryParam;
import com.xhgj.srm.api.dto.purchase.order.AddPurchaseOrderReturnParam;
import com.xhgj.srm.api.dto.purchase.order.ConsignmentReturnOrderParam;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderContractVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderDetailedVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderInvoiceVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderLargeTicketInfoDTO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderProductDetailedVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderReturnVO;
import com.xhgj.srm.api.dto.purchase.order.PurchaseOrderWarehousingEntryInfoVO;
import com.xhgj.srm.api.dto.purchase.order.ShippingAndWarehousingInformationVO;
import com.xhgj.srm.api.dto.purchase.order.UpdateNotesParam;
import com.xhgj.srm.api.dto.purchase.order.UpdateProductDetailParam;
import com.xhgj.srm.api.dto.purchase.order.UpdateSupplierContactParam;
import com.xhgj.srm.api.dto.purchase.order.UpdateWarehouseLogisticsParam;
import com.xhgj.srm.api.dto.supplierorder.CancelPurchaseOrderListDTO;
import com.xhgj.srm.api.dto.supplierorder.CancelPurchaseOrderParam;
import com.xhgj.srm.api.dto.supplierorder.ExportPurchaseOrderProductParams;
import com.xhgj.srm.api.dto.supplierorder.OutBoundDeliveryPrams;
import com.xhgj.srm.api.dto.supplierorder.PuchaseOrderAddParams;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPaymentTermsPageVO;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPaymentTermsParam;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderPrepaidApplicationPreInfoDTO;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderProductSearchForm;
import com.xhgj.srm.api.dto.supplierorder.PurchaseOrderWarehousingDTO;
import com.xhgj.srm.api.dto.supplierorder.RetreatWarehouseDTO;
import com.xhgj.srm.api.dto.supplierorder.SupplierOrderCountDTO;
import com.xhgj.srm.api.dto.supplierorder.UnCancelPurchaseOrderDTO;
import com.xhgj.srm.api.dto.supplierorder.UserScreeningConditionParam;
import com.xhgj.srm.api.dto.supplierorder.WarehouseEntryListParams;
import com.xhgj.srm.api.service.PurchaseOrderService;
import com.xhgj.srm.api.task.SupplierOrderTask;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderOutBoundDeliveryStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderPaymentTermsStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderProductStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderStatistics;
import com.xhgj.srm.jpa.dto.purchase.order.PurchaseOrderWarehousingStatistics;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.request.dto.mdm.NameAndCodeDTO;
import com.xhgj.srm.request.dto.oms.SalesOrderListDTO;
import com.xhgj.srm.service.SharePurchaseOrderService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.mvc.lock.BootLockByUserFor;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/purchaseOrder")
@Validated
@Api(tags = {"采购订单相关api"})
@Slf4j
public class PurchaseOrderController extends AbstractRestController {

  @Resource private PurchaseOrderService purchaseOrderService;
  @Resource private RedissonClient redissonClient;
  @Resource
  SharePurchaseOrderService sharePurchaseOrderService;
  @Resource
  SupplierOrderTask supplierOrderTask;

  @ApiOperation(value = "新增采购订单", notes = "新增采购订单")
  @PostMapping(value = "/addOrUpdatePurchaseOrder")
  @BootLockByUserFor(ConstantsLockByUserForType.SAVE_PURCHASE_ORDER)
  public ResultBean<String> addPurchaseOrder(@RequestBody @Valid PuchaseOrderAddParams params) {
    return new ResultBean<>(purchaseOrderService.addPurchaseOrder(params));
  }

  @ApiOperation(value = "获取订单合同信息", notes = "获取订单合同信息")
  @GetMapping(value = "/getContractFiles")
  public ResultBean<PurchaseOrderContractVO> getContractFiles(@RequestParam String id) {
    return new ResultBean<>(purchaseOrderService.getContractFiles(id));
  }

  @ApiOperation(value = "导入采购订单")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "file", value = "文件", required = true)
  })
  @PostMapping("/saveSupplierOrderExcel")
  public ResultBean<Boolean> saveSupplierOrderExcel(
      @RequestParam MultipartFile file,
      @RequestParam @NotBlank(message = "用户 id 不能为空") String userId) {
    purchaseOrderService.saveSupplierOrderExcel(file,userId);
    return new ResultBean<>();
  }

  @ApiOperation("根据采购订单 id 获得订单明细")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "采购订单 id"))
  @GetMapping("purchaseOrderDetailed")
  public ResultBean<PurchaseOrderDetailedVO> getPurchaseOrderDetailed(@NotBlank String id) {
    return new ResultBean<>(purchaseOrderService.getPurchaseOrderDetailed(id));
  }

  @ApiOperation("根据采购订单物料id（项目类别为委外组件的物料） 获得其组件下所有的物料")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "采购订单物料id"))
  @GetMapping("purchaseOrderProductDetailed")
  public ResultBean<List<PurchaseOrderProductDetailedVO>> purchaseOrderProductDetailed(
      @NotBlank String id) {
    return new ResultBean<>(purchaseOrderService.getPurchaseOrderProductDetailed(id));
  }

  @ApiOperation("根据采购单订单 id 查询发货单")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "采购订单 id"))
  @GetMapping("purchaseOrderInvoiceInfo")
  public ResultBean<List<PurchaseOrderInvoiceVO>> getPurchaseOrderInvoiceInfo(@NotBlank String id) {
    return new ResultBean<>(purchaseOrderService.getPurchaseOrderInvoiceInfo(id));
  }

  @ApiOperation("根据采购单订单 id 查询入库单")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "采购订单 id"))
  @GetMapping("purchaseOrderWarehousingEntryInfo")
  public ResultBean<List<PurchaseOrderWarehousingEntryInfoVO>> getPurchaseOrderWarehousingEntryInfo(
      @NotBlank String id) {
    return new ResultBean<>(purchaseOrderService.getPurchaseOrderWarehousingEntryInfo(id));
  }

  @ApiOperation("查询发货入库信息")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "采购订单 id"))
  @GetMapping("shippingAndWarehousingInformation")
  public ResultBean<ShippingAndWarehousingInformationVO> getShippingAndWarehousingInformationVO(
      @NotBlank String id) {
    return new ResultBean<>(purchaseOrderService.getShippingAndWarehousingInformationVO(id));
  }

  @ApiOperation("撤销发货单")
  @PostMapping(value = "cancelInvoiceForm")
  @RepeatSubmit
  public ResultBean<Boolean> cancelInvoiceForm(
      @NotBlank(message = "发货单 id 必传") String orderToFormId) {
    RLock lock = null;
    try {
      lock = redissonClient.getLock(Constants_LockName.PURCHASE_ORDER_INVOICE_REVOKE);
      lock.lock();
      purchaseOrderService.cancelInvoiceForm(orderToFormId);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      throw new CheckException("未知异常，请联系管理员！");
    } finally {
      if (lock != null) {
        lock.unlock();
      }
    }
    return new ResultBean<>(true);
  }

  @ApiOperation("发货并且入库")
  @PostMapping(value = "deliveryAndConfirmReceipt")
  @RepeatSubmit
  public ResultBean<Boolean> deliveryAndConfirmReceipt(
      @RequestBody AddPurchaseOrderDeliveryParam param) {
    RLock lock = null;
    RLock lockGroup = null;
    try {
      lock =
          redissonClient.getLock(
              Constants_LockName.PURCHASE_ORDER_DELIVERY_AND_CONFIRM_RECEIPT + param.getId());
      lock.lock();
      lockGroup = redissonClient.getLock(Constants_LockName.LOCK_GROUP_PURCHASE_ADD_RETURN_AND_REVERSAL + param.getId());
      lockGroup.lock();
      purchaseOrderService.purchaseOrderDelivery(param, true);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知异常，请联系管理员！");
    } finally {
      if (lock != null) {
        lock.unlock();
      }
      if (lockGroup != null) {
        lockGroup.unlock();
      }
    }
    return new ResultBean<>(true);
  }

  @ApiOperation("修改发货单")
  @PostMapping(value = "updateShipForm", consumes = MediaType.APPLICATION_JSON_VALUE)
  @RepeatSubmit
  public ResultBean<Boolean> updateShipForm(
      @RequestBody AddPurchaseOrderDeliveryParam param) {
    purchaseOrderService.updateShipForm(param);
    return new ResultBean<>(true);
  }
  @ApiOperation(value = "获取单据类型数量")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "purchaseOrderId", value = "采购单 id", required = true)
  })
  @GetMapping(value = "getPurchaseOrderToFormCount")
  public ResultBean<SupplierOrderCountDTO> getSupplierOrderToFormCount(
      @NotBlank String purchaseOrderId) {
    return new ResultBean<>(purchaseOrderService.getSupplierOrderFormCountById(purchaseOrderId));
  }

  @ApiOperation("获得订单状态对应的数量")
  @GetMapping("getSupplierOrderCount")
  public ResultBean<SupplierOrderCountDTO> getSupplierOrderCount(PurchaseOrderPageQuery query) {
    User user = getUser();
    return new ResultBean<>(purchaseOrderService.getSupplierOrderCount(user,query));
  }

  @ApiOperation("获得物料状态对应的数量")
  @GetMapping("getOrderProductCount")
  public ResultBean<SupplierOrderCountDTO> getOrderProductCount(PurchaseOrderProductSearchForm form) {
    User user = getUser();
    return new ResultBean<>(purchaseOrderService.getOrderProductCount(user,form));
  }

  @ApiOperation("分页查询采购订单")
  @GetMapping("getPagePurchaseOrderPage")
  public ResultBean<PageResult<PurchaseOrderListDTO>> getPagePurchaseOrderPage(PurchaseOrderPageQuery query) {
    User user = getUser();
    query.setUserId(user.getId());
    return new ResultBean<>(
        purchaseOrderService.getPagePurchaseOrderPageRef(query));
  }

  @ApiOperation("查询采购订单统计forOrder")
  @GetMapping("/statistics/order")
  public ResultBean<PurchaseOrderStatistics> getPagePurchaseOrderStatistics(PurchaseOrderPageQuery query) {
    User user = getUser();
    query.setUserId(user.getId());
    return new ResultBean<>(purchaseOrderService.getPagePurchaseOrderStatisticsForOrder(query));
  }

  @ApiOperation("分页查询采购单物料信息")
  @GetMapping("getPagePurchaseOrderProductPage")
  public ResultBean<PageResult<PurchaseOrderProductListDTO>> getPagePurchaseOrderProductPageRef(PurchaseOrderProductSearchForm form) {
    User user = getUser();
    form.setUserId(user.getId());
    return new ResultBean<>(purchaseOrderService.getPagePurchaseOrderProductPageRef(form));
  }

  @ApiOperation("查询采购单统计数据")
  @GetMapping("/statistics")
  public ResultBean<PurchaseOrderProductStatistics> getPagePurchaseOrderStatistics(PurchaseOrderProductSearchForm form) {
    User user = getUser();
    form.setUserId(user.getId());
    return new ResultBean<>(purchaseOrderService.getPagePurchaseOrderStatisticsForProduct(form));
  }

  @ApiOperation("导出采购单物料信息")
  @PostMapping(value = "exportPurchaseOrderProduct", consumes = MediaType.APPLICATION_JSON_VALUE)
  public ResultBean<Boolean> exportPurchaseOrderProduct(
      @RequestBody @Valid ExportPurchaseOrderProductParams exportPurchaseOrderParams) {
    purchaseOrderService.exportPurchaseOrderProduct(exportPurchaseOrderParams);
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation("分页查询采购单付款条件清单")
  @GetMapping("paymentTermsPage")
  public ResultBean<PageResult<PurchaseOrderPaymentTermsPageVO>> getPaymentTermsPage(
      PurchaseOrderPaymentTermsParam param) {
    User user = getUser();
    return new ResultBean<>(purchaseOrderService.getPaymentTermsPage(user, param));
  }

  @ApiOperation("查询采购单付款条件清单统计")
  @GetMapping("/paymentTerms/statistics")
  public ResultBean<PurchaseOrderPaymentTermsStatistics> getPaymentTermsStatistics(PurchaseOrderPaymentTermsParam param) {
    User user = getUser();
    return new ResultBean<>(purchaseOrderService.getPaymentTermsStatistics(user, param));
  }

  @ApiOperation("入库")
  @PostMapping(value = "confirmReceipt")
  @RepeatSubmit
  public ResultBean<Boolean> confirmReceipt(@RequestBody Map<String, String> param) {
    purchaseOrderService.confirmReceipt(param.get("formId"));
    return new ResultBean<>(true);
  }

  @ApiOperation("入库/退货单 冲销")
  @ApiImplicitParams({
    @ApiImplicitParam(name = "orderToFormId", value = "入库/退货单 id", required = true)
  })
  @PostMapping(value = "receiptOrReturnReversal")
  @RepeatSubmit
  public ResultBean<Boolean> receiptOrReturnReversal(
      @RequestParam @NotBlank(message = "入库/退货单 " + "id 必传") String orderToFormId) {
    purchaseOrderService.receiptOrReturnReversal(orderToFormId);
    return new ResultBean<>(true);
  }

  @ApiOperation("更新备注")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "采购订单物料id"))
  @PutMapping("updateNotes")
  public ResultBean<Boolean> updateNotes(@RequestBody UpdateNotesParam param) {
    purchaseOrderService.updateNotes(param);
    return new ResultBean<>(true);
  }

  @ApiOperation("新增退库单")
  @PostMapping(value = "addReturnOrder")
  @RepeatSubmit
  public ResultBean<Boolean> addReturnOrder(
      @RequestBody AddPurchaseOrderReturnParam param) {
    RLock lock = null;
    RLock lockGroup = null;
    try {
      lock = redissonClient.getLock(Constants_LockName.PURCHASE_ORDER_RETURN + param.getId());
      lockGroup = redissonClient.getLock(Constants_LockName.LOCK_GROUP_PURCHASE_ADD_RETURN_AND_REVERSAL + param.getId());
      lock.lock();
      lockGroup.lock();
      purchaseOrderService.addReturnOrder(param);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      throw new CheckException("未知异常，请联系管理员！");
    } finally {
      if (lock != null) {
        lock.unlock();
      }
      if (lockGroup != null) {
        lockGroup.unlock();
      }
    }
    return new ResultBean<>(true);
  }

  @ApiOperation("寄售订单退货")
  @PostMapping(value = "consignmentReturnOrder")
  @RepeatSubmit
  public ResultBean<String> consignmentReturnOrder(@RequestBody @Valid ConsignmentReturnOrderParam params) {
    User user = getUser();
    params.setUserName(user.getName());
    return new ResultBean<>(purchaseOrderService.consignmentReturnOrder(params));
  }


  @ApiOperation(value = "取消订货")
  @PostMapping(value = "cancelPurchaseOrder")
  public ResultBean<Boolean> cancelPurchaseOrder(@RequestBody @Valid CancelPurchaseOrderParam params) {
    purchaseOrderService.cancelPurchaseOrder(params);
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation("根据采购单订单 id 查询退库单")
  @ApiImplicitParams(@ApiImplicitParam(name = "id", value = "采购订单 id"))
  @GetMapping("purchaseOrderReturn")
  public ResultBean<List<PurchaseOrderReturnVO>> purchaseOrderReturn(
      @NotBlank String id) {
    return new ResultBean<>(purchaseOrderService.purchaseOrderReturn(id));
  }
  @ApiOperation("根据采购订单id获取取消单列表")
  @GetMapping(value = "getCancelPurchaseOrderList")
  public ResultBean<List<CancelPurchaseOrderListDTO>> getCancelPurchaseOrderList(
      @RequestParam @NotBlank(message = "采购单id不能为空！") String purchaseOrderId) {
    return new ResultBean<>(purchaseOrderService.getCancelPurchaseOrderList(purchaseOrderId));
  }

  @ApiOperation(value = "反取消")
  @PostMapping(value = "unCancelPurchaseOrder")
  public ResultBean<Boolean> unCancelPurchaseOrder(@RequestBody  @Valid  UnCancelPurchaseOrderDTO params) {
    purchaseOrderService.unCancelPurchaseOrder(params);
    return new ResultBean<>(true, "操作成功");
  }

  @SneakyThrows
  @ApiOperation(value = "导出采购合同")
  @PostMapping(value = "downloadPurchaseOrderContract", consumes =
      {MediaType.APPLICATION_JSON_VALUE})
  public ResponseEntity<byte[]> downloadSendTickets(@RequestBody SingleBaseParam param) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
    headers.setContentDispositionFormData("attachment",
        URLEncoder.encode("purchaseOrderContract" + System.currentTimeMillis() + ".docx", "UTF-8"));
    byte[] bytes = sharePurchaseOrderService.downloadPurchaseOrderContract(param.getId());
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }

  @SneakyThrows
  @ApiOperation(value = "打印采购合同")
  @PostMapping(value = "printOutPurchaseOrderContract", consumes =
      {MediaType.APPLICATION_JSON_VALUE})
  public ResponseEntity<byte[]> printOutPurchaseOrderContract(@RequestBody SingleBaseParam param) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_PDF);
    headers.setContentDispositionFormData("inline",
        URLEncoder.encode("purchaseOrderContract" + System.currentTimeMillis() + ".pdf", "UTF-8"));
    byte[] bytes = purchaseOrderService.printOutPurchaseOrderContract(param.getId());
    return new ResponseEntity<>(bytes, headers, HttpStatus.CREATED);
  }

  @ApiOperation("oms销售订单详情分页查询接口")
  @GetMapping(value = "salesOrderDetailPage")
  public ResultBean<PageResult<SalesOrderListDTO>> getSalesOrderDetail(
      @NotBlank String salesOrgCode, String salesOrderNo, String projectNo, int pageNo, int pageSize) {
    return new ResultBean<>(
        purchaseOrderService.getSalesOrderDetail(salesOrgCode, salesOrderNo , projectNo, pageNo,
            pageSize));
  }

  @ApiOperation("查询 SAP 库存地址")
  @GetMapping(value = "stockAddr")
  public ResultBean<List<NameAndCodeDTO>> getSAPStockAddr(String name, String code,
      String sapComponentCode, String sapFactoryCode) {
    return new ResultBean<>(
        purchaseOrderService.getSAPStockAddr(name, code, sapComponentCode, sapFactoryCode));
  }

  @ApiOperation("修改供方和收方联系人信息")
  @PostMapping(value = "updateSupplierContact")
  public ResultBean<Boolean> updateSupplierContact(
      @RequestBody @Valid UpdateSupplierContactParam param) {
    purchaseOrderService.updateSupplierContact(param);
    return new ResultBean<>(true);
  }

  @ApiOperation("修改物料明细")
  @PostMapping(value = "updateProductDetail")
  public ResultBean<Boolean> updateProductDetail(
      @RequestBody @Valid UpdateProductDetailParam param) {
    purchaseOrderService.updateProductDetail(param);
    return new ResultBean<>(true);
  }

  /**
   * @see PaymentApplyRecordV2MixController#getPrepaidApplicationPreInfo(List, String)
   */
  /*
  @ApiOperation("查询采购订单申请预付前置信息")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "ids", value = "采购订单 id", required = true),
      @ApiImplicitParam(name = "applyId", value = "编辑的申请 id", required = false)
  })
  @GetMapping(value = "prepaid-application")
  public ResultBean<List<PurchaseOrderPrepaidApplicationPreInfoDTO>> getPrepaidApplicationPreInfo(
      @RequestParam List<String> ids,
      @RequestParam(required = false) String applyId
  ) {
    return new ResultBean<>(
        purchaseOrderService.getPrepaidApplicationPreInfoByIds(ids, applyId));
  }*/



  @ApiOperation(value = "根据表头筛选采购订单数据")
  @RequestMapping(value = "/getOrderListByTableHeader",method = RequestMethod.GET)
  public ResultBean<List<Object>> getOrderListByTableHeader(PurchaseOrderTableHeaderQuery param) {
    User user = getUser();
    param.setUserId(user.getId());
    return new ResultBean<>(purchaseOrderService.getOrderListByTableHeaderRef(param));
  }

  @ApiOperation(value = "根据表头筛选采购物料数据")
  @RequestMapping(value = "/getProductListByTableHeader", method = RequestMethod.GET)
  public ResultBean<List<Object>> getProductListByTableHeader(
      PurchaseOrderProductTableHeaderQuery param) {
    User user = getUser();
    param.setUserId(user.getId());
    return new ResultBean<>(purchaseOrderService.getProductListByTableHeaderRef(param));
  }

  @ApiOperation("入库单列表查询")
  @GetMapping("warehouseWarrantPage")
  public ResultBean<PageResult<PurchaseOrderWarehousingDTO>> warehouseWarrantPage(WarehouseEntryListParams param) {
    User user = getUser();
    param.setUserId(user.getId());
    return new ResultBean<>(purchaseOrderService.warehouseWarrantPageRef(param,user));
  }

  @ApiOperation("入库单统计")
  @GetMapping("/warehouse/statistics")
  public ResultBean<PurchaseOrderWarehousingStatistics> warehouseStatistics(WarehouseEntryListParams param) {
    User user = getUser();
    param.setUserId(user.getId());
    return new ResultBean<>(purchaseOrderService.warehouseStatistics(param, user));
  }

  @ApiOperation("退库单列表查询")
  @GetMapping("outBoundDeliveryPage")
  public ResultBean<PageResult<RetreatWarehouseDTO>> outBoundDeliveryPage(OutBoundDeliveryPrams param) {
    User user = getUser();
    param.setUserId(user.getId());
    return new ResultBean<>(purchaseOrderService.outBoundDeliveryPageRef(param,user));
  }


  @ApiOperation("退库单统计")
  @GetMapping("/outBoundDelivery/statistics")
  public ResultBean<PurchaseOrderOutBoundDeliveryStatistics> outBoundDeliveryStatistics(OutBoundDeliveryPrams param) {
    User user = getUser();
    param.setUserId(user.getId());
    return new ResultBean<>(purchaseOrderService.outBoundDeliveryStatistics(param, user));
  }

  @ApiOperation("导出退库单信息")
  @PostMapping(value = "exportOutBoundDelivery")
  public ResultBean<Boolean> exportOutBoundDelivery(
          @RequestBody  OutBoundDeliveryPrams outBoundDeliveryPrams) {
    User user = getUser();
    purchaseOrderService.exportOutBoundDelivery(user, outBoundDeliveryPrams);
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation("导出入库单信息")
  @PostMapping(value = "exportWarehouseWarrant")
  public ResultBean<Boolean> exportWarehouseWarrant(
          @RequestBody WarehouseEntryListParams warehouseEntryListParams) {
    User user = getUser();
    purchaseOrderService.exportWarehouseWarrant(user, warehouseEntryListParams);
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation("导入退库单信息")
  @PostMapping(value = "importOutBoundDelivery")
  public ResultBean<Boolean> importOutBoundDelivery(
      @RequestParam MultipartFile file) {
    User user = getUser();
    purchaseOrderService.importOutBoundDelivery(file, user);
    return new ResultBean<>(true, "操作成功");
  }

  @ApiOperation(value = "查询预计导出入库单信息条数")
  @PostMapping(value = "getExportWarehouseWarrantCount")
  public ResultBean<Long> getExportWarehouseWarrantCount(
      @RequestBody WarehouseEntryListParams param) {
    return new ResultBean<>(purchaseOrderService.getExportWarehouseWarrantCount(param,getUser()));
  }

  @ApiOperation(value = "查询预计导出退库单信息条数")
  @PostMapping(value = "getExportOutBoundCount")
  public ResultBean<Long> getExportOutBoundCount(
      @RequestBody OutBoundDeliveryPrams param) {
    return new ResultBean<>(purchaseOrderService.getExportOutBoundCount(param,getUser()));
  }

  @ApiOperation("修改基础信息")
  @PostMapping(value = "updateSupplierOrderBaseInfo", consumes = MediaType.APPLICATION_JSON_VALUE)
  @RepeatSubmit(interval = 10000)
  public ResultBean<Boolean> updateSupplierOrderBaseInfo(
      @RequestBody @Valid UpdateSupplierOrderBaseInfoDTO params) {
    purchaseOrderService.updateSupplierOrderBaseInfo(params);
    return new ResultBean<>(true);
  }

  @ApiOperation("根据采购订单号修改供应商开票状态")
  @GetMapping(value = "updateOrderCode")
  public ResultBean<Boolean> updateOrderCode(
      @RequestParam @NotBlank(message = "采购订单号不能为空！") String code,
      @RequestParam @NotBlank(message = "供应商开票状态不能为空！") String supplierOpenInvoiceState) {
    purchaseOrderService.updateOrderCode(code, supplierOpenInvoiceState);
    return new ResultBean<>(true, "操作成功");
  }
  @ApiOperation(value = "导入入库单已开票数量", notes = "导入入库单已开票数量")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userId", value = "用户 id", required = true),
      @ApiImplicitParam(name = "file", value = "文件", required = true)
  })
  @PostMapping("/importWarehouseInvoiceNum")
  public ResultBean<Boolean> importWarehouseInvoiceNum(
      @RequestParam MultipartFile file,
      @RequestParam @NotBlank(message = "用户 id 不能为空") String userId) {
    purchaseOrderService.importWarehouseInvoiceNum(file, userId);
    return new ResultBean<>();
  }

  @ApiOperation(value = "导入退库单已开红票数量", notes = "导入退库单已开红票数量")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "userId", value = "用户 id", required = true),
      @ApiImplicitParam(name = "file", value = "文件", required = true)
  })
  @PostMapping("/importReturnRedInvoiceNum")
  public ResultBean<Boolean> importReturnRedInvoiceNum(
      @RequestParam MultipartFile file,
      @RequestParam @NotBlank(message = "用户 id 不能为空") String userId) {
    purchaseOrderService.importReturnRedInvoiceNum(file, userId);
    return new ResultBean<>();
  }

  @ApiOperation("删除退库单")
  @GetMapping("deleteOutBound")
  public ResultBean<Boolean> deleteOutBound(@NotBlank String id,@NotBlank String sapRwoId) {
    purchaseOrderService.deleteOutBound(id, sapRwoId);
    return new ResultBean<>(true, "操作成功!");
  }


  @ApiOperation("提交内部备注")
  @PostMapping("submitInternalRemark")
  public ResultBean<Boolean> submitInternalRemark(@RequestBody @Valid
  SupplierOrderInternalRemarkParam param) {
    purchaseOrderService.submitInternalRemark(param,getUser());
    return new ResultBean<>(true);
  }


  @ApiOperation(value = "处理入库错误数据", notes = "处理入库错误数据")
  @GetMapping(value = "/handleInboundErrorData")
  public ResultBean<Boolean> handleInboundErrorData(
      @NotBlank String code,
      String orderState,
      String stockProgress,
      BigDecimal finalPrice
      ) {
    purchaseOrderService.handleInboundErrorData(code,orderState,stockProgress,finalPrice);
    return new ResultBean<>(true, "操作成功!");
  }

  @ApiOperation("下载合同附件")
  @GetMapping("contract-attachment")
  public ResultBean<String> getContractAttachment(@RequestParam String id) {
    return new ResultBean<>(purchaseOrderService.getContractAttachment(id));
  }

  @ApiOperation(value = "采购订单根据用户保存表头筛选条件", notes = "采购订单根据用户保存表头筛选条件")
  @PostMapping("addScreeningCondition")
  public ResultBean<Boolean> addScreeningCondition(@RequestBody @Valid UserScreeningConditionParam param) {
    purchaseOrderService.addScreeningCondition(param);
    return new ResultBean<>(true, "操作成功!");
  }

  /**
   * 不满足多页面筛选方法
   * #{@link ScreeningSchemeController}
   */
  @ApiOperation(value = "获取采购订单用户表头筛选条件", notes = "获取采购订单用户表头筛选条件")
  @GetMapping("getScreeningConditionByUser")
  @Deprecated
  public ResultBean<UserScreeningConditionParam> getScreeningConditionByUser(@RequestParam @NotBlank(message = "用户 id 不能为空") String userId) {
    return new ResultBean<>(purchaseOrderService.getScreeningConditionByUser(userId));
  }

  @ApiOperation("修改入库单物流信息")
  @PostMapping(value = "updateWarehouseLogistics")
  public ResultBean<Boolean> updateWarehouseLogistics(
      @RequestBody @Valid UpdateWarehouseLogisticsParam param) {
    purchaseOrderService.updateWarehouseLogistics(param);
    return new ResultBean<>(true);
  }

  @PostMapping(value = "handleStockProgress")
  public ResultBean<Boolean> handleStockProgress() {
    supplierOrderTask.handleStockProgress();
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "获取履约项目编号的开票和回款信息")
  @GetMapping(value = "getLargeTicketInfoByProjectNoList")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "projectNos", value = "项目编号多个用英文逗号分隔", required = true)
  })
  public ResultBean<PurchaseOrderLargeTicketInfoDTO> getLargeTicketInfoByProjectNoList(@RequestParam String projectNos) {
    return new ResultBean<>(purchaseOrderService.getLargeTicketInfoByProjectNoList(projectNos));
  }

}
