package com.xhgj.srm.api.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.xhgj.srm.api.dto.HasRecordBySupplierAndOrgParam;
import com.xhgj.srm.api.dto.ImportPriceProcessResult;
import com.xhgj.srm.api.dto.PurchaseInfoRecordBatchSaveDTO;
import com.xhgj.srm.api.dto.PurchaseInfoRecordImportProgressVo;
import com.xhgj.srm.api.dto.PurchaseInfoRecordListDTO;
import com.xhgj.srm.api.dto.PurchaseInfoRecordListParam;
import com.xhgj.srm.api.dto.PurchaseInfoRecordPageQuery;
import com.xhgj.srm.api.dto.PurchaseInfoRecordTableDTO;
import com.xhgj.srm.api.dto.SrmProductInfoDTO;
import com.xhgj.srm.api.service.GroupService;
import com.xhgj.srm.api.service.MissionService;
import com.xhgj.srm.api.service.PermissionTypeService;
import com.xhgj.srm.api.service.ProductService;
import com.xhgj.srm.api.service.PurchaseInfoRecordService;
import com.xhgj.srm.api.service.SearchSchemeService;
import com.xhgj.srm.api.service.SupplierInGroupService;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.api.service.UserService;
import com.xhgj.srm.util.ImportExcelUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_Batch;
import com.xhgj.srm.common.Constants_Excel;
import com.xhgj.srm.common.Constants_PurchaseInfoRecord;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.dto.Approval;
import com.xhgj.srm.common.dto.ApprovalInstanceResult;
import com.xhgj.srm.common.dto.ApprovalResult;
import com.xhgj.srm.common.enums.DataStatusEnum;
import com.xhgj.srm.common.enums.DingTalkApproveEventResultEnum;
import com.xhgj.srm.common.enums.LogicalOperatorsEnums;
import com.xhgj.srm.common.enums.purchase.inforecord.PurchaseInfoRecordAuditStatusEnum;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.common.utils.HttpUtil;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.annotations.DefaultSearchScheme;
import com.xhgj.srm.jpa.dao.GroupDao;
import com.xhgj.srm.jpa.dao.PurchaseInfoRecordDao;
import com.xhgj.srm.jpa.dao.UserToGroupDao;
import com.xhgj.srm.jpa.dto.purchaseInfoRecord.PurchaseInfoRecordStatistics;
import com.xhgj.srm.jpa.entity.Group;
import com.xhgj.srm.jpa.entity.Mission;
import com.xhgj.srm.jpa.entity.PurchaseInfoRecord;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierInGroup;
import com.xhgj.srm.jpa.entity.User;
import com.xhgj.srm.jpa.entity.UserToGroup;
import com.xhgj.srm.jpa.repository.GroupRepository;
import com.xhgj.srm.jpa.repository.PurchaseInfoRecordRepository;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.sender.mq.sender.BatchTaskMqSender;
import com.xhgj.srm.service.SharePurchaseInfoRecordService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.ExcelUtil;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.redis.util.RedisUtil;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2023/12/29 16:20
 */
@Service
@Slf4j
public class PurchaseInfoRecordServiceImpl implements PurchaseInfoRecordService {
  @Autowired private PurchaseInfoRecordRepository repository;
  @Autowired private PurchaseInfoRecordDao dao;
  @Autowired private SearchSchemeService searchSchemeService;
  @Autowired private GroupService groupService;
  @Autowired private SupplierService supplierService;
  @Autowired private UserService userService;
  @Autowired private MissionService missionService;
  @Autowired private BatchTaskMqSender batchTaskMqSender;
  @Autowired private PermissionTypeService userToPermissionService;
  @Autowired private UserToGroupDao userToGroupDao;
  @Autowired private GroupDao groupDao;
  @Autowired private ExportUtil ex;
  @Autowired private GroupRepository groupRepository;
  @Autowired private SupplierInGroupService supplierInGroupService;
  @Autowired private PurchaseInfoRecordRepository purchaseInfoRecordRepository;
  @Autowired private DingUtils dingUtils;
  @Autowired private SrmConfig srmConfig;
  @Autowired private SharePurchaseInfoRecordService sharePurchaseInfoRecordService;
  @Resource
  MPMService mpmService;

  private static final String PURCHASE_INFO_RECORD_IMPORT_KEY_PREFIX = "purchaseInfoRecordImport";
  private static final String TASK_REDIS_KEY =
      PURCHASE_INFO_RECORD_IMPORT_KEY_PREFIX + "::" + "task";
  // 已处理条数key
  private static final String PROCESSED_REDIS_KEY =
      PURCHASE_INFO_RECORD_IMPORT_KEY_PREFIX + "::" + "processed";
  private static final String ORG_REDIS_KEY =
      PURCHASE_INFO_RECORD_IMPORT_KEY_PREFIX + "::" + "orgName";
  private static final String DEPT_REDIS_KEY =
      PURCHASE_INFO_RECORD_IMPORT_KEY_PREFIX + "::" + "deptName";
  private static final String SUPPLIER_CODE_REDIS_KEY =
      PURCHASE_INFO_RECORD_IMPORT_KEY_PREFIX + "::" + "supplierCode";
  // hash 3分钟-过期时间
  private static final Long PURCHASE_INFO_RECORD_IMPORT_KEY_EXPIRE_TIME = 60 * 3L;
  // 结果集 过期时间-3小时
  private static final Long PURCHASE_INFO_RECORD_IMPORT_RESULT_EXPIRE_TIME = 60 * 60 * 3L;
  @Autowired
  private RedisUtil redisUtil;
  @Resource(name = "taskExecutor")
  private Executor taskExecutor;
  @Resource
  private ProductService productService;
  @Override
  public BootBaseRepository<PurchaseInfoRecord, String> getRepository() {
    return repository;
  }

  @Override
  public PageResult<PurchaseInfoRecordTableDTO> getTablePage(
      PurchaseInfoRecordPageQuery query, Pageable pageable) {
    Assert.notNull(query);
    String schemeId = query.getSchemeId();
    String userId = query.getUserId();
    String supplierName = query.getSupplierName();
    String productCode = query.getProductCode();
    String productName = query.getProductName();
    String productBrandName = query.getProductBrandName();

    String deptName = query.getDeptName();
    Integer deliveryDays = query.getDeliveryDays();
    String productManuCode = query.getProductManuCode();
    String productTaxRate = query.getProductTaxRate();
    BigDecimal productTaxPrice = query.getProductTaxPrice();
    LogicalOperatorsEnums productTaxPriceOperators = query.getProductTaxPriceOperators();
    String productUnit = query.getProductUnit();
    String productCurrency = query.getProductCurrency();
    Long validDateBeginStart = query.getValidDateBeginStart();
    Long validDateBeginEnd = query.getValidDateBeginEnd();
    Long validDateEndStart = query.getValidDateEndStart();
    Long validDateEndEnd = query.getValidDateEndEnd();
    String erpCode = query.getErpCode();
    String priceType = query.getPriceType();
    // 查询方案
    if (StrUtil.isBlank(schemeId)) {
      SearchScheme search =
          searchSchemeService.getDefaultSearchScheme(
              userId, Constants.SEARCH_TYPE_PURCHASE_INFO_RECORD);
      if (search != null) {
        schemeId = search.getId();
      }
    }
    if (StrUtil.isNotEmpty(schemeId)) {
      SearchScheme search = searchSchemeService.get(schemeId);
      if (search != null && StrUtil.isNotEmpty(search.getContent())) {
        PurchaseInfoRecordPageQuery supplierChinaSchemeDTO =
            JSON.parseObject(
                search.getContent(), new TypeReference<PurchaseInfoRecordPageQuery>() {});
        if (supplierChinaSchemeDTO != null) {
          supplierName =
              StrUtil.blankToDefault(supplierName, supplierChinaSchemeDTO.getSupplierName());
          productCode =
              StrUtil.blankToDefault(productCode, supplierChinaSchemeDTO.getProductCode());
          productName =
              StrUtil.blankToDefault(productName, supplierChinaSchemeDTO.getProductName());
          productBrandName =
              StrUtil.blankToDefault(
                  productBrandName, supplierChinaSchemeDTO.getProductBrandName());
          deptName =
              StrUtil.blankToDefault(
                  deptName, supplierChinaSchemeDTO.getDeptName());
          deliveryDays =
              ObjectUtil.defaultIfNull(deliveryDays, supplierChinaSchemeDTO.getDeliveryDays());
          productTaxPrice =
              ObjectUtil.defaultIfNull(productTaxPrice, supplierChinaSchemeDTO.getProductTaxPrice());
          productTaxPriceOperators =
              ObjectUtil.defaultIfNull(productTaxPriceOperators,
                  supplierChinaSchemeDTO.getProductTaxPriceOperators());
          productManuCode =
              StrUtil.blankToDefault(
                  productManuCode, supplierChinaSchemeDTO.getProductManuCode());
          productTaxRate =
              StrUtil.blankToDefault(
                  productTaxRate, supplierChinaSchemeDTO.getProductTaxRate());
          productUnit =
              StrUtil.blankToDefault(
                  productUnit, supplierChinaSchemeDTO.getProductUnit());
          productCurrency =
              StrUtil.blankToDefault(
                  productCurrency, supplierChinaSchemeDTO.getProductCurrency());
          erpCode =
              StrUtil.blankToDefault(
                  erpCode, supplierChinaSchemeDTO.getErpCode());
          validDateBeginStart =
                ObjectUtil.defaultIfNull(validDateBeginStart,
                  supplierChinaSchemeDTO.getValidDateBeginStart());

          validDateBeginEnd =
              ObjectUtil.defaultIfNull(validDateBeginEnd, supplierChinaSchemeDTO.getValidDateBeginEnd());
          validDateEndStart =
              ObjectUtil.defaultIfNull(validDateEndStart, supplierChinaSchemeDTO.getValidDateEndStart());
          validDateEndEnd =
              ObjectUtil.defaultIfNull(validDateEndEnd, supplierChinaSchemeDTO.getValidDateEndEnd());
        }
      }
    }
    if (validDateBeginStart!=null) {
      validDateBeginStart = DateUtil.beginOfDay(DateTime.of(validDateBeginStart)).getTime();
    }
    if (validDateBeginEnd!=null) {
      validDateBeginEnd = DateUtil.endOfDay(DateTime.of(validDateBeginEnd)).getTime();
    }
    if (validDateEndStart!=null) {
      validDateEndStart = DateUtil.beginOfDay(DateTime.of(validDateEndStart)).getTime();
    }
    if (validDateEndEnd!=null) {
      validDateEndEnd = DateUtil.endOfDay(DateTime.of(validDateEndEnd)).getTime();
    }
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    List<String> deptCodes ;
    // 查询数据权限
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)
        || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      deptCodes = null;
    }else{
      String priceLibrary =
          StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
              userId, Constants.USER_PERMISSION_PRICE_LIBRARY),Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION
          );
      if (Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION.equals(priceLibrary)) {
        deptCodes = null;
      }else if (Constants.DATA_PERMISSIONS_TYPE_DEPT.equals(priceLibrary)){
        deptCodes =
            CollUtil.emptyIfNull(userToGroupDao.getUserToGroupList(userId)).parallelStream()
                .map(UserToGroup::getDeptId).distinct()
                .filter(StrUtil::isNotBlank).map(deptId->groupService.get(deptId)).filter(
                    Objects::nonNull
                ).map(Group::getCode).collect(Collectors.toList());
      } else if (Constants.DATA_PERMISSIONS_TYPE_DEPT_CHILDREN.equals(priceLibrary)) {
        deptCodes =
            CollUtil.emptyIfNull(userToGroupDao.getUserToGroupList(userId)).parallelStream()
                .map(UserToGroup::getDeptId).distinct().filter(StrUtil::isNotBlank)
                .map(deptId -> groupService.get(deptId)).filter(Objects::nonNull).map(dept -> {
                  List<Group> deptList =
                      CollUtil.defaultIfEmpty(groupDao.getGroupByParentIds(null, dept.getId()),
                          new ArrayList<>());
                  deptList.add(dept);
                  return deptList;
                }).flatMap(Collection::stream).map(Group::getCode).distinct()
                .collect(Collectors.toList());
      }else{
        throw new CheckException("权限配置错误请联系管理员！");
      }
    }
    Page<PurchaseInfoRecord> pageByOrgCode =
        dao.findPageByOrgCode(query.getUserGroup(), supplierName, productCode, productName,
            productBrandName, null, deptCodes, deptName, deliveryDays, productManuCode,
            productTaxRate, productTaxPrice,
            productTaxPriceOperators, productUnit, productCurrency,
            validDateBeginStart,
            validDateBeginEnd, validDateEndStart, validDateEndEnd, erpCode,priceType,
            pageable.getPageNumber() + 1,
            pageable.getPageSize());
    return PageResultBuilder.buildPageResult(
        pageByOrgCode,
        pageByOrgCode.getContent().parallelStream()
            .map(
                purchaseInfoRecord -> {
                  String groupNameByErpCode =
                      groupService.getGroupNameByErpCode(purchaseInfoRecord.getOrgCode());
                  String enterpriseNameByMdmCode =
                      supplierService.getEnterpriseNameByMdmCode(
                          purchaseInfoRecord.getSupplierCode());
                  return new PurchaseInfoRecordTableDTO(
                      purchaseInfoRecord, groupNameByErpCode, enterpriseNameByMdmCode);
                })
            .collect(Collectors.toList()));
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_PURCHASE_INFO_RECORD)
  public PageResult<PurchaseInfoRecordTableDTO> getTablePageRef(PurchaseInfoRecordPageQuery query) {
    String userId = query.getUserId();
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    List<String> deptCodes ;
    // 查询数据权限
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)
        || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      deptCodes = null;
    }else{
      String priceLibrary =
          StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
              userId, Constants.USER_PERMISSION_PRICE_LIBRARY),Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION
          );
      if (Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION.equals(priceLibrary)) {
        deptCodes = null;
      }else if (Constants.DATA_PERMISSIONS_TYPE_DEPT.equals(priceLibrary)){
        deptCodes =
            CollUtil.emptyIfNull(userToGroupDao.getUserToGroupList(userId)).parallelStream()
                .map(UserToGroup::getDeptId).distinct()
                .filter(StrUtil::isNotBlank).map(deptId->groupService.get(deptId)).filter(
                    Objects::nonNull
                ).map(Group::getCode).collect(Collectors.toList());
      } else if (Constants.DATA_PERMISSIONS_TYPE_DEPT_CHILDREN.equals(priceLibrary)) {
        deptCodes =
            CollUtil.emptyIfNull(userToGroupDao.getUserToGroupList(userId)).parallelStream()
                .map(UserToGroup::getDeptId).distinct().filter(StrUtil::isNotBlank)
                .map(deptId -> groupService.get(deptId)).filter(Objects::nonNull).map(dept -> {
                  List<Group> deptList =
                      CollUtil.defaultIfEmpty(groupDao.getGroupByParentIds(null, dept.getId()),
                          new ArrayList<>());
                  deptList.add(dept);
                  return deptList;
                }).flatMap(Collection::stream).map(Group::getCode).distinct()
                .collect(Collectors.toList());
      }else{
        throw new CheckException("权限配置错误请联系管理员！");
      }
    }
    Page<PurchaseInfoRecord> pageRef = dao.findPageRef(query.toQueryMap(deptCodes));
    return PageResultBuilder.buildPageResult(
        pageRef,
        pageRef.getContent().parallelStream()
            .map(
                purchaseInfoRecord -> {
                  String groupNameByErpCode =
                      groupService.getGroupNameByErpCode(purchaseInfoRecord.getOrgCode());
                  String enterpriseNameByMdmCode =
                      supplierService.getEnterpriseNameByMdmCode(
                          purchaseInfoRecord.getSupplierCode());
                  return new PurchaseInfoRecordTableDTO(
                      purchaseInfoRecord, groupNameByErpCode, enterpriseNameByMdmCode);
                })
            .collect(Collectors.toList()));
  }

  @Override
  @DefaultSearchScheme(searchType = Constants.SEARCH_TYPE_PURCHASE_INFO_RECORD)
  public PurchaseInfoRecordStatistics getStatistics(PurchaseInfoRecordPageQuery query) {
    String userId = query.getUserId();
    User user = userService.get(userId, () -> CheckException.noFindException(User.class, userId));
    List<String> deptCodes ;
    // 查询数据权限
    if (user.getRoleList().contains(Constants.SUPPLIER_USER_ROLE_ADMIN)
        || user.getRoleList().contains(Constants.ROLE_ADMINISTRATOR)) {
      deptCodes = null;
    }else{
      String priceLibrary =
          StrUtil.blankToDefault(userToPermissionService.getUserPermissionCodeByUserIdAndType(
              userId, Constants.USER_PERMISSION_PRICE_LIBRARY),Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION
          );
      if (Constants.DATA_PERMISSIONS_TYPE_ORGANIZATION.equals(priceLibrary)) {
        deptCodes = null;
      }else if (Constants.DATA_PERMISSIONS_TYPE_DEPT.equals(priceLibrary)){
        deptCodes =
            CollUtil.emptyIfNull(userToGroupDao.getUserToGroupList(userId)).parallelStream()
                .map(UserToGroup::getDeptId).distinct()
                .filter(StrUtil::isNotBlank).map(deptId->groupService.get(deptId)).filter(
                    Objects::nonNull
                ).map(Group::getCode).collect(Collectors.toList());
      } else if (Constants.DATA_PERMISSIONS_TYPE_DEPT_CHILDREN.equals(priceLibrary)) {
        deptCodes =
            CollUtil.emptyIfNull(userToGroupDao.getUserToGroupList(userId)).parallelStream()
                .map(UserToGroup::getDeptId).distinct().filter(StrUtil::isNotBlank)
                .map(deptId -> groupService.get(deptId)).filter(Objects::nonNull).map(dept -> {
                  List<Group> deptList =
                      CollUtil.defaultIfEmpty(groupDao.getGroupByParentIds(null, dept.getId()),
                          new ArrayList<>());
                  deptList.add(dept);
                  return deptList;
                }).flatMap(Collection::stream).map(Group::getCode).distinct()
                .collect(Collectors.toList());
      }else{
        throw new CheckException("权限配置错误请联系管理员！");
      }
    }
//    List<PurchaseInfoRecord> purchaseInfoRecordList = dao.findStatistics(query.toQueryMap(deptCodes));
//    BigDecimal productTaxPrice =
//        purchaseInfoRecordList.stream().map(PurchaseInfoRecord::getProductTaxPrice)
//        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
//    PurchaseInfoRecordStatistics purchaseInfoRecordStatistics = new PurchaseInfoRecordStatistics();
//    purchaseInfoRecordStatistics.setProductTaxPrice(productTaxPrice);
//    return purchaseInfoRecordStatistics;
    return dao.findStatistics2(query.toQueryMap(deptCodes));
  }

  @Async
  @Transactional(rollbackFor = Exception.class)
  @Override
  public PurchaseInfoRecordBatchSaveDTO saveFromExcel(InputStream file, String originalFilename,
      String userId,
      String taskId) {
    PurchaseInfoRecordImportProgressVo progressVo = new PurchaseInfoRecordImportProgressVo();
    // 保存任务状态-初始化
    progressVo.setTaskId(taskId);
    progressVo.setStage(Constants.STATE_NO);
    saveTaskStatus(taskId,progressVo);
    String importBatchNumber = taskId;
    // 初始化已处理条数
    redisUtil.set(getProcessedNumsKey(taskId), 0, PURCHASE_INFO_RECORD_IMPORT_RESULT_EXPIRE_TIME);
    List<String> failureDetails = new ArrayList<>();
    List<PurchaseInfoRecord> successfulLine = new ArrayList<>();
    InputStream inputStream = file;
    Workbook book = null;
    try {
      book = ExcelUtil.buildByFile(originalFilename, inputStream);
    } catch (IOException e) {
      log.error("导入采购价格库文件构建workBook异常", e);
      progressVo.setStage(Constants.IMPORT_STATE_EXCEPTION);
      progressVo.setErrorMsg("导入采购价格库文件构建workBook异常");
      saveTaskStatus(taskId,progressVo);
      throw new CheckException("文件异常，请联系管理员");
    }
    if (book == null) {
      progressVo.setStage(Constants.IMPORT_STATE_EXCEPTION);
      progressVo.setErrorMsg("文件异常，请联系管理员");
      saveTaskStatus(taskId,progressVo);
      throw new CheckException("文件异常，请联系管理员");
    }
    Sheet sheet = book.getSheetAt(0);
    if (!ex.validateExcel(sheet, 1, Constants_Excel.IMPORT_PURCHASE_INFO_RECORD_TITLE_LIST)) {
      progressVo.setStage(Constants.IMPORT_STATE_EXCEPTION);
      progressVo.setErrorMsg("导入采购价格库失败，请从正确的地址下载模板！");
      saveTaskStatus(taskId,progressVo);
      throw new CheckException("导入采购价格库失败，请从正确的地址下载模板！");
    }
    int COLUMN_HEADER_LENGTH = 2;
    // 总行数
    int rowNums = sheet.getPhysicalNumberOfRows();
    progressVo.setRowNums(rowNums - COLUMN_HEADER_LENGTH);
    saveTaskStatus(taskId,progressVo);
    int batch = (int) Math.ceil((double) rowNums / Runtime.getRuntime().availableProcessors());
    List<CompletableFuture<ImportPriceProcessResult>> futures = new ArrayList<>();
    for (int batchStart = COLUMN_HEADER_LENGTH; batchStart < rowNums; batchStart += batch) {
      int batchEnd = Math.min(batchStart + batch, rowNums);
      int finalBatchStart = batchStart;
      CompletableFuture<ImportPriceProcessResult> future =
          CompletableFuture.supplyAsync(() -> processRow(sheet,
          finalBatchStart, batchEnd, userId, importBatchNumber), taskExecutor);
      futures.add(future);
    }
    // 等待所有任务完成并收集结果
    List<ImportPriceProcessResult> collect =
        futures.stream().map(CompletableFuture::join).collect(Collectors.toList());
    collect.forEach(
        importPriceProcessResult -> {
          failureDetails.addAll(CollUtil.emptyIfNull(importPriceProcessResult.getFailureDetails()));
          successfulLine.addAll(CollUtil.emptyIfNull(importPriceProcessResult.getSuccessfulLine()));
        });
    boolean success = CollUtil.isEmpty(failureDetails);
    if (success && CollUtil.isNotEmpty(successfulLine)) {
      log.info("导入价格库成功-saveAll-start：{}", DateUtil.now());
      purchaseInfoRecordRepository.saveAll(successfulLine);
      log.info("导入价格库成功-saveAll-end：{}", DateUtil.now());
    }
    PurchaseInfoRecordBatchSaveDTO infoRecordBatchSaveDTO = new PurchaseInfoRecordBatchSaveDTO();
    infoRecordBatchSaveDTO.setSuccess(success);
    infoRecordBatchSaveDTO.setImportBatchNumber(importBatchNumber);
    infoRecordBatchSaveDTO.setFailureDetails(failureDetails);
    progressVo.setStage(Constants.STATE_OK);
    progressVo.setImportResult(infoRecordBatchSaveDTO);
    saveTaskStatus(taskId,progressVo);
    log.info("导入价格库-处理完成--{}",DateUtil.now());
    return infoRecordBatchSaveDTO;
  }

  private ImportPriceProcessResult processRow(Sheet sheet, int batchStart, int batchEnd, String userId,
      String importBatchNumber) {
    log.info("导入价格库-线程{}开始处理,行号{}-{}--{}",Thread.currentThread().getName(),batchStart,
        batchEnd,
        DateUtil.now());
    ImportPriceProcessResult processResult = new ImportPriceProcessResult();
    for (int i = batchStart; i < batchEnd; i++) {
      Row row = sheet.getRow(i);
      int rowNo = i + 1;
      long now = System.currentTimeMillis();
      increaseProcessedNums(importBatchNumber);
      // 获得当前行
      if (row == null) {
        continue;
      }
      String orgName = ex.getCellStringValue(row.getCell(0)).trim();
      String deptName = ex.getCellStringValue(row.getCell(1)).trim();
      String supplierCode = ex.getCellStringValue(row.getCell(2)).trim();
      String supplierName = ex.getCellStringValue(row.getCell(3)).trim();
      String productCode = ex.getCellStringValue(row.getCell(4)).trim();
      String deliveryDaysStr = ex.getCellStringValue(row.getCell(5)).trim();
      String taxRate = ex.getCellStringValue(row.getCell(6)).trim();
      String taxPriceStr = ex.getCellStringValue(row.getCell(7)).trim();
      String currency = ex.getCellStringValue(row.getCell(8)).trim();
      // 空行校验
      if (StrUtil.isAllBlank(orgName, deptName, supplierCode, supplierName, productCode,
          deliveryDaysStr, taxRate, taxPriceStr, currency)) {
        continue;
      }
      // 必填项校验
      if (StrUtil.hasBlank(orgName, deptName, supplierCode, productCode, deliveryDaysStr, taxRate,
          taxPriceStr, currency)) {
        processResult.getFailureDetails().add("第" + rowNo + "行，必填项必须全部填写。");
        continue;
      }
      // 采购组织
      Group org = getGroupFromCacheOrDb(ORG_REDIS_KEY, orgName);
      if (org == null) {
        processResult.getFailureDetails()
            .add("第" + rowNo + "行，采购组织名称未匹配到，请按照规范填写，注意符号大小写");
        continue;
      }
      // 采购部门，需要校验这个部门是否属于上面的采购组织
      Group dept = getGroupFromCacheOrDb(DEPT_REDIS_KEY, deptName);
      if (dept == null || !Objects.equals(dept.getGroupCode(), org.getCode())) {
        processResult.getFailureDetails()
            .add("第" + rowNo + "行，采购部门在采购组织下找不到");
        continue;
      }
      // 供应商主数据编码
      Supplier supplier =  supplierService.getByMdmCode(supplierCode);
      if(supplier==null){
        processResult.getFailureDetails()
            .add("第" + rowNo + "行，供应商主数据编码未查询到供应商数据");
        continue;
      }
      if (Objects.equals(supplier.getSupType(), Constants.SUPPLIER_TYPE_PROVISIONAL)) {
        processResult.getFailureDetails().add("第" + rowNo + "行，一次性供应商不能维护价格库");
        continue;
      }
      // 如果不为内部供应商，需要校验这个供应商是否属于上面的采购组织
      if(!Objects.equals(supplier.getSupType(), Constants.SUPPLIER_TYPE_INTERNAL)){
        // 供应商，需要校验这个供应商是否属于上面的采购组织
        SupplierInGroup sig =
            getSupplierInGroupFromCacheOrDb(SUPPLIER_CODE_REDIS_KEY, supplierCode, org.getCode());
        if (sig == null) {
          processResult.getFailureDetails()
              .add("第" + rowNo + "行，供应商主数据编码在采购组织下找不到");
          continue;
        }
      }

      // 获取物料信息，顺便校验 MPM 是否存在
      String productName;
      String brandName;
      String model;
      String basicUnitName;
      String description;
      try {
        // 已检验正确性
        TypeReference<JSONObject> typeRef = new TypeReference<JSONObject>() {};
        JSONObject json = mpmService.getProductDetail(productCode, typeRef);
        productName = json.getString("name");
        brandName = json.getString("brandNameEn") + "/" + json.getString("brandNameCn");
        model = json.getString("model");
        basicUnitName = json.getString("basicUnitName");
        description = json.getString("remark");
      } catch (Exception e) {
        // 此处只打印简略日志
        log.error("第" + rowNo + "行，物料编码未找到或 MPM 服务异常，请检查", e);
        processResult.getFailureDetails()
            .add("第" + rowNo + "行，物料编码未找到或 MPM 服务异常，请检查");
        continue;
      }
      // 计划交货时间
      int deliveryDays;
      try {
        deliveryDays = Integer.parseInt(deliveryDaysStr);
        if (deliveryDays <= 0) {
          throw new RuntimeException("非正整数");
        }
      } catch (Exception e) {
        processResult.getFailureDetails().add("第" + rowNo + "行，计划交货时间请输入正整数");
        continue;
      }
      // 税率
      if (!Constants_PurchaseInfoRecord.TAX_RATE_MAP.containsKey(taxRate)) {
        processResult.getFailureDetails().add("第" + rowNo + "行，税率请输入：" + CollUtil.join(
            Constants_PurchaseInfoRecord.TAX_RATE_MAP.keySet(), "、"));
        continue;
      }
      // 含税单价
      // 12 可
      // 12.2 可
      // 12.23 可
      // 12.234 不可以
      // 12. 不可
      BigDecimal taxPrice;
      try {
        // 合法数字
        taxPrice = new BigDecimal(taxPriceStr);
        // 大于 0
        if (NumberUtil.isLessOrEqual(taxPrice, BigDecimal.ZERO)) {
          throw new RuntimeException("小于等于0");
        }
        // 如果有小数点，则小数点后位数不能是 0 且不能大于 2
        if (taxPriceStr.contains(".")) {
          int afterDotLength = StrUtil.length(StrUtil.split(taxPriceStr, ".")[1]);
          if (afterDotLength == 0 || afterDotLength > 2) {
            throw new RuntimeException("小数点后位数异常");
          }
        }
      } catch (Exception e) {
        processResult.getFailureDetails()
            .add("第" + rowNo + "行，含税单价必须是大于0的数字，精度两位小数");
        continue;
      }
      // 货币码
      if (!Constants.MONEY_CODE_MAP.containsValue(currency)) {
        processResult.getFailureDetails().add(
            "第" + rowNo + "行，货币码请输入：" + CollUtil.join(Constants.MONEY_CODE_MAP.values(),
                "、"));
        continue;
      }
      // 创建新的记录
      PurchaseInfoRecord purchaseInfoRecord =
          PurchaseInfoRecord.builder().createMan(userId).createTime(now).orgCode(org.getCode())
              .deptCode(dept.getCode()).deptName(dept.getName()).supplierCode(supplierCode)
              .productCode(productCode).deliveryDays(deliveryDays).productName(productName)
              .productBrandName(brandName).productManuCode(model).productTaxRate(taxRate)
              .productTaxPrice(taxPrice).productUnit(basicUnitName).productCurrency(currency)
              .priceType(Constants.PURCHASE_PRICE_TYPE_SIGN_COST)
              .description(description)
              .importBatchNumber(importBatchNumber).state(DataStatusEnum.LOCKED.getCode()).build();
      processResult.getSuccessfulLine().add(purchaseInfoRecord);
    }
    log.info("导入价格库-线程{}结束,行号{}-{}--{}",Thread.currentThread().getName(),
        batchStart,batchEnd,
        DateUtil.now());
    return processResult;
  }

  /**
   * 从缓存或数据库中获取供应商信息
   * @param cacheKey 缓存 key
   * @param supplierCode 供应商编码
   * @param groupCode 组织编码
   */
  private SupplierInGroup getSupplierInGroupFromCacheOrDb(String cacheKey, String supplierCode,
      String groupCode) {
    SupplierInGroup sig = null;
    Object cachedSupplier = redisUtil.hget(cacheKey, supplierCode);
    if (ObjectUtil.isNotNull(cachedSupplier)) {
      sig = ((JSONObject) cachedSupplier).toJavaObject(SupplierInGroup.class);
    }
    if (sig == null) {
      sig = supplierInGroupService.getSupplierInGroupByMDMAndGroupCode(supplierCode, groupCode);
      if (sig != null) {
        redisUtil.hset(cacheKey, supplierCode, sig,PURCHASE_INFO_RECORD_IMPORT_KEY_EXPIRE_TIME);
      }
    }
    return sig;
  }

  /**
   * 从缓存或数据库中获取组织信息
   * @param cacheKey 缓存 key
   * @param name 组织名称
   */
  private Group getGroupFromCacheOrDb(String cacheKey, String name) {
    Group group = null;
    Object cachedGroup = redisUtil.hget(cacheKey, name);
    if (ObjectUtil.isNotNull(cachedGroup)) {
      group = ((JSONObject) cachedGroup).toJavaObject(Group.class);
    }
    if (group == null) {
      group = groupRepository.findFirstByNameAndState(name, Constants.STATE_OK);
      if (group != null) {
        redisUtil.hset(cacheKey, name, group,PURCHASE_INFO_RECORD_IMPORT_KEY_EXPIRE_TIME);
      }
    }
    return group;
  }

  @Override
  public void exportExcel(PurchaseInfoRecordPageQuery query) {
    Assert.notNull(query);
    User user = userService.getUserById(query.getUserId());
    List<String> exportIds = new ArrayList<>();
    List<String> ids = query.getIds();
    boolean exportAll = false;
    if (CollUtil.isNotEmpty(ids)) {
      exportIds.addAll(ids);
    } else if (query.isEmpty()) {
      exportAll = true;
    } else {
      String schemeId = query.getSchemeId();
      String userId = query.getUserId();
      String supplierName = query.getSupplierName();
      String productCode = query.getProductCode();
      String productName = query.getProductName();
      String productBrandName = query.getProductBrandName();

      String deptName = query.getDeptName();
      Integer deliveryDays = query.getDeliveryDays();
      String productManuCode = query.getProductManuCode();
      String productTaxRate = query.getProductTaxRate();
      BigDecimal productTaxPrice = query.getProductTaxPrice();
      LogicalOperatorsEnums productTaxPriceOperators = query.getProductTaxPriceOperators();
      String productUnit = query.getProductUnit();
      String productCurrency = query.getProductCurrency();
      Long validDateBeginStart = query.getValidDateBeginStart();
      Long validDateBeginEnd = query.getValidDateBeginEnd();
      Long validDateEndStart = query.getValidDateEndStart();
      Long validDateEndEnd = query.getValidDateEndEnd();
      String erpCode = query.getErpCode();
      String priceType = query.getPriceType();
      // 查询方案
      if (StrUtil.isBlank(schemeId)) {
        SearchScheme search =
            searchSchemeService.getDefaultSearchScheme(
                userId, Constants.SEARCH_TYPE_PURCHASE_INFO_RECORD);
        if (search != null) {
          schemeId = search.getId();
        }
      }
      if (StrUtil.isNotEmpty(schemeId)) {
        SearchScheme search = searchSchemeService.get(schemeId);
        if (search != null && StrUtil.isNotEmpty(search.getContent())) {
          PurchaseInfoRecordPageQuery supplierChinaSchemeDTO =
              JSON.parseObject(
                  search.getContent(), new TypeReference<PurchaseInfoRecordPageQuery>() {});
          if (supplierChinaSchemeDTO != null) {
            supplierName =
                StrUtil.blankToDefault(supplierName, supplierChinaSchemeDTO.getSupplierName());
            productCode =
                StrUtil.blankToDefault(productCode, supplierChinaSchemeDTO.getProductCode());
            productName =
                StrUtil.blankToDefault(productName, supplierChinaSchemeDTO.getProductName());
            productBrandName =
                StrUtil.blankToDefault(
                    productBrandName, supplierChinaSchemeDTO.getProductBrandName());
            deptName =
                StrUtil.blankToDefault(
                    deptName, supplierChinaSchemeDTO.getDeptName());
            deliveryDays =
                ObjectUtil.defaultIfNull(deliveryDays, supplierChinaSchemeDTO.getDeliveryDays());
            productTaxPrice =
                ObjectUtil.defaultIfNull(productTaxPrice, supplierChinaSchemeDTO.getProductTaxPrice());
            productTaxPriceOperators =
                ObjectUtil.defaultIfNull(productTaxPriceOperators,
                    supplierChinaSchemeDTO.getProductTaxPriceOperators());
            productManuCode =
                StrUtil.blankToDefault(
                    productManuCode, supplierChinaSchemeDTO.getProductManuCode());
            productTaxRate =
                StrUtil.blankToDefault(
                    productTaxRate, supplierChinaSchemeDTO.getProductManuCode());
            productUnit =
                StrUtil.blankToDefault(
                    productUnit, supplierChinaSchemeDTO.getProductUnit());
            productCurrency =
                StrUtil.blankToDefault(
                    productCurrency, supplierChinaSchemeDTO.getProductCurrency());
            erpCode =
                StrUtil.blankToDefault(
                    erpCode, supplierChinaSchemeDTO.getErpCode());
            validDateBeginStart =
                ObjectUtil.defaultIfNull(validDateBeginStart,
                    supplierChinaSchemeDTO.getValidDateBeginStart());
            validDateBeginEnd =
                ObjectUtil.defaultIfNull(validDateBeginEnd, supplierChinaSchemeDTO.getValidDateBeginEnd());
            validDateEndStart =
                ObjectUtil.defaultIfNull(validDateEndStart, supplierChinaSchemeDTO.getValidDateEndStart());
            validDateEndEnd =
                ObjectUtil.defaultIfNull(validDateEndEnd, supplierChinaSchemeDTO.getValidDateEndEnd());
          }
        }
      }
      if (validDateBeginStart!=null) {
        validDateBeginStart = DateUtil.beginOfDay(DateTime.of(validDateBeginStart)).getTime();
      }
      if (validDateBeginEnd!=null) {
        validDateBeginEnd = DateUtil.endOfDay(DateTime.of(validDateBeginEnd)).getTime();
      }
      if (validDateEndStart!=null) {
        validDateEndStart = DateUtil.beginOfDay(DateTime.of(validDateEndStart)).getTime();
      }
      if (validDateEndEnd!=null) {
        validDateEndEnd = DateUtil.endOfDay(DateTime.of(validDateEndEnd)).getTime();
      }
      List<String> supplierOrder =
          CollUtil.emptyIfNull(
              dao.findIdsByOrgCode(
                  query.getUserGroup(), supplierName, productCode, productName, productBrandName, deptName, deliveryDays, productManuCode,
                  productTaxRate, productTaxPrice,
                  productTaxPriceOperators, productUnit, productCurrency,
                  validDateBeginStart,
                  validDateBeginEnd, validDateEndStart, validDateEndEnd, erpCode,priceType));
      exportIds.addAll(supplierOrder);
    }
    Mission mission =
        missionService.createMission(user, "导出-采购价格库", Constants.PLATFORM_TYPE_AFTER, null, null);
    Map<String, Object> params = new HashMap<>();
    params.put("userId", query.getUserId());
    params.put("orgCode", query.getUserGroup());
    params.put("ids", exportIds);
    params.put("exportAll", exportAll);
    batchTaskMqSender.toHandleBatchTask(
        mission.getId(),
        JSON.toJSONString(params),
        Constants_Batch.BATCH_TASK_EXPORT_PURCHASE_INFO_RECORD);
  }

  @Override
  public List<PurchaseInfoRecordListDTO> getNewestRecordByProductCode(PurchaseInfoRecordListParam param) {
    List<PurchaseInfoRecordListDTO> dtoList = new ArrayList<>();
    String[] split = param.getProductCodes().split(",");
    for (String productCode : split) {
      dao.getNewestRecordByProductCode(param.getUserGroup(),
          param.getSupplierId(),
          productCode,
          param.getProductCurrency(),Constants.PURCHASE_PRICE_TYPE_SIGN_COST)
          .ifPresent(purchaseInfoRecord -> dtoList.add(new PurchaseInfoRecordListDTO(purchaseInfoRecord)));
    }
    return dtoList;
  }

  @Override
  public Boolean hasRecordBySupplierAndUserGroup(HasRecordBySupplierAndOrgParam param) {
    Long count = dao.countRecordBySupplierIdAndUserGroup(param.getUserGroup(),
        param.getSupplierId());
    return count>0;
  }
  @Override
  @Transactional(rollbackFor = Exception.class)
  public void submitImport(String importBatchNumber, String fileUrl, User user) {
    List<PurchaseInfoRecord> purchaseInfoRecords =
        purchaseInfoRecordRepository.findAllByImportBatchNumberAndState(importBatchNumber,
            DataStatusEnum.LOCKED.getCode());
    if (CollUtil.isEmpty(purchaseInfoRecords)) {
      throw new CheckException("没有需要审核的采购价格库");
    }
    Set<String> supplierCodes =
        purchaseInfoRecords.stream().map(PurchaseInfoRecord::getSupplierCode)
            .collect(Collectors.toSet());
    List<String> supplierNames =
        supplierCodes.stream().map(supplierService::getByMdmCode).filter(Objects::nonNull)
            .map(Supplier::getEnterpriseName).collect(Collectors.toList());
    String processInstanceId = sendDingTalkApprove(fileUrl, user, purchaseInfoRecords.size(),
        StrUtil.join("，", supplierNames));
    purchaseInfoRecords.stream().parallel().forEach(purchaseInfoRecord -> {
      purchaseInfoRecord.setState(DataStatusEnum.NORMAL.getCode());
      purchaseInfoRecord.setAuditStatus(PurchaseInfoRecordAuditStatusEnum.UNDER_REVIEW.getKey());
      purchaseInfoRecord.setProcessInstanceId(processInstanceId);
      purchaseInfoRecordRepository.save(purchaseInfoRecord);
    });
  }

  private String sendDingTalkApprove(String fileUrl, User user, int count, String supplierNames) {
    if (StrUtil.isBlank(user.getMobile())) {
      throw new CheckException("用户手机号为空");
    }
    String dingTalkUserId = dingUtils.getUserId(user.getMobile());
    List<Long> deptIds = dingUtils.getDeptIds(dingTalkUserId);
    if (CollUtil.isEmpty(deptIds)) {
      throw new CheckException("用户部门为空");
    }
    final String preview_service = "https://www.xhgjmall.com/kkfileview/onlinePreview?url=";
    String encodedFileUrl = URLUtil.encodeAll(Base64.encode(fileUrl));
    List<Approval.FormComponentValue> formComponentValues = ListUtil.of(
        new Approval.FormComponentValue("发起人", StrUtil.emptyIfNull(user.getRealName())),
        new Approval.FormComponentValue("本次导入价格库数量", String.valueOf(count)),
        new Approval.FormComponentValue("供应商", StrUtil.emptyIfNull(supplierNames)),
        new Approval.FormComponentValue("查看价格库导入文件", preview_service + encodedFileUrl),
        new Approval.FormComponentValue("下载价格库导入文件", fileUrl));
    Approval instance = Approval.createInstance(dingTalkUserId,
        srmConfig.getDingTalkApprovalTemplatePurchaseInfoRecord(), formComponentValues, null,
        deptIds.get(0));
    ApprovalInstanceResult approval = dingUtils.createApproval(instance);
    if (approval == null || StrUtil.isBlank(approval.getInstanceId())) {
      throw new CheckException("钉钉审批创建失败，请联系管理员");
    }
    return approval.getInstanceId();
  }

  @Override
  public void approveFromAdmin(ApprovalResult param) {
    if (Objects.equals(param.getResult(), DingTalkApproveEventResultEnum.AGREE.getType())) {
      sharePurchaseInfoRecordService.doPassHandle(param);
    }
    if (Objects.equals(param.getResult(), DingTalkApproveEventResultEnum.REFUSE.getType())) {
      sharePurchaseInfoRecordService.doRejectHandle(param);
    }
  }

  @Override
  public PurchaseInfoRecordImportProgressVo getImportProgress(String taskId) {
    PurchaseInfoRecordImportProgressVo result = getTaskStatus(taskId);
    result.setProcessedNums(getProcessedNums(taskId));
    if (StrUtil.equalsAny(result.getStage(),Constants.STATE_OK,Constants.IMPORT_STATE_EXCEPTION)) {
      // 删除缓存
      redisUtil.del(getTaskKey(taskId));
      redisUtil.del(getProcessedNumsKey(taskId));
    }
    return result;
  }

  @Override
  @Async
  public void initProductDesc() {
    int pageNo = 0;
    int pageSize = 1000;
    Page<PurchaseInfoRecord> recordPage =
        repository.findNullDescPage(PageRequest.of(pageNo, pageSize, Direction.ASC,"productCode"));
    while (recordPage != null && CollUtil.isNotEmpty(recordPage.getContent())) {
      log.info("采购价格库物料描述初始化-第{}页", pageNo);
      List<PurchaseInfoRecord> content = recordPage.getContent();
      List<String> codeList = content.stream().map(PurchaseInfoRecord::getProductCode).distinct()
          .collect(Collectors.toList());
      Map<String, String> code2Desc =
          CollUtil.emptyIfNull(productService.getSrmProductInfoList(codeList, null)).stream()
              .collect(Collectors.toMap(SrmProductInfoDTO::getCode, SrmProductInfoDTO::getDesc,
                  (k1, k2) -> k1));
      for (PurchaseInfoRecord purchaseInfoRecord : content) {
        purchaseInfoRecord.setDescription(code2Desc.get(purchaseInfoRecord.getProductCode()));
      }
      repository.saveAll(content);
      pageNo ++;
      recordPage = repository.findNullDescPage(PageRequest.of(pageNo, pageSize, Direction.ASC,"productCode"));
    }
    log.info("采购价格库物料描述初始化-完成");
  }

  private void saveTaskStatus(String taskId, PurchaseInfoRecordImportProgressVo progressVo) {
    redisUtil.set(getTaskKey(taskId),
        progressVo,
        PURCHASE_INFO_RECORD_IMPORT_RESULT_EXPIRE_TIME);
  }

  private PurchaseInfoRecordImportProgressVo getTaskStatus(String taskId) {
    Object cachedProgress = redisUtil.get(getTaskKey(taskId));
    if (ObjectUtil.isNull(cachedProgress)) {
      throw new CheckException("导入任务不存在或已过期");
    }
    return ((JSONObject) cachedProgress).toJavaObject(PurchaseInfoRecordImportProgressVo.class);
  }

  private static String getTaskKey(String taskId) {
    return TASK_REDIS_KEY + "::" + taskId;
  }

  private int getProcessedNums(String taskId) {
    Object cachedProgress = redisUtil.get(getProcessedNumsKey(taskId));
    if (ObjectUtil.isNotNull(cachedProgress)) {
      return (int) cachedProgress;
    }
    return 0;
  }

  private void increaseProcessedNums(String taskId) {
    redisUtil.incr(getProcessedNumsKey(taskId), 1);
  }

  private String getProcessedNumsKey(String taskId) {
    return PROCESSED_REDIS_KEY + "::" + taskId;
  }
}
