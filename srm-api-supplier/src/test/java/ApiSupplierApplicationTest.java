import com.alibaba.fastjson.JSON;
import com.xhgj.srm.api.ApiSupplierApplication;
import com.xhgj.srm.api.service.impl.OrderFilingServiceImpl;
import com.xhgj.srm.common.dto.Approval;
import com.xhgj.srm.common.utils.dingding.DingUtils;
import com.xhgj.srm.jpa.entity.OrderFiling;
import com.xhgj.srm.jpa.repository.OrderFilingRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import javax.annotation.Resource;

/**
 * @Author: fanghuanxu
 * @Date: 2025/3/19 15:17
 * @Description: ApiSupplierApplicationTest
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApiSupplierApplication.class)
public class ApiSupplierApplicationTest {
  @Autowired
  private OrderFilingServiceImpl orderFilingService;
  @Autowired
  private OrderFilingRepository orderFilingRepository;
  @Resource
  private DingUtils dingUtils;
  @Test
  public void test() {

    OrderFiling orderFiling =
        orderFilingRepository.findById("40289b288b94b1cd018b94b5a0260016").get();
    orderFiling.setDockingSalesId("4c90f03f8f241678019005ab015e007c");
    //orderFilingService.createProductFilingApproval(orderFiling);
  }

  public static void main(String[] args) {
    System.out.println(JSON.toJSONString(new String[] {"dingId"}));
  }
}
