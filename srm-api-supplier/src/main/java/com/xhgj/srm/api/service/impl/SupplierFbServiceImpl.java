package com.xhgj.srm.api.service.impl;


import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.service.SupplierFbService;
import com.xhgj.srm.api.service.SupplierService;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhgj.srm.jpa.entity.SupplierFb;
import com.xhgj.srm.jpa.repository.SupplierFbRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class SupplierFbServiceImpl implements SupplierFbService {

    @Autowired
    SupplierFbRepository supplierFbRepository;

    @Autowired
    SupplierService supplierService;

    @Override
    public BootBaseRepository<SupplierFb, String> getRepository() {
        return supplierFbRepository;
    }

    @Transactional
    @Override
    public SupplierFb addBySupplier(
            String supplierId) {
        Supplier supplier =
                supplierService.get(
                        supplierId, () -> CheckException.noFindException(Supplier.class, supplierId));
        SupplierFb supplierFb = new SupplierFb();
        supplierFb.setSupplier(supplier);
        supplierFb.setSupplierId(supplier.getId());
        supplierFb.setEnterpriseName(StrUtil.emptyIfNull(supplier.getEnterpriseName()));
        supplierFb.setEnterpriseNature(StrUtil.emptyIfNull(supplier.getEnterpriseNature()));
        supplierFb.setEnterpriseLevel(StrUtil.emptyIfNull(supplier.getEnterpriseLevel()));
        supplierFb.setIndustry(StrUtil.emptyIfNull(supplier.getIndustry()));
        supplierFb.setMobile(StrUtil.emptyIfNull(supplier.getMobile()));
        supplierFb.setCreateGroup(StrUtil.emptyIfNull(supplier.getCreateGroup()));
        supplierFb.setCreateCode(StrUtil.emptyIfNull(supplier.getCreateCode()));
        supplierFb.setUseGroup(StrUtil.emptyIfNull(supplier.getUseGroup()));
        supplierFb.setUseCode(StrUtil.emptyIfNull(supplier.getUseCode()));
        supplierFb.setErpid(StrUtil.emptyIfNull(supplier.getErpid()));
        supplierFb.setErpCode(StrUtil.emptyIfNull(supplier.getErpCode()));
        supplierFb.setCode(StrUtil.emptyIfNull(supplier.getCode()));
        supplierFb.setCountry(StrUtil.emptyIfNull(supplier.getCountry()));
        supplierFb.setProvince(StrUtil.emptyIfNull(supplier.getProvince()));
        supplierFb.setCity(StrUtil.emptyIfNull(supplier.getCity()));
        supplierFb.setDetails(StrUtil.emptyIfNull(supplier.getDetails()));
        supplierFb.setLicense(StrUtil.emptyIfNull(supplier.getLicense()));
        supplierFb.setDate(supplier.getDate() != null && supplier.getDate() > 0 ? supplier.getDate() : 0);
        supplierFb.setStartDate(supplier.getStartDate() != null && supplier.getStartDate() > 0 ? supplier.getStartDate() : 0);
        supplierFb.setEndDate(supplier.getEndDate() != null && supplier.getEndDate() > 0 ? supplier.getEndDate() : 0);
        supplierFb.setCorporate(StrUtil.emptyIfNull(supplier.getCorporate()));
        supplierFb.setRegNo(StrUtil.emptyIfNull(supplier.getRegNo()));
        supplierFb.setRegCapital(StrUtil.emptyIfNull(supplier.getRegCapital()));
        supplierFb.setPaidCapital(StrUtil.emptyIfNull(supplier.getPaidCapital()));
        supplierFb.setLicenseUrl(StrUtil.emptyIfNull(supplier.getLicenseUrl()));
        supplierFb.setUscc(!StringUtils.isNullOrEmpty(supplier.getUscc()) ? supplier.getUscc() : "");
        supplierFb.setRegAddress(StrUtil.emptyIfNull(supplier.getRegAddress()));
        supplierFb.setBankName(StrUtil.emptyIfNull(supplier.getBankName()));
        supplierFb.setBankNum(StrUtil.emptyIfNull(supplier.getBankNum()));
        supplierFb.setBankAccount(StrUtil.emptyIfNull(supplier.getBankAccount()));
        supplierFb.setBankCode(StrUtil.emptyIfNull(supplier.getBankCode()));
        supplierFb.setSettleCurrency(StrUtil.emptyIfNull(supplier.getSettleCurrency()));
        supplierFb.setTaxNo(StrUtil.emptyIfNull(supplier.getTaxNo()));
        supplierFb.setBusinessScope(StrUtil.emptyIfNull(supplier.getBusinessScope()));
        supplierFb.setTaxNumber(StrUtil.emptyIfNull(supplier.getTaxNumber()));
        supplierFb.setManageType(StrUtil.emptyIfNull(supplier.getManageType()));
        supplierFb.setInvoiceType(StrUtil.emptyIfNull(supplier.getInvoiceType()));
        supplierFb.setTaxRate(StrUtil.emptyIfNull(supplier.getTaxRate()));
        supplierFb.setCreateTime(System.currentTimeMillis());
        supplierFb.setPurchaserId(StrUtil.emptyIfNull(supplier.getPurchaserId()));
        supplierFb.setPurchaserName(StrUtil.emptyIfNull(supplier.getPurchaserName()));
        supplierFb.setDepartName(StrUtil.emptyIfNull(supplier.getDepartName()));
        supplierFb.setDepartId(StrUtil.emptyIfNull(supplier.getDepartId()));
        supplierFb.setIntegrity(StrUtil.emptyToDefault(supplier.getIntegrity(), "0"));
        supplierFb.setType(StrUtil.emptyIfNull(supplier.getType()));
        supplierFb.setInsNum(StrUtil.emptyIfNull(supplier.getInsNum()));
        supplierFb.setTaxQualification(StrUtil.emptyIfNull(supplier.getTaxQualification()));
        supplierFb.setUsedName(StrUtil.emptyIfNull(supplier.getUsedName()));
        supplierFb.setEnglishName(StrUtil.emptyIfNull(supplier.getEnglishName()));
        supplierFb.setRegNo(StrUtil.emptyIfNull(supplier.getRegNo()));
        supplierFb.setOrgCode(StrUtil.emptyIfNull(supplier.getOrgCode()));
        supplierFb.setRegAuthority(StrUtil.emptyIfNull(supplier.getRegAuthority()));
        supplierFb.setPeopleNum(StrUtil.emptyIfNull(supplier.getPeopleNum()));
        supplierFb.setState(Constants.STATE_OK);
        return supplierFbRepository.save(supplierFb);
    }
}
