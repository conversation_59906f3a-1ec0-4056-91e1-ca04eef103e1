package com.xhgj.srm.api.dto.product;

import com.xhgj.srm.jpa.entity.File;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MDMFileDTO {
  private String baseUrl;
  private String fileName;
  private String fileSize;
  private String fileUrl;

  public MDMFileDTO(File file, String baseUrl) {
    fileUrl = file.getUrl();
    fileName = file.getName();
    this.baseUrl = baseUrl;
  }


}
