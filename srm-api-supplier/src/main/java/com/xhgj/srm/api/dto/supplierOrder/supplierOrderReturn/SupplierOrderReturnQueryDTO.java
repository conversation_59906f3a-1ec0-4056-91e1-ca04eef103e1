package com.xhgj.srm.api.dto.supplierOrder.supplierOrderReturn;

import com.xhgj.srm.api.dto.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/** <AUTHOR> @ClassName QuerySupplierOrderDTO */
@Data
public class SupplierOrderReturnQueryDTO extends BaseQuery {
  @ApiModelProperty("单据类型 3-退货单 4-取消单")
  private String supplierOrderReturnType;

  @ApiModelProperty("单号")
  private String supplierOrderReturnNumber;

  @ApiModelProperty("采购订单号")
  private String supplierOrderReturnCode;

  @ApiModelProperty("采购员")
  private String supplierOrderReturnReceiveMan;

  @ApiModelProperty("采购组织")
  private String supplierOrderReturnGroupName;

  @ApiModelProperty("是否厂直发")
  private Boolean supplierOrderDirectShipment;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty("开始-生成时间")
  private String supplierOrderReturnStartTime;

  @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty("结束-生成时间")
  private String supplierOrderReturnEndTime;

  @ApiModelProperty("状态")
  private String status;

  @ApiModelProperty("件数")
  private String supplierOrderReturnNum;

  @ApiModelProperty("金额")
  private String supplierOrderReturnPrice;
}
