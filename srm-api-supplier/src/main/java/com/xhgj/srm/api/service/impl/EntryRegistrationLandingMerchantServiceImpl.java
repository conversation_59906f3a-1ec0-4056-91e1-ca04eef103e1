package com.xhgj.srm.api.service.impl;

import com.xhgj.srm.api.service.EntryRegistrationLandingMerchantService;
import com.xhgj.srm.jpa.dao.EntryRegistrationLandingMerchantDao;
import com.xhgj.srm.jpa.entity.EntryRegistrationLandingMerchant;
import com.xhgj.srm.jpa.repository.EntryRegistrationLandingMerchantRepository;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 入驻报备单落地商信息
 */
@Service
public class EntryRegistrationLandingMerchantServiceImpl implements
    EntryRegistrationLandingMerchantService {

  @Autowired
  private EntryRegistrationLandingMerchantRepository entryRegistrationLandingMerchantRepository;

  @Override
  public BootBaseRepository<EntryRegistrationLandingMerchant, String> getRepository() {
    return entryRegistrationLandingMerchantRepository;
  }

  @Autowired
  EntryRegistrationLandingMerchantDao dao;

  @Override
  public EntryRegistrationLandingMerchant getEntryRegistrationOrderId(String id) {
    return dao.getEntryRegistrationOrderId(id);
  }
}
