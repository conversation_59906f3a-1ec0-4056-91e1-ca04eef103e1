package com.xhgj.srm.api.dto.order;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
  *@ClassName CustomerPaybackParams
  *<AUTHOR>
  *@Date 2023/9/25 14:41
*/
@Data
public class CustomerPaybackDTO {
  @ApiModelProperty("下单平台")
  private String  platform;

  @ApiModelProperty("客户名称")
  private String  customer;

  @ApiModelProperty("总销售金额")
  private BigDecimal allSalesAmount;

  @ApiModelProperty("已回款金额")
  private BigDecimal allRefundPrice;

  @ApiModelProperty("未回款金额")
  private BigDecimal unRefundPrice;
}
