package com.xhgj.srm.api.controller;

import com.xhgj.srm.common.dto.XhgjPersonInfoDTO;
import com.xhgj.srm.service.OAUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by Geng Shy on 2023/8/29
 */
@RestController
@RequestMapping("/oa-user")
@Api(tags = {"oa用户接口"})
@Slf4j
@Validated
public class OAUserController {

  @Resource
  private OAUserService oaUserService;

  @GetMapping("/info")
  @ApiModelProperty("获取oa用户信息（手机号是加密的）")
  public XhgjPersonInfoDTO getOAUserList(String name, String mobile) {
    return oaUserService.getOAUserInfo(name, mobile);
  }
}
