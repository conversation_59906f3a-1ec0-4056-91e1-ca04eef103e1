package com.xhgj.srm.api.service;

import com.xhgj.srm.api.dto.InvoiceResultParam;
import com.xhgj.srm.api.dto.OrderInvoiceRelationDTO;
import com.xhgj.srm.api.dto.OrderInvoiceRelationPageQuery;
import com.xhgj.srm.api.dto.SupplierInvoiceSaveParam;
import com.xhgj.srm.api.dto.account.AccountDetailDTO;
import com.xhgj.srm.api.dto.account.AccountOpenInvoiceParams;
import com.xhgj.srm.api.dto.account.AccountPaymentDTO;
import com.xhgj.srm.api.dto.account.InvoiceOrderPageDTO;
import com.xhgj.srm.api.dto.account.InvoiceOrderPageQuery;
import com.xhgj.srm.api.dto.supplier.invoice.SupplierInvoiceDetailsDTO;
import com.xhgj.srm.api.dto.supplier.invoice.SupplierOpenInvoiceDetailsDTO;
import com.xhgj.srm.api.dto.supplierinvoice.SaveLogisticsParam;
import com.xhgj.srm.api.dto.supplierinvoice.SupplierInvoiceOrderPageParam;
import com.xhgj.srm.api.dto.supplierinvoice.SupplierInvoiceOrderPageVO;
import com.xhgj.srm.common.dto.invoice.InvoiceIdentifyResult;
import com.xhgj.srm.common.dto.invoice.InvoiceOcrRecognitionResultDTO;
import com.xhgj.srm.jpa.entity.OrderAccount;
import com.xhiot.boot.framework.jpa.service.BootBaseService;
import com.xhiot.boot.mvc.base.PageResult;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.springframework.data.domain.Pageable;

public interface OrderSupplierInvoiceService extends BootBaseService<OrderAccount, String> {

  /**
   * 获取对账单详情
   *
   * @param accountId
   * @return
   */
  AccountDetailDTO getAccountDetail(String accountId);
  /**
   * 分页获取可对账列表
   *
   * @param query    查询条件
   * @param pageable 分页参数
   */
  PageResult<InvoiceOrderPageDTO> getCanInvoiceOrderPage(InvoiceOrderPageQuery query,
      Pageable pageable);

  /**
   * 对账单开票
   * @param params 参数
   */
  void accountOpenInvoice(AccountOpenInvoiceParams params);

  AccountPaymentDTO getDataCount(String supplierId);

  /**
   * 删除对账单关联数据
   * @param orderAccountId
   */
  void deleteOrderAccountRelevanceData(String orderAccountId);

  /**
   * 删除对账单发票相关信息
   * @param fileIds
   * @param orderAccountInvoiceIds
   */
  void deleteOrderAccountInvoiceRelevanceData(List<String> fileIds,
      List<String> orderAccountInvoiceIds);


  /**
   * 获取对账单下所有下单平台 - 所包含订单的下单平台 v5.7调整为相同下单平台的订单才能批量对账。
   * 查询为空时返回null
   * 默认去重
   * @param orderAccountId
   * @param distinct 入参为false时不去除重复，其他情况都去除重复。
   * @return 平台code值
   */
  Collection<String> getPlatforms(String orderAccountId, boolean distinct);

  /**
   * 导出寄票随单
   * @param id
   * @return
   */
  byte[] downloadAcceptTemp(List<String> id);
  /**
   * 导出供应商寄票随单
   * @param id 订单id
   */
  byte[] downloadSupplierOrderAcceptTemp(List<String> id);

  /**
   * 新增发票
   * @param param
   * @return 新增关联数据的id
   */
  String save(SupplierInvoiceSaveParam param);

  /**
   * 供应商新增发票
   * @param param
   * @return 新增关联数据的id
   */
  String supplierInvoiceSave(SupplierInvoiceSaveParam param);

  /**
   * 删除发票，并且处理相关数据。
   * @param invoiceId 供应商发票id
   */
  void deleteInvoice(String invoiceId);

  /**
   * 分页获取供应商开票和订单中间表数据
   * @param query
   * @return
   */
  PageResult<OrderInvoiceRelationDTO> getRelationPage(OrderInvoiceRelationPageQuery query);
  /**
   * 获取订单和进项票关联数据详情
   */
  SupplierInvoiceDetailsDTO getRelationDataDetails(String relationDataId);

  /**
   * 获取供应商订单和进项票关联数据详情
   */
  SupplierOpenInvoiceDetailsDTO getSupplierInvoiceRelationDataDetails(String relationDataId);

  /**
   * 获取发票验证结果
   * @return
   */
  InvoiceIdentifyResult getVerificationResult(InvoiceResultParam invoiceParam);

  /**
   * 删除暂存的发票单
   * @param invoiceOrderId 发票单id
   * @return
   */
  Boolean deleteInvoiceOrder(String invoiceOrderId);

  /**
   * 保存发票单的物流信息
   */
  boolean saveLogistics(SaveLogisticsParam saveLogisticsParam);
  /**
   * 导出可开票明细
   */
  byte[] exportDetail(List<String> orderIds);

  /**
   * @param param 查询条件
   */
  PageResult<SupplierInvoiceOrderPageVO> getSupplierCanInvoiceOrderPage(SupplierInvoiceOrderPageParam param);

  /**
   * @param orderIds 供应商订单集合
   * @return 供应商订单明细
   */
  byte[] exportSupplierOrderDetail(List<String> orderIds);
}
