package com.xhgj.srm.api.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.xhgj.srm.common.constants.Constants_LockName;
import com.xhgj.srm.common.enums.order.OrderPaymentSource;
import com.xhgj.srm.dto.order.CreatePaymentOrderResult;
import com.xhgj.srm.dto.order.OrderPaymentInfoDTO;
import com.xhgj.srm.dto.order.OrderPaymentListDTO;
import com.xhgj.srm.dto.order.OrderPaymentListQuery;
import com.xhgj.srm.dto.order.SubmitPaymentOrderApiParams;
import com.xhgj.srm.dto.order.SubmitPaymentOrderParams;
import com.xhgj.srm.dto.order.needPayment.NeedPaymentPreCheckForApi;
import com.xhgj.srm.service.OrderNeedPaymentService;
import com.xhgj.srm.service.OrderPaymentService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.framework.web.dto.param.PageParam;
import com.xhiot.boot.framework.web.util.PageResultBuilder;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import com.xhiot.boot.repeat.annotation.RepeatSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/** <AUTHOR> @ClassName OrderPaymentController */
@RestController
@RequestMapping("orderPayment")
@Api(tags = {"付款订单接口"})
@Slf4j
public class OrderPaymentController {
  @Autowired private OrderPaymentService service;
  @Resource
  private RedissonClient redissonClient;
  @Resource
  private OrderNeedPaymentService orderNeedPaymentService;

  @ApiOperation(value = "提交付款申请")
  @PostMapping(value = "/submitPaymentOrder", consumes = MediaType.APPLICATION_JSON_VALUE)
  // 产品害怕重复调用了ERP的接口创建付款单，强烈建议重复校验为1分钟
  @RepeatSubmit(interval = 60000)
  public ResultBean<Boolean> submitPaymentOrder(
      @RequestBody @Valid SubmitPaymentOrderApiParams params) {
    RLock lock = null;
    CreatePaymentOrderResult createPaymentOrderResult;
    try {
      lock = redissonClient.getLock(Constants_LockName.SUBMIT_PAYMENT_APPLY + params.getSupplierId());
      lock.lock();
      params.setSource(OrderPaymentSource.FRONTEND.getCode());
      createPaymentOrderResult = service.submitPaymentOrderForApi(params);
    } catch (CheckException checkException) {
      throw checkException;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e, -1));
      throw new CheckException("未知异常，请联系管理员！");
    }finally {
      if (lock != null) {
        lock.unlock();
      }
    }
    return new ResultBean<>(true);
  }

  @ApiOperation(value = "分页获取付款单列表")
  @GetMapping(value = "/getOrderPaymentPage")
  public ResultBean<PageResult<OrderPaymentListDTO>> getOrderPaymentPage(
      @Valid OrderPaymentListQuery orderPaymentListQuery, @Valid PageParam params) {
    return new ResultBean<>(
        PageResultBuilder.buildPageResult(service.getOrderPaymentPage(orderPaymentListQuery, params.toPageable())));
  }

  @ApiOperation(value = "获取付款单详情")
  @GetMapping(value = "/getOrderPaymentInfoById")
  public ResultBean<OrderPaymentInfoDTO> getOrderPaymentInfoById(
      @NotBlank(message = "付款单id必传") @RequestParam String paymentId) {
    return new ResultBean<>(service.getOrderPaymentInfoById(paymentId));
  }

  /**
   * 提交付款预校验
   */
  @ApiOperation(value = "提交付款预校验")
  @PostMapping(value = "/preCheck")
  public ResultBean<Boolean> preCheck(@RequestBody @Valid NeedPaymentPreCheckForApi form) {
    orderNeedPaymentService.preCheck(form.getOrderIds());
    return new ResultBean<>(true);
  }
}
