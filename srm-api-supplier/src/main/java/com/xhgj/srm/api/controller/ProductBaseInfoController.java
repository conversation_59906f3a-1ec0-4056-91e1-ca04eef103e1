package com.xhgj.srm.api.controller;

import com.xhgj.srm.api.service.BrandService;
import com.xhgj.srm.api.service.ProductService;
import com.xhgj.srm.common.dto.MdmBrandPageData;
import com.xhgj.srm.request.service.third.mpm.MPMService;
import com.xhgj.srm.request.vo.mpm.MPMCategoryVO;
import com.xhgj.srm.request.vo.mpm.MPMIntelligentCodeVO;
import com.xhgj.srm.request.vo.mpm.MPMUnitVO;
import com.xhiot.boot.mvc.base.PageResult;
import com.xhiot.boot.mvc.base.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 物料接口
 * 物料基础信息获取
 */
@Api(value = "/product", tags = {"物料接口 物料基础信息获取"})
@RestController
@RequestMapping("/product")
public class ProductBaseInfoController {

  @Resource
  private MPMService mpmService;
  @Resource
  ProductService productService;
  @Resource
  BrandService brandService;

  /**
   * 慧穗云 - 智能赋码 获取
   */
  @ApiOperation(value = "慧穗云 - 智能赋码 获取", notes = "慧穗云 - 智能赋码 获取",
      httpMethod = "GET")
  @ApiImplicitParam(name = "productName", value = "物料名称", required = true)
  @GetMapping("/intelligentCoding")
  public ResultBean<List<MPMIntelligentCodeVO>> intelligentCoding(String productName) {
    return new ResultBean<>(mpmService.intelligentCoding(productName));
  }

  /**
   * 获取计量单位列表 分页
   * @param name
   * @param pageNo
   * @param pageSize
   * @return
   */
  @ApiOperation(value = "获取计量单位列表 分页", notes = "获取计量单位列表 分页")
  @GetMapping(value = "/getUnitListPage")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "name", value = "单位名称", required = false, dataType = "String")
  })
  public ResultBean<PageResult<MPMUnitVO>> getUnitListPage(
      @RequestParam(required = false, value = "name") String name,
      @RequestParam(required = false, value = "pageNo", defaultValue = "1") Integer pageNo,
      @RequestParam(required = false, value = "pageSize", defaultValue = "20") Integer pageSize
  ) {
    return new ResultBean<>((productService.getUnitList(name, pageNo, pageSize)));
  }

  /**
   * 获取品牌信息 分页
   */
  @ApiOperation(value = "获取品牌信息 分页", notes = "获取品牌信息 分页")
  @GetMapping(value = "/getBrandListPage")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "name", value = "品牌名称", required = false, dataType = "String")
  })
  public ResultBean<PageResult<MdmBrandPageData>> getBrandListPage(
      @RequestParam(required = false, value = "name") String name,
      @RequestParam(required = false, value = "pageNo", defaultValue = "1") Integer pageNo,
      @RequestParam(required = false, value = "pageSize", defaultValue = "20") Integer pageSize
  ) {
    return new ResultBean<>((brandService.getMdmBrandPage(null, name, pageNo, pageSize)));
  }
  /**
   * 获取类目信息 分页
   */
  @ApiOperation(value = "获取类目信息 分页", notes = "获取类目信息 分页")
  @GetMapping(value = "/getCategoryListPage")
  @ApiImplicitParams({
      @ApiImplicitParam(name = "name", value = "四级类目名称", required = false, dataType = "String")
  })
  public ResultBean<PageResult<MPMCategoryVO>> getCategoryListPage(
      @RequestParam(required = false, value = "name") String name,
      @RequestParam(required = false, value = "pageNo", defaultValue = "1") Integer pageNo,
      @RequestParam(required = false, value = "pageSize", defaultValue = "20") Integer pageSize
  ) {
    return new ResultBean<>(mpmService.getCategoryListPage(name, pageNo, pageSize));
  }
}
