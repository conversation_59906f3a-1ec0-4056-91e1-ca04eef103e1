package com.xhgj.srm.api.dto.noticeCenter;

import com.xhgj.srm.api.dto.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/** <AUTHOR> @ClassName QuerySupplierOrderDTO */
@Data
public class NoticeCenterQueryDTO extends BaseQuery {

  @ApiModelProperty("是否已读")
  private Boolean readState;

  @ApiModelProperty("消息内容")
  private String content;

  @ApiModelProperty("消息类型")
  private String messageType;
}
