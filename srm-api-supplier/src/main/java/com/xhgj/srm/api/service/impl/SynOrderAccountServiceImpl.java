package com.xhgj.srm.api.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.service.SynOrderAccountService;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.config.SrmConfig;
import com.xhgj.srm.common.utils.ExportUtil;
import com.xhgj.srm.jpa.dao.OrderAccountProductDetailDao;
import com.xhgj.srm.jpa.entity.OrderAccount;
import com.xhgj.srm.jpa.entity.OrderAccountDetail;
import com.xhgj.srm.jpa.entity.OrderAccountProductDetail;
import com.xhgj.srm.jpa.repository.OrderAccountDetailRepository;
import com.xhgj.srm.jpa.repository.OrderAccountRepository;
import com.xhgj.srm.service.SharePlatformService;
import com.xhiot.boot.core.common.exception.CheckException;
import com.xhiot.boot.core.common.util.BootMailUtil;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SynOrderAccountServiceImpl implements SynOrderAccountService {

    @Autowired
    OrderAccountRepository repository;

    @Autowired
    ExportUtil exportUtil;

    @Autowired
    OrderAccountProductDetailDao orderAccountProductDetailDao;

    @Autowired
    OrderAccountDetailRepository orderAccountDetailRepository;
    @Resource
    private SharePlatformService platformService;

    private final String batchUrl;

    public SynOrderAccountServiceImpl(SrmConfig config) {
        this.batchUrl
                = config.getBatchUrl();
    }


    @Override
    public BootBaseRepository<OrderAccount, String> getRepository() {
        return repository;
    }

    @SneakyThrows
    @Override
    @Async
    public void synBatchExportAccount(String mail, List<String> exportIds) {
      try(XSSFWorkbook workbook = new XSSFWorkbook();) {
        XSSFSheet sheet = (XSSFSheet) exportUtil.createSheet(workbook, "订单信息", Arrays.asList(
            20, 15, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20));
        CellStyle baseStyle = exportUtil.getBaseStyle(workbook);
        CellStyle titleStyle = exportUtil.getTitleStyle(workbook);
        XSSFRow row = sheet.createRow(0);
        exportUtil.createCell(row, 0, "对账单号", titleStyle);//对账单号
        exportUtil.createCell(row, 1, "发起人", titleStyle);
        exportUtil.createCell(row, 2, "提交时间", titleStyle);
        exportUtil.createCell(row, 3, "对账单状态", titleStyle);
        exportUtil.createCell(row, 4, "对账金额", titleStyle);
        exportUtil.createCell(row, 5, "已回金额", titleStyle);
        exportUtil.createCell(row, 6, "客户订单号", titleStyle);
        exportUtil.createCell(row, 7, "最终结算金额", titleStyle);
        exportUtil.createCell(row, 8, "下单时间", titleStyle);
        exportUtil.createCell(row, 9, "下单平台", titleStyle);
        exportUtil.createCell(row, 10, "客户名称", titleStyle);
        exportUtil.createCell(row, 11, "收件人", titleStyle);
        exportUtil.createCell(row, 12, "联系方式", titleStyle);
        exportUtil.createCell(row, 13, "商品编码", titleStyle);
        exportUtil.createCell(row, 14, "品牌", titleStyle);
        exportUtil.createCell(row, 15, "商品名称", titleStyle);
        exportUtil.createCell(row, 16, "型号", titleStyle);
        exportUtil.createCell(row, 17, "数量", titleStyle);
        exportUtil.createCell(row, 18, "单位", titleStyle);
        exportUtil.createCell(row, 19, "单价", titleStyle);
        int rowNum = 1;
        for (String id : exportIds) {
          OrderAccount orderAccount = repository.findById(id).orElseThrow(() -> CheckException.noFindException(OrderAccount.class, id));
          if (orderAccount != null) {
            List<OrderAccountProductDetail> orderAccountProductDetailList = orderAccountProductDetailDao.getOrderAccountProductDetailListByOrderNo(id, "");
            if (CollUtil.isNotEmpty(orderAccountProductDetailList)) {
              for (OrderAccountProductDetail orderAccountProductDetail : orderAccountProductDetailList) {
                if (orderAccountProductDetail != null) {
                  OrderAccountDetail orderAccountDetail = orderAccountDetailRepository.findById(orderAccountProductDetail.getAccountDetailId()).orElseThrow(()
                      -> CheckException.noFindException(OrderAccountDetail.class, orderAccountProductDetail.getAccountDetailId()));
                  if (orderAccountDetail != null) {
                    String name =
                        platformService.findNameByCode(orderAccountDetail.getType());
                    XSSFRow row1 = sheet.createRow(rowNum);
                    exportUtil.createCell(row1, 0, orderAccount.getAccountNo(), baseStyle);//对账单号
                    exportUtil.createCell(row1, 1, StringUtils.emptyIfNull(orderAccount.getCreateSupplier()), baseStyle);
                    exportUtil.createCell(row1, 2, orderAccount.getCommitTime() > 0 ? DateUtils.formatTimeStampToNormalDate(orderAccount.getCreateTime()) : "", baseStyle);
                    exportUtil.createCell(row1, 3, !StringUtils.isNullOrEmpty(orderAccount.getAccountState()) ? Constants_order.ORDER_ACCOUNT_STATE_MAP.get(orderAccount.getAccountState()) : "", baseStyle);
                    exportUtil.createCell(row1, 4, orderAccount.getPrice() != null && !NumberUtil.equals(BigDecimal.ZERO,orderAccount.getPrice()) ? orderAccount.getPrice() : "0", baseStyle);
                    exportUtil.createCell(row1, 5, orderAccount.getReturnPrice() != null && !NumberUtil.equals(BigDecimal.ZERO,orderAccount.getReturnPrice()) ? orderAccount.getReturnPrice() : "0", baseStyle);
                    exportUtil.createCell(row1, 6, StringUtils.emptyIfNull(orderAccountDetail.getOrderNo()), baseStyle);
                    exportUtil.createCell(row1, 7, orderAccountDetail.getPrice() != null && !NumberUtil.equals(BigDecimal.ZERO,orderAccountDetail.getPrice())  ? orderAccountDetail.getPrice() : "0", baseStyle);
                    exportUtil.createCell(row1, 8, orderAccountDetail.getOrderTime() > 0 ? DateUtils.formatTimeStampToNormalDate(orderAccountDetail.getOrderTime()) : "", baseStyle);
                    exportUtil.createCell(row1, 9,  StrUtil.emptyIfNull(name), baseStyle);
                    exportUtil.createCell(row1, 10, StringUtils.emptyIfNull(orderAccountDetail.getCustomer()), baseStyle);
                    exportUtil.createCell(row1, 11, StringUtils.emptyIfNull(orderAccountDetail.getConsignee()), baseStyle);
                    exportUtil.createCell(row1, 12, StringUtils.emptyIfNull(orderAccountDetail.getMobile()), baseStyle);
                    exportUtil.createCell(row1, 13, StringUtils.emptyIfNull(orderAccountProductDetail.getCode()), baseStyle);
                    exportUtil.createCell(row1, 14, StringUtils.emptyIfNull(orderAccountProductDetail.getBrand()), baseStyle);
                    exportUtil.createCell(row1, 15, StringUtils.emptyIfNull(orderAccountProductDetail.getName()), baseStyle);
                    exportUtil.createCell(row1, 16, StringUtils.emptyIfNull(orderAccountProductDetail.getModel()), baseStyle);
                    exportUtil.createCell(row1, 17, orderAccountProductDetail.getNum() != null ? orderAccountProductDetail.getNum() : "0", baseStyle);
                    exportUtil.createCell(row1, 18, StringUtils.emptyIfNull(orderAccountProductDetail.getUnit()), baseStyle);
                    exportUtil.createCell(row1, 19, orderAccountProductDetail.getPrice() != null ? orderAccountProductDetail.getPrice() : "0", baseStyle);
                    rowNum += 1;
                  }
                }
              }
            }
          }
        }
        String fileName = "account" + DateUtils.formatTimeStampToNormalDate(System.currentTimeMillis()) + ".xlsx";
        String path = batchUrl;
        //判断文件夹是否存在
        File parent = new File(path);
        if (!parent.exists()) {
          parent.mkdirs();
        }
        File file = new File(path, fileName);
        try(OutputStream os = new FileOutputStream(file);) {
          workbook.write(os);
          os.flush();
        }
        BootMailUtil.sendAttachmentsMail(mail, "履约平台对账单信息，请查收", "履约平台对账单邮件", file);
      }
    }
}
