package com.xhgj.srm.api.dto.supplierOrder;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.api.dto.FileDTO;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderRefuseState;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.jpa.entity.File;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

/** <AUTHOR> @ClassName SupplierOrderDTO */
@Data
@NoArgsConstructor
public class SupplierOrderDetailDTO extends BaseSupplierOrderDTO {
  @ApiModelProperty("采购组织")
  private String purchaseGroupName;

  @ApiModelProperty("退货取消金额")
  private BigDecimal cancelReturnPrice;

  @ApiModelProperty("最终结算金额")
  private BigDecimal finalPrice;

  @ApiModelProperty("联系方式")
  private String receiveMobile;

  @ApiModelProperty("收件地址")
  private String receiveAddress;

  @ApiModelProperty("物料明细")
  private List<SupplierOrderDetailedProductDTO> detailedProductDTOList;
  @ApiModelProperty("订单附件")
  private List<FileDTO> annexDTOs;

  @ApiModelProperty("是否有拒单")
  private Boolean refuseState;

  @ApiModelProperty("拒单理由")
  private String refuseReason;

  @ApiModelProperty("订单合同")
  private List<FileDTO> contractFiles;

  @ApiModelProperty("是否有待确认")
  private Boolean confirmState;

  @ApiModelProperty("是否有取消")
  private Boolean cancelState;

  @ApiModelProperty("是否有退货")
  private Boolean returnState;

  public SupplierOrderDetailDTO(SupplierOrder supplierOrder, boolean hasReturnOrder) {
    super(supplierOrder);
    this.purchaseGroupName = StrUtil.emptyIfNull(supplierOrder.getGroupName());
    this.cancelReturnPrice =
        ObjectUtil.defaultIfNull(supplierOrder.getCancelReturnPrice(), new BigDecimal(0));
    this.finalPrice = supplierOrder.getFinalPrice();
    this.receiveMobile = StrUtil.emptyIfNull(supplierOrder.getReceiveMobile());
    this.receiveAddress = StrUtil.emptyIfNull(supplierOrder.getReceiveAddress());
    this.refuseState =
        StrUtil.equals(supplierOrder.getRefuseState(), SupplierOrderRefuseState.REFUSE.getKey());
    this.refuseReason = supplierOrder.getRefuseReason();
    this.confirmState = supplierOrder.getOrderConfirmState();
    this.cancelState = supplierOrder.getOrderCancelState();
    this.returnState = supplierOrder.getOrderReturnState() && hasReturnOrder;
    if (Boolean.TRUE.equals(this.cancelState) || Boolean.TRUE.equals(this.returnState)) {
      this.confirmState = false;
    }
    // 如果订单履约状态不为待履约则不待确认按钮
    if (!supplierOrder.getOrderState().equals(SupplierOrderState.WAIT.getKey())) {
      this.confirmState = false;
    }
  }

  public void setAnnexDTOs(Collection<File> annex, String baseUrl) {
    ArrayList<FileDTO> fileDTOS = new ArrayList<>();
    for (File file : annex) {
      FileDTO fileDTO = new FileDTO();
      fileDTO.setId(file.getId());
      fileDTO.setUrl(file.getUrl());
      fileDTO.setName(file.getName());
      fileDTO.setBaseUrl(baseUrl);
      if (StrUtil.isNotBlank(file.getDescription())) {
        String[] split = StrUtil.split(file.getDescription(), ".");
        String fileType = ArrayUtil.get(split, -1);
        fileDTO.setType(fileType);
      }
      fileDTOS.add(fileDTO);
    }
    annexDTOs = fileDTOS;
  }

  public void setContractFiles(Collection<File> annex, String baseUrl) {
    ArrayList<FileDTO> fileDTOS = new ArrayList<>();
    for (File file : annex) {
      FileDTO fileDTO = new FileDTO();
      fileDTO.setId(file.getId());
      fileDTO.setUrl(file.getUrl());
      fileDTO.setName(file.getName());
      fileDTO.setBaseUrl(baseUrl);
      if (StrUtil.isNotBlank(file.getDescription())) {
        String[] split = StrUtil.split(file.getDescription(), ".");
        String fileType = ArrayUtil.get(split, -1);
        fileDTO.setType(fileType);
      }
      fileDTOS.add(fileDTO);
    }
    contractFiles = fileDTOS;
  }
}
