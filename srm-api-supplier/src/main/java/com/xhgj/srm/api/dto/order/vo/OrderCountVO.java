package com.xhgj.srm.api.dto.order.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrderCountVO {

  @ApiModelProperty("待履约订单个数")
  private Long waitCount;
  @ApiModelProperty("履约中订单个数")
  private Long processCount;
  @ApiModelProperty("待验收订单个数")
  private Long acceptedCount;
  @ApiModelProperty("已完成订单个数")
  private Long completeCount;
  @ApiModelProperty("代发货订单个数")
  private Long dropShippingCount;
}
