package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.InventoryDao;
import com.xhgj.srm.jpa.entity.Inventory;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * InventoryDaoImpl
 */
@Service
public class InventoryDaoImpl extends  AbstractExtDao<Inventory> implements InventoryDao {

  @Override
  public Page<Inventory> getAllInventoryList(Map<String, Object> map) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    sql.append("select i.* from t_inventory i where i.c_state=? ");
    params.add(Constants.STATE_OK);
    if (!StrUtil.isBlankIfStr(map.get("productCode"))) {
      sql.append("and i.c_product_code like ? ");
      params.add("%" + map.get("productCode") + "%");
    }
    if (!StrUtil.isBlankIfStr(map.get("groupCode"))) {
      sql.append("and i.c_group_code = ? ");
      params.add(map.get("groupCode"));
    }
    if (!StrUtil.isBlankIfStr(map.get("warehouse"))) {
      sql.append(" and i.c_warehouse = ? ");
      params.add(map.get("warehouse"));
    }
    if (!StrUtil.isBlankIfStr(map.get("batchNo"))) {
      sql.append(" and i.c_batch_no like ? ");
      params.add("%" + map.get("batchNo") + "%");
    }
    if (!StrUtil.isBlankIfStr(map.get("brand"))) {
      sql.append(" and ( i.c_brand_name_cn like ? or i.c_brand_name_en like ? ) ");
      params.add("%" + map.get("brand") + "%");
      params.add("%" + map.get("brand") + "%");
    }
    if (!StrUtil.isBlankIfStr(map.get("name"))) {
      sql.append(" and i.c_name like ? ");
      params.add("%" + map.get("name") + "%");
    }
    if (!StrUtil.isBlankIfStr(map.get("model"))) {
      sql.append(" and i.c_model like ? ");
      params.add("%" + map.get("model") + "%");
    }
    if (!StrUtil.isBlankIfStr(map.get("purchaseDepartment"))) {
      sql.append(" and i.c_purchase_department like ? ");
      params.add("%" + map.get("purchaseDepartment") + "%");
    }

    String purchaseMan = Convert.toStr(map.get("purchaseMan"));
    if (StrUtil.isNotBlank(purchaseMan)) {
      List<String> split = StrUtil.split(purchaseMan, ',', true, true);
      sql.append(" and ( ");
      for (int i = 0; i < split.size(); i++) {
        if (i != 0) {
          sql.append(" or ");
        }
        String s = split.get(i);
        sql.append(" i.c_purchase_man like ? ");
        params.add("%" + s + "%");
      }
      sql.append(" ) ");
    }
    // 当前用户部门
    List<String> curUserDeptList = Optional.ofNullable((List<String>) map.get("curUserDeptList"))
        .orElse(Collections.emptyList());
    if (CollUtil.isNotEmpty(curUserDeptList)) {
      sql.append(" and i.c_purchase_department_code in ( ");
      for (int i = 0; i < curUserDeptList.size(); i++) {
        if (i != 0) {
          sql.append(",");
        }
        sql.append("?");
        params.add(curUserDeptList.get(i));
      }
      sql.append(" ) ");
    }
    if (BooleanUtil.isTrue((Boolean) map.get("isFilterTotalZero"))) {
      sql.append( " and COALESCE(i.c_inventory_total_number, 0) !=0 ");
    }
    sql.append(" order by i.c_create_time desc");
    return findPageSql(sql.toString(), params.toArray(), (Integer) map.get("pageNo"), (Integer) map.get(
        "pageSize"));
  }

  @Override
  public List<Inventory> checkInventorySafe(String groupCode, String warehouse, List<String> productCodes) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    sql.append("select i.* from t_inventory i where i.c_state=? ");
    params.add(Constants.STATE_OK);
    sql.append(" and i.c_group_code = ? ");
    params.add(groupCode);
    sql.append(" and i.c_warehouse = ? ");
    params.add(warehouse);
    sql.append(" and i.c_product_code in ( ");
    for (int i = 0; i < productCodes.size(); i++) {
      if (i != 0) {
        sql.append(",");
      }
      sql.append("?");
      params.add(productCodes.get(i));
    }
    sql.append(" ) ");
    return getSqlList(sql.toString(), params.toArray());
  }
}
