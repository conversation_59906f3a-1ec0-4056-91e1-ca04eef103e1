package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.FinancialDao;
import com.xhgj.srm.jpa.entity.Financial;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class FinancialDaoImpl extends AbstractExtDao<Financial> implements FinancialDao {

  @Override
  public List<Financial> getFinancialListBySid(String sid) {
    String hql = "from Financial f where f.state = ? and f.supplier.id = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, sid};
    hql += " order by f.createTime desc";
    return getHqlList(hql, params);
  }

  @Override
  public List<Financial> getFinancialListByFbId(String fbid) {
    String hql = "from Financial f where f.state = ? and f.supplierFb.id = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, fbid};
    hql += " order by f.createTime desc";
    return getHqlList(hql, params);
  }

  @Override
  public void deleteFinancial(String supplierId) {
    String hql = "delete from t_financial where c_state = ? and supplierId = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, supplierId};
    executeSqlUpdate(hql, params);
  }

  @Override
  public Page<Financial> getFinancialListBySid(String sid, String pageNo, String pageSize) {
    String hql = "from Financial  where state = ? and supplier.id = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, sid};
    hql += " order by createTime desc";
    return findPage(hql, params, Integer.parseInt(pageNo), Integer.parseInt(pageSize));
  }

  @Override
  public List<Financial> getFinancialListBySupplierInGroupId(String supplierInGroupId) {
    String hql = "from Financial  where state = ? and supplierInGroupId = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, supplierInGroupId};
    hql += "ORDER BY createTime ASC ";
    return getHqlList(hql, params);
  }

  @Override
  public Financial getPersonSupplierFinancial(String supplierInGroupId) {
    Assert.notEmpty(supplierInGroupId);
    String hql = "from Financial  where state = ? and supplierInGroupId = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, supplierInGroupId};
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public void delFinancialListBySupplierId(String supplierId, String supplierInGroupId) {
    Assert.notEmpty(supplierId);
    String hql = "delete from Financial  where state = ? and supplier.id = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, supplierId};
    if (!StringUtils.isNullOrEmpty(supplierInGroupId)) {
      hql += "and supplierInGroupId = ? ";
      params = ObjectUtils.objectAdd(params, supplierInGroupId);
    }
    executeUpdate(hql, params);
  }

  @Override
  public List<Financial> getFinancialListBySupplierInGroupIdList(
      List<String> supplierInGroupIdList) {
    StringBuilder hql = new StringBuilder("from Financial  where state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    if (CollUtil.isNotEmpty(supplierInGroupIdList)) {
      params = HqlUtil.appendFieldIn(hql, params, "supplierInGroupId", supplierInGroupIdList);
    }
    return getHqlList(hql.toString(), params);
  }

  @Transactional
  @Override
  public void updateFinancialSupplierInGroup(String supplierInGroupId, String supplierId) {
    String hql =
        "update Financial f set f.supplierInGroupId = ? where f.state = ? and f.supplier.id = ? ";
    Object[] params = new Object[] {supplierInGroupId, Constants.STATE_OK, supplierId};
    executeUpdate(hql, params);
  }

  @Override
  public List<Financial> getBySupplierId(String supplierId){
    String hql = "select f.* from t_financial f left join "
        + "t_supplier_in_group sig on f.supplier_in_group_id = sig.id "
        + "where f.c_state =? and sig.c_state = ? and sig.supplier_id = ? order by f.c_createTime ";
    Object[] params = new Object[] {Constants.STATE_OK,Constants.STATE_OK, supplierId};
    return getSqlList(hql,params);
  }

  @Override
  public List<Financial> getBySupplierIdAndGroupCode(String supplierId, String groupCode) {
    String sql = "select f.* from t_financial f "
        + "  left join t_supplier_in_group sig on f.supplier_in_group_id = sig.id "
        + "left join t_group tg " + "on sig.group_id = tg.id "
        + "where f.c_state =? and sig.c_state = ? and sig.supplier_id = ? "
        + "and tg.c_erpCode = ? " + "order by f.c_createTime desc ";
    Object[] params = new Object[] {Constants.STATE_OK,Constants.STATE_OK, supplierId,groupCode};
    return getSqlList(sql,params);
  }
}
