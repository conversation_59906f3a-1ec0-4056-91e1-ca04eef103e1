package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.*;
import javax.validation.constraints.Size;

@Data
@Entity
@Table(name = "t_cache_store")
public class CacheStore {

  /**
   * 主键
   */
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 用户ID
   */
  @Column(name = "user_id", length = 32)
  private String userId;

  /**
   * 缓存类型
   */
  @Column(name = "c_cache_type", length = 2)
  private String cacheType;

  /**
   * 缓存内容
   */
  @Column(name = "c_content")
  private String content;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 更新时间
   */
  @Column(name = "c_update_time")
  private Long updateTime;

  /**
   * 状态
   */
  @Column(name = "c_state", length = 1)
  private String state;
}