package com.xhgj.srm.jpa.dto;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
  *@ClassName PaymentAdvanceDTO
  *<AUTHOR>
  *@Date 2023/12/28 17:17
*/
@Data
public class  PaymentAdvanceDetailDTO {

  /**
   * 申请单号
   */
  @ApiModelProperty("申请单号")
  private String paymentApplyNo;

  /**
   * 最大可预付金额
   */
  @ApiModelProperty("最大可预付金额")
  private BigDecimal maxAdvancePrice;
  /**
   * sap财务凭证号
   */
  @ApiModelProperty("sap财务凭证号")
  private String financialVouchers;
  /**
   * SAP会计年度
   */
  @ApiModelProperty("sap会计年度")
  private String accountingYear;

  /**
   * 申请预付金额
   */
  @ApiModelProperty("申请预付金额")
  private BigDecimal applyAdvancePrice;

  /**
   * 预付账期
   */
  @ApiModelProperty("预付账期")
  private String advancePeriod;

  /**
   * 预计付款日期（以毫秒为单位）
   */
  @ApiModelProperty("预计付款日期（以毫秒为单位）")
  private Long advanceDate;

  /**
   * 付款方式
   */
  @ApiModelProperty("付款方式")
  private String payType;

  @ApiModelProperty("付款方式描述")
  private String payDesc;

  /**
   * 开户行
   */
  @ApiModelProperty("开户行")
  private String bank;

  /**
   * 联行号
   */
  @ApiModelProperty("联行号")
  private String bankCode;

  /**
   * 银行账号
   */
  @ApiModelProperty("银行账号")
  private String bankAccount;

  /**
   * 账户名称
   */
  @ApiModelProperty("账户名称")
  private String accountName;

  /**
   * 采购订单号
   */
  @ApiModelProperty("采购订单号")
  private String supplierOrderNo;
  /**
   * 备注
   */
  @ApiModelProperty("备注")
  private String remark;
  /**
   * 申请状态  1 审核中 2 通过 3 驳回 4 已放弃
   */
  @ApiModelProperty("申请状态  1 审核中 2 通过 3 驳回 4 已放弃")
  private String applyState;

  /**
   * 驳回理由
   */
  @ApiModelProperty("驳回理由")
  private String rejectReason;

  /**
   * 提交人
   */
  @ApiModelProperty("提交人")
  private String createMan;

  /**
   * 购买账号（一次性供应商使用到的字段）
   */
  @ApiModelProperty("购买账号（一次性供应商使用到的字段）")
  private String purchaseAccount;

  /**
   * 订单号（一次性供应商使用到的字段）
   */
  @ApiModelProperty("订单号（一次性供应商使用到的字段）")
  private String orderNo;

  /**
   * 物料名称（一次性供应商使用到的字段）
   */
  @ApiModelProperty("物料名称（一次性供应商使用到的字段）")
  private String productName;

  /**
   * 订单链接（一次性供应商使用到的字段）
   */
  @ApiModelProperty("订单链接（一次性供应商使用到的字段）")
  private String orderLink;


  @ApiModelProperty("供应商 id")
  private String supplierId;

  @ApiModelProperty("采购订单详情列表")
  private List<PaymentAdvanceDetailInfo> PaymentAdvanceDetailInfoList;

  @ApiModelProperty("本次申请总额")
  private BigDecimal allApplyAdvancePrice;

  @ApiModelProperty("供应商名称")
  private String enterpriseName;

  @ApiModelProperty("货币码")
  private String moneyCode;

  @Data
  public static class PaymentAdvanceDetailInfo {
    @ApiModelProperty("采购订单id")
    private String purchaseOrderId;

    @ApiModelProperty("采购订单号")
    private String purchaseOrderNo;

    @ApiModelProperty("订单结算金额")
    private BigDecimal orderSettlementAmount;

    @ApiModelProperty("本次申请预付金额")
    private BigDecimal applyAdvancePrice;
    /**
     * 期初订单标识
     */
    @ApiModelProperty("期初订单标识")
    private Boolean initialOrderFlag;
  }

}
