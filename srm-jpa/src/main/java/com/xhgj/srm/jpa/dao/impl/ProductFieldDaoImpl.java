package com.xhgj.srm.jpa.dao.impl;


import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.ProductFieldDao;
import com.xhgj.srm.jpa.entity.ProductField;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import org.springframework.stereotype.Repository;
import java.util.List;
import org.springframework.transaction.annotation.Transactional;

/**
 * @description 物料上架项目字段表 Dao
 * <AUTHOR>
 * @date 2023-05-06 18:31:11
 */
@Repository
public class ProductFieldDaoImpl extends AbstractExtDao<ProductField> implements ProductFieldDao {

  @Override
  public List<ProductField> getProductFieldByParams(String productId, String platform) {
    StringBuilder hql =new StringBuilder( "from ProductField where state = ? ");
    Object[] params  = new Object[]{Constants.STATE_OK};
    if(!StringUtils.isNullOrEmpty(productId)){
      hql.append(" and productId = ?");
      params = ObjectUtils.objectAdd(params, productId);
    }
    if(!StringUtils.isNullOrEmpty(platform)){
      hql.append(" and platform = ?");
      params = ObjectUtils.objectAdd(params, platform);
    }
    return getHqlList(hql.toString(),params);
  }

  @Transactional
  @Override
  public void deleteByProductId(String productId) {
    String hql = "delete from ProductField e where e.state = ? and e.productId = ? ";
    Object[] params = new Object[]{Constants.STATE_OK, productId};
    executeUpdate(hql, params);
  }
}

