package com.xhgj.srm.jpa.sharding.listenner;/**
 * @since 2025/5/28 22:49
 */

import com.alibaba.fastjson.JSON;
import com.xhgj.srm.common.event.impl.SrmSpringEventPublisher;
import com.xhgj.srm.jpa.entity.BaseSupplierOrder;
import com.xhgj.srm.common.event.newEvent.SrmRedundancyEvent;
import com.xhgj.srm.jpa.entity.v2.index.SupplierOrderIndex;
import com.xhgj.srm.jpa.factory.MapStructFactory;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import javax.persistence.PostPersist;
import javax.persistence.PostRemove;
import javax.persistence.PostUpdate;
import java.util.UUID;

/**
 *<AUTHOR>
 *@date 2025/5/28 22:49:56
 *@description
 */
public class SupplierOrderEntityListener {

  private static SrmSpringEventPublisher publisher;

  public static SrmRedundancyEvent createMessage(BaseSupplierOrder order, String operation) {
    SrmRedundancyEvent message = new SrmRedundancyEvent();
    message.setId(UUID.randomUUID().toString());
    message.setOperation(operation);
    message.setSimpleName(BaseSupplierOrder.class.getSimpleName());
    SupplierOrderIndex supplierOrderIndex = MapStructFactory.INSTANCE.toSupplierOrderIndex(order);
    message.setData(JSON.toJSONString(supplierOrderIndex));
    return message;
  }

  // 静态setter方法，用于依赖注入
  public static void setPublisher(SrmSpringEventPublisher publisher) {
    SupplierOrderEntityListener.publisher = publisher;
  }

  @PostPersist
  public void onPostPersist(BaseSupplierOrder order) {
    SrmRedundancyEvent ev = createMessage(order, "CREATE");
    // 如果当前有活跃事务，就注册一个 afterCommit 回调
    if (TransactionSynchronizationManager.isSynchronizationActive()) {
      TransactionSynchronizationManager.registerSynchronization(
          new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
              // 真正提交后才发
              publisher.asyncPublish(ev);
            }
          });
    } else {
      // 没事务，直接发送，保证“非事务场景”也会走
      publisher.asyncPublish(ev);
    }
  }

  @PostUpdate
  public void onPostUpdate(BaseSupplierOrder order) {
    SrmRedundancyEvent ev = createMessage(order, "UPDATE");
    if (TransactionSynchronizationManager.isSynchronizationActive()) {
      TransactionSynchronizationManager.registerSynchronization(
          new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
              // 真正提交后才发
              publisher.asyncPublish(ev);
            }
          });
    } else {
      // 没事务，直接发送，保证“非事务场景”也会走
      publisher.asyncPublish(ev);
    }
  }

  @PostRemove
  public void onPostRemove(BaseSupplierOrder order) {
    SrmRedundancyEvent ev = createMessage(order, "DELETE");
    if (TransactionSynchronizationManager.isSynchronizationActive()) {
      TransactionSynchronizationManager.registerSynchronization(
          new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
              // 真正提交后才发
              publisher.asyncPublish(ev);
            }
          });
    } else {
      // 没事务，直接发送，保证“非事务场景”也会走
      publisher.asyncPublish(ev);
    }
  }
}
