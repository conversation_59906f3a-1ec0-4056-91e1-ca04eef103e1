package com.xhgj.srm.jpa.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;
import com.xhgj.srm.jpa.util.LazyLoadEntityListener;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

/**
 * 各组织的供应商信息
 *
 * <AUTHOR>
 * @since 2022/7/6 11:07
 */
@Entity
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "t_supplier_in_group")
@EntityListeners(LazyLoadEntityListener.class)
public class SupplierInGroup extends BaseSupplierInGroup {

  // =========================================审核

  /** 审核状态 */
  @Column(name = "c_audit_state")
  private String auditState;
  /** 是否撤回 */
  @Column(name = "c_is_cancel")
  private String isCancel;
  /** 审核人 id */
  @Column(name = "c_manage_id")
  private String manageId;

  // =========================================拉黑

  /** 拉黑人 */
  @Column(name = "c_shielding_people")
  private String shieldingPeople;
  /** 拉黑状态 */
  @Column(name = "c_shield_state")
  private String shieldState;
  /** 拉黑原因 */
  @Column(name = "c_reason")
  private String reason;
  /** 拉黑审核人 */
  @Column(name = "c_shield_manager")
  private String shieldManager;
  /** 拉黑范围 */
  @Column(name = "c_block_range")
  private String blockRange;


  // ========================================= ERP
  /** 标识 erp 是否推送成功 */
  @Column(name = "c_erp_success")
  private String erpSuccess;

  /** erpCode */
  @Column(name = "c_erp_code")
  private String erpCode;

  /** erpId */
  @Column(name = "c_erp_id")
  private String erpId;

  /** 供应商同步状态 */
  @Column(name = "c_syn_state")
  private String synState;

  // =========================================审计字段

  /** 创建时间 */
  @Column(name = "c_create_time")
  private Long createTime;

  /** 数据状态：{@link com.xhgj.srm.common.Constants#COMMONSTATE} */
  @Column(name = "c_state")
  private String state;

  /** 创建人 */
  @Column(name = "c_create_man")
  private String createMan;

  /** 修改人 */
  @Column(name = "c_update_man")
  private String updateMan;

  /** 修改时间 */
  @Column(name = "c_update_time")
  private Long updateTime;

  /**
   * 推荐自营等级，枚举同供应商等级
   * @see com.xhgj.srm.common.enums.supplier.SupplierLevelEnum
   */
  @Column(name = "c_recommended_level")
  private String recommendedLevel;

  // 6.9.0新增字段

  /**
   * 上周期自营合作金额
   */
  @Column(name = "c_last_period_self_amount")
  private BigDecimal lastPeriodSelfAmount;

  /**
   * 自营合作总金额
   */
  @Column(name = "c_self_total_amount")
  private BigDecimal selfTotalAmount;

  /**
   * 电供合作总金额
   */
  @Column(name = "c_ele_total_amount")
  private BigDecimal eleTotalAmount;

  /**
   * 定向合作总金额
   */
  @Column(name = "c_direct_total_amount")
  private BigDecimal directTotalAmount;

  /**
   * 价格库条数
   */
  @Column(name = "c_purchase_record_count")
  private Integer purchaseRecordCount;

  /**
   * 统计金额相关计算更新最新时间(组织内单个供应商维度)
   */
  @Column(name = "c_amount_count_time")
  private Long amountCountTime;

  /**
   * 推荐自营等级更新时间(组织内单个供应商维度)
   */
  @Column(name = "c_recommended_level_time")
  private Long recommendedLevelTime;

  /**
   * 合作性质更新时间(组织内单个供应商维度)
   */
  @Column(name = "c_partnership_type_time")
  private Long partnershipTypeTime;
}
