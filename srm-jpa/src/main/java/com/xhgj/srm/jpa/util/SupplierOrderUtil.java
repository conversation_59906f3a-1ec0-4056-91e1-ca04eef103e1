package com.xhgj.srm.jpa.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.jpa.entity.SupplierOrder;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderToForm;
import com.xhgj.srm.jpa.entity.v2.SupplierOrderV2;
import com.xhgj.srm.jpa.provider.SupplierOrderStateProvider;
import com.xhgj.srm.jpa.repository.SupplierOrderDetailRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderRepository;
import com.xhgj.srm.jpa.repository.SupplierOrderToFormRepository;
import com.xhiot.boot.core.common.exception.CheckException;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Created by Geng Shy on 2023/12/4
 */
@Component
@Slf4j
public class SupplierOrderUtil {
  private static SupplierOrderDetailRepository supplierOrderDetailRepository;
  private static SupplierOrderToFormRepository supplierOrderToFormRepository;
  private static SupplierOrderRepository supplierOrderRepository;
  private static SupplierOrderStateProvider supplierOrderStateProvider;

  @Resource
  public void setSupplierOrderDetailRepository(
      SupplierOrderDetailRepository supplierOrderDetailRepository) {
    this.supplierOrderDetailRepository = supplierOrderDetailRepository;
  }

  @Resource
  public void setSupplierOrderToFormRepository(
      SupplierOrderToFormRepository supplierOrderToFormRepository) {
    this.supplierOrderToFormRepository = supplierOrderToFormRepository;
  }

  @Resource
  public void setSupplierOrderRepository(
      SupplierOrderRepository supplierOrderRepository) {
    this.supplierOrderRepository = supplierOrderRepository;
  }

  public static void setOrderFinalPrice(SupplierOrderV2 order) {
    supplierOrderStateProvider.updateFinalPrice(order);
  }

  public static void setOrderFinalPrice(SupplierOrder order) {
    Assert.notNull(order);
    List<SupplierOrderToForm> supplierOrderToForms =
        supplierOrderToFormRepository.findBySupplierOrderIdAndTypeAndState(order.getId(),
            SupplierOrderFormType.DETAILED.getType(), Constants.STATE_OK);
    if (CollUtil.isEmpty(supplierOrderToForms)) {
      throw new CheckException("订单异常，没有相关表单，请联系管理员！");
    }
    List<SupplierOrderDetail> supplierOrderDetails = new LinkedList<>();
    for (SupplierOrderToForm supplierOrderToForm : supplierOrderToForms) {
      List<SupplierOrderDetail> currSupplierOrderDetails =
          supplierOrderDetailRepository.findByOrderToFormIdAndState(supplierOrderToForm.getId(),
              Constants.STATE_OK);
      if (CollUtil.isEmpty(currSupplierOrderDetails)) {
        throw new CheckException("订单数据异常，请联系管理员！");
      }
      supplierOrderDetails.addAll(currSupplierOrderDetails);
    }
    BigDecimal finalPrice = BigDecimal.ZERO;
    for (SupplierOrderDetail supplierOrderDetail : supplierOrderDetails) {
      BigDecimal stockInputQty = supplierOrderDetail.getStockInputQty();
      BigDecimal stockOutputQty = supplierOrderDetail.getStockOutputQty();
//      BigDecimal settleQty = supplierOrderDetail.getSettleQty();
      BigDecimal price = supplierOrderDetail.getPrice();
      finalPrice = finalPrice.add(NumberUtil.mul(NumberUtil.sub(stockInputQty, stockOutputQty),
          price));
    }
    order.setFinalPrice(finalPrice);
    supplierOrderRepository.save(order);
  }

  @Resource
  public void setSupplierOrderStateProvider(
      SupplierOrderStateProvider supplierOrderStateProvider) {
    this.supplierOrderStateProvider = supplierOrderStateProvider;
  }






}
