package com.xhgj.srm.jpa.entity.v2;/**
 * @since 2025/4/28 9:11
 */

import com.xhgj.srm.jpa.annotations.ShardingTable;
import com.xhgj.srm.jpa.entity.BaseSupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderDetail;
import com.xhgj.srm.jpa.entity.SupplierOrderProduct;
import com.xhgj.srm.jpa.util.LazyLoadEntityListener;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicUpdate;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 *<AUTHOR>
 *@date 2025/4/28 09:11:49
 *@description
 */
@Entity
@Table(name = "t_supplier_order_detail")
@EntityListeners(LazyLoadEntityListener.class)
@Data
@DynamicUpdate
@ShardingTable(
    logicTable = "t_supplier_order_detail",
    actualDataNodes = "ds0.t_supplier_order_detail,ds0.t_supplier_order_detail_v2"
)
@EqualsAndHashCode(callSuper = true)
public class SupplierOrderDetailV2 extends BaseSupplierOrderDetail {

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "order_product_id", insertable = false, updatable = false)
  private SupplierOrderProductV2 supplierOrderProduct;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "detailed_id", insertable = false, updatable = false)
  private SupplierOrderDetailV2 detailed;

  /**
   * 关联form的type类型
   * @see com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType
   */
  @Column(name = "c_order_to_form_type")
  private String orderToFormType;

  /**
   * 待申请入库数量
   * 2.0 由 待发货数量 改为 待申请入库数量(订货数量 - 取消订货数量 - 已申请入库数量)
   */
  @Column(name = "c_wait_qty")
  private BigDecimal waitQty = BigDecimal.ZERO;

  /**
   * 已申请入库数量
   * 2.0 由 已发货数量 改为 已申请入库数量(入库申请数量之和 - 入库申请单取消数量之和)
   */
  @Column(name = "c_ship_qty")
  private BigDecimal shipQty = BigDecimal.ZERO;


  /**
   * 采购入库数量 / 已入库数量
   * @ 2.0入库申请单需要用
   */
  @Column(name = "c_stock_input_qty")
  private BigDecimal stockInputQty = BigDecimal.ZERO;

  /**
   * 取消数量 / 取消申请入库数量
   * @ 2.0入库申请单需要用
   */
  @Column(name = "c_cancel_qty")
  private BigDecimal cancelQty = BigDecimal.ZERO;

  /**
   * 剩余入库数量
   * @deprecated 计划 2.0去除该字段，但前端建议保留(实际上是订货数量 - 取消数量 - 实际入库数量)
   */
  @Deprecated
  @Column(name = "c_remain_qty")
  private BigDecimal remainQty = BigDecimal.ZERO;

  /**
   * 质检数量
   * @ 2.0入库申请单需要用
   */
  @Column(name = "c_inspect_qty")
  private BigDecimal inspectQty;

  /**
   * 入库单申请单id
   */
  @Column(name = "c_in_warehouse_apply_id")
  private String inWareHouseApplyId;

  /**
   * 入库单申请单名称
   */
  @Column(name = "c_in_warehouse_apply_name")
  private String inWareHouseApplyName;

  /**
   * 序号
   */
  @Column(name = "c_index")
  private String index;
}
