package com.xhgj.srm.jpa.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * 客户回款表
 */
@Entity
@Table(name = "t_customer_return")
@Data
public class CustomerReturn implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 主键
   */
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 收款 id
   */
  @Column(name = "customer_receivable_id")
  private String customerReceivableId;

  /**
   * 回款金额
   */
  @Column(name = "c_price")
  private BigDecimal price;

  /**
   * 回款日期
   */
  @Column(name = "c_return_time")
  private Long returnTime;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 数据状态
   */
  @Column(name = "c_state")
  private String state;

}
