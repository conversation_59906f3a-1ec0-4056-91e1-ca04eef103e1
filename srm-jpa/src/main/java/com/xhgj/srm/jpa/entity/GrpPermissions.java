package com.xhgj.srm.jpa.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * Created by Geng Shy on 2023/10/23
 */
@Entity
@Table(name = "t_grp_permission")
@Data
public class GrpPermissions {
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 父id
   */
  @Column(name = "parent_id")
  private String parentId;

  /**
   * 名称
   */
  @Column(name = "c_name")
  private String name;

  /**
   * url路径
   */
  @Column(name = "c_url")
  private String url;

  /**
   * 组件
   */
  @Column(name = "c_component")
  private String component;

  /**
   * 组件名称
   */
  @Column(name = "c_component_name")
  private String componentName;

  @Column(name = "c_redirect")
  private String redirect;

  /**
   * 菜单类型
   */
  @Column(name = "c_menu_type")
  private String menuType;

  @Column(name = "c_perms")
  private String perms;

  @Column(name = "c_perms_type")
  private String permsType;

  /**
   * 排序
   */
  @Column(name = "c_sort")
  private Integer sort;

  @Column(name = "c_always_show")
  private String alwaysShow;

  @Column(name = "c_icon")
  private String icon;

  @Column(name = "c_is_route")
  private String route;

  @Column(name = "c_is_leaf")
  private String leaf;

  @Column(name = "c_keep_alive")
  private String keepAlive;

  /**
   * 是否隐藏
   */
  @Column(name = "c_is_hidden")
  private String hidden;

  /**
   * 描述
   */
  @Column(name = "c_description")
  private String description;

  /**
   * 创建人
   */
  @Column(name = "c_create_man")
  private String createMan;

  /**
   * 创建时间
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 更新人
   */
  @Column(name = "c_update_man")
  private String updateMan;

  /**
   * 更新时间
   */
  @Column(name = "c_update_time")
  private Long updateTime;

  /**
   * 数据状态
   */
  @Column(name = "c_state")
  private String state;

  /**
   * 规则标志
   */
  @Column(name = "c_rule_flag")
  private String ruleFlag;

  @Column(name = "c_status")
  private String status;

  @Column(name = "c_internal_or_external")
  private String internalOrExternal;



}
