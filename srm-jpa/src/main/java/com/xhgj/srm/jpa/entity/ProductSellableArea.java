package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
  *@ClassName ProductSellableArea
  *<AUTHOR>
  *@Date 2023/10/18 15:20
*/
@Entity
@Data
@Table(name="t_product_sellable_area")
public class ProductSellableArea implements Serializable {
  private static final long serialVersionUID = 1L;

  /**
   * 主键
   */
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  @Column(name = "supplier_id")
  private String supplierId;
  /**
   * 物料编码
   */
  @Column(name="c_product_code")
  private String productCode;

  /**
   * 可售区域
   */
  @Column(name="c_sell_area")
  private String sellArea;

  /**
   * 库存
   */
  @Column(name="c_stock")
  private Long stock;

  /**
   * 创建时间
   */
  @Column(name="c_create_time")
  private Long createTime;

  /**
   * 更新时间
   */
  @Column(name="c_update_time")
  private Long updateTime;

  /**
   * 创建人
   */
  @Column(name="c_create_man")
  private String createMan;

  /**
   * 更新人
   */
  @Column(name="c_update_man")
  private String updateMan;
}
