package com.xhgj.srm.jpa.dao.impl;/**
 * @since 2025/2/12 9:16
 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_FileRelationType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderFormType;
import com.xhgj.srm.common.enums.supplierorder.SupplierOrderState;
import com.xhgj.srm.common.utils.SQLUtils;
import com.xhgj.srm.common.vo.returnExchange.ReturnExchangeListVO;
import com.xhgj.srm.jpa.dao.ReturnExchangeOrderDao;
import com.xhgj.srm.jpa.dto.permission.MergeUserPermission;
import com.xhgj.srm.jpa.dto.returnExchange.ReturnExchangeStatistics;
import com.xhgj.srm.jpa.entity.ReturnExchangeOrder;
import com.xhiot.boot.mvc.base.PageResult;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Repository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 *@date 2025/2/12 09:16:31
 *@description
 */
@Repository
public class ReturnExchangeOrderDaoImpl extends AbstractExtDao<ReturnExchangeOrder> implements ReturnExchangeOrderDao {

  @Override
  public PageResult<ReturnExchangeListVO> getPage(Map<String, Object> query) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    sql.append("select ro.id roId, ");
    sql.append("so.id soId, ");
    sql.append("so.c_code soCode, ");
    sql.append("so.c_order_state, ");
    sql.append("ro.c_project_type, ");
    sql.append("ro.c_return_reason, ");
    sql.append("ro.c_create_time, ");
    sql.append("so.supplier_id, ");
    sql.append("so.c_supplier_name, ");
    sql.append("so.c_purchase_man, ");
    sql.append("so.c_purchase_dept, ");
    sql.append("so.c_purchase_dept_code, ");
    sql.append("sop.c_code sopCode, ");
    sql.append("sop.c_brand, ");
    sql.append("sop.c_name, ");
    sql.append("sod.c_description, ");
    sql.append("sop.c_manu_code, ");
    sql.append("sop.c_specification, ");
    sql.append("sop.c_model, ");
    sql.append("sop.c_unit, ");
    sql.append("sod.c_num, ");
    sql.append("sod.c_price, ");
    sql.append("sod.c_tax_rate, ");
    sql.append("sod.c_total_price, ");
    sql.append("sod.c_stock_output_qty, ");
    sql.append("sod.c_cancel_qty, ");
    sql.append("IFNULL(sod.c_num, 0) - IFNULL(sod.c_stock_output_qty, 0) - IFNULL(sod.c_cancel_qty, 0) as remainReturnNum, ");
    sql.append("sod.c_warehouse, ");
    sql.append("sod.c_warehouse_name, ");
    sql.append("sod.c_mark, ");
    sql.append("ro.c_original_order_no, ");
    sql.append("sod.c_batch_no, ");
    sql.append("ro.c_need_red_invoice, ");
    sql.append("so.c_supplier_open_invoice_state, ");
    sql.append("so.c_loss, ");
    sql.append("so.c_order_cancel_state ");


    sql.append("from t_return_exchange_order ro ");
    sql.append("join t_supplier_order so on ro.c_supplier_order_id = so.id AND so.c_state = ?"
        + " ");
    params.add(Constants.STATE_LOCKED);

    sql.append("left join t_supplier_order_to_form sof on so.id = sof.supplier_order_id AND sof"
        + ".c_type = ? AND sof.c_state = ? ");
    params.add(SupplierOrderFormType.DETAILED.getKey());
    params.add(Constants.STATE_OK);

    sql.append("left join t_supplier_order_detail sod on sof.id = sod.order_to_form_id AND sod"
        + ".c_state = ? ");
    params.add(Constants.STATE_OK);

    sql.append("left join t_supplier_order_product sop on sod.order_product_id = sop.id ");
    this.buildWhereQuery(sql, params, query);

    sql.append("order by ro.c_create_time desc, ro.id desc ");
    Page<Object[]> pageSqlObject =
        findPageSqlObject(sql.toString(), params.toArray(), (Integer) query.get("pageNo"),
            (Integer) query.get("pageSize"));
    List<Object[]> content = pageSqlObject.getContent();
    // Object[] 转换为ReturnExchangeListVO
    List<ReturnExchangeListVO> res = content.parallelStream().map(item -> {
      int index = 0;
      ReturnExchangeListVO vo = new ReturnExchangeListVO();
      vo.setId(Convert.toStr(item[index++]));
      vo.setSupplierOrderId(Convert.toStr(item[index++]));
      vo.setCode(Convert.toStr(item[index++]));
      vo.setOrderStateKey(Convert.toStr(item[index++]));
      vo.setProjectType(Convert.toStr(item[index++]));
      vo.setReturnReason(Convert.toStr(item[index++]));
      vo.setCreateTime(Convert.toLong(item[index++]));
      vo.setSupplierId(Convert.toStr(item[index++]));
      vo.setSupplierName(Convert.toStr(item[index++]));
      vo.setPurchaseMan(Convert.toStr(item[index++]));
      vo.setPurchaseDept(Convert.toStr(item[index++]));
      vo.setPurchaseDeptCode(Convert.toStr(item[index++]));
      vo.setProductCode(Convert.toStr(item[index++]));
      vo.setBrand(Convert.toStr(item[index++]));
      vo.setProductName(Convert.toStr(item[index++]));
      vo.setDescription(Convert.toStr(item[index++]));
      vo.setManuCode(Convert.toStr(item[index++]));
      vo.setSpecification(Convert.toStr(item[index++]));
      vo.setModel(Convert.toStr(item[index++]));
      vo.setUnit(Convert.toStr(item[index++]));
      vo.setNum(Convert.toBigDecimal(item[index++]));
      vo.setPrice(Convert.toBigDecimal(item[index++]));
      vo.setTaxRate(Convert.toBigDecimal(item[index++]));
      vo.setTotalPrice(Convert.toBigDecimal(item[index++]));
      vo.setReturnedNum(Convert.toBigDecimal(item[index++]));
      vo.setCancelReturnNum(Convert.toBigDecimal(item[index++]));
      vo.setRemainReturnNum(Convert.toBigDecimal(item[index++]));
      vo.setWarehouse(Convert.toStr(item[index++]));
      vo.setWarehouseName(Convert.toStr(item[index++]));
      vo.setMark(Convert.toStr(item[index++]));
      vo.setOriginalOrderNo(Convert.toStr(item[index++]));
      vo.setBatchNo(Convert.toStr(item[index++]));
      vo.setNeedRedInvoice(Convert.toBool(item[index++]));
      vo.setSupplierOpenInvoiceState(Convert.toStr(item[index++]));
      vo.setLoss(Convert.toBool(item[index++]));
      vo.setCancelState(Convert.toBool(item[index++]));
      return vo;
    }).collect(Collectors.toList());
    return new PageResult<>(res, pageSqlObject.getTotalElements(), pageSqlObject.getTotalPages(),
        (Integer) query.get("pageNo"), (Integer) query.get("pageSize"));
  }

  @Override
  public Long getCount(Map<String, Object> query) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    sql.append("select count(1) ");

    sql.append("from t_return_exchange_order ro ");
    sql.append("left join t_supplier_order so on ro.c_supplier_order_id = so.id AND so.c_state = ?"
        + " ");
    params.add(Constants.STATE_LOCKED);

    sql.append("left join t_supplier_order_to_form sof on so.id = sof.supplier_order_id AND sof"
        + ".c_type = ? AND sof.c_state = ? ");
    params.add(SupplierOrderFormType.DETAILED.getKey());
    params.add(Constants.STATE_OK);

    sql.append("left join t_supplier_order_detail sod on sof.id = sod.order_to_form_id AND sod"
        + ".c_state = ? ");
    params.add(Constants.STATE_OK);

    sql.append("left join t_supplier_order_product sop on sod.order_product_id = sop.id ");
    this.buildWhereQuery(sql, params, query);
    return countSql(sql.toString(), params.toArray());
  }

  @Override
  public ReturnExchangeStatistics getStatistics(Map<String, Object> query) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    sql.append("select COALESCE(sum(sod.c_stock_output_qty), 0) as returnedNum, ");
    sql.append("COALESCE(sum(sod.c_total_price), 0) as totalPrice ");

    sql.append("from t_return_exchange_order ro ");
    sql.append("join t_supplier_order so on ro.c_supplier_order_id = so.id AND so.c_state = ?"
        + " ");
    params.add(Constants.STATE_LOCKED);

    sql.append("left join t_supplier_order_to_form sof on so.id = sof.supplier_order_id AND sof"
        + ".c_type = ? AND sof.c_state = ? ");
    params.add(SupplierOrderFormType.DETAILED.getKey());
    params.add(Constants.STATE_OK);

    sql.append("left join t_supplier_order_detail sod on sof.id = sod.order_to_form_id AND sod"
        + ".c_state = ? ");
    params.add(Constants.STATE_OK);

    sql.append("left join t_supplier_order_product sop on sod.order_product_id = sop.id ");
    this.buildWhereQuery(sql, params, query);
    Object[] obj = (Object[])getFirstSqlObj(sql.toString(), params.toArray());
    ReturnExchangeStatistics res = new ReturnExchangeStatistics();
    res.setReturnedNum(Convert.toBigDecimal(obj[0]));
    res.setTotalPrice(Convert.toBigDecimal(obj[1]));
    return res;

  }

  private void buildWhereQuery(StringBuilder sql, List<Object> params, Map<String, Object> query) {
    sql.append("where ro.c_state = ? ");
    params.add(Constants.STATE_OK);
    // 查询权限筛选
    MergeUserPermission mergeUserPermission = Convert.convert(MergeUserPermission.class, query.get(
        "searchPermission"));
    if (mergeUserPermission != null) {
      StringBuilder permissionSql = new StringBuilder();
      // 为采购员时
      if (StrUtil.isNotBlank(mergeUserPermission.getCurrentUserId())) {
        // 为采购员 -> 当前用户采购员
        permissionSql.append(" and so.c_purchase_man  = ? ");
        params.add(mergeUserPermission.getCurrentUserNameWithCode());
        permissionSql.append(" and so.c_group_code = ? ");
        params.add(query.get("userGroup"));
      } else if (CollUtil.isNotEmpty(mergeUserPermission.getCurrentDepartmentCodes())) {
        // 导出所在部门
        permissionSql.append(" and so.c_purchase_dept_code in ( ");
        for (String departmentCode : mergeUserPermission.getCurrentDepartmentCodes()) {
          permissionSql.append("?,");
          params.add(departmentCode);
        }
        permissionSql.deleteCharAt(permissionSql.length() - 1);
        permissionSql.append(" ) ");
        permissionSql.append(" and so.c_group_code = ? ");
        params.add(query.get("userGroup"));
      } else if (StrUtil.isNotBlank(mergeUserPermission.getCurrentGroupErpCode())) {
        // 所在组织 -> 查询所在组织
        permissionSql.append(" and so.c_group_code = ?  ");
        params.add(mergeUserPermission.getCurrentGroupErpCode());
      } else {
        permissionSql.append(" and so.c_group_code = ? ");
        params.add(query.get("userGroup"));
      }
      if (permissionSql.length() > 0) {
        sql.append("AND ( 1 = 1 ");
        sql.append(permissionSql);
        sql.append(" ) ");
      }
    }
    // 导出 - 勾选了ids
    List<String> ids = Convert.toList(String.class, query.get("ids"));
    if (CollUtil.isNotEmpty(ids)) {
      sql.append("AND ro.id in ( ");
      for (int i = 0; i < ids.size(); i++) {
        sql.append("?,");
        params.add(ids.get(i));
      }
      sql.deleteCharAt(sql.length() - 1);
      sql.append(" ) ");
      return;
    }
    // 退换货单号
    String orderCode = Convert.toStr(query.get("orderCode"));
    if (StrUtil.isNotBlank(orderCode)) {
      sql.append("AND so.c_code like ? ");
      params.add("%" + orderCode + "%");
    }
    // 订单状态
    String orderStateStr = Convert.toStr(query.get("orderState"));
    if (StrUtil.isNotBlank(orderStateStr)) {
      SupplierOrderState orderState = EnumUtil.fromString(SupplierOrderState.class, orderStateStr);
      if (orderState != null) {
        if (orderState.getOrderState().equals(SupplierOrderState.NOT_REVIEWED.getOrderState())) {
          sql.append("AND so.c_order_state in (?,?,?,?) ");
          params.addAll(ListUtil.of(
              SupplierOrderState.PENDING.getOrderState(),
              SupplierOrderState.STAGING.getOrderState(),
              SupplierOrderState.UNAUDITED.getOrderState(),
              SupplierOrderState.REJECT.getOrderState()
          ));
        }else if (orderState.getOrderState().equals(SupplierOrderState.REVIEWED.getOrderState())){
          sql.append("AND so.c_order_state in (?,?) ");
          params.addAll(ListUtil.of(SupplierOrderState.WAIT.getOrderState(),SupplierOrderState.IN_PROGRESS.getOrderState()));
        } else {
          sql.append("AND so.c_order_state = ? ");
          params.add(orderState.getOrderState());
        }
      }
    }
    // tabState
    String tabStateStr = Convert.toStr(query.get("tabState"));
    if (StrUtil.isNotBlank(tabStateStr)) {
      SupplierOrderState tabState = EnumUtil.fromString(SupplierOrderState.class, tabStateStr);
      if (tabState != null) {
        if (tabState.getOrderState().equals(SupplierOrderState.NOT_REVIEWED.getOrderState())) {
          sql.append("AND so.c_order_state in (?,?,?,?) ");
          params.addAll(ListUtil.of(
              SupplierOrderState.PENDING.getOrderState(),
              SupplierOrderState.STAGING.getOrderState(),
              SupplierOrderState.UNAUDITED.getOrderState(),
              SupplierOrderState.REJECT.getOrderState()
          ));
        }else if (tabState.getOrderState().equals(SupplierOrderState.REVIEWED.getOrderState())){
          sql.append("AND so.c_order_state in (?,?) ");
          params.addAll(ListUtil.of(SupplierOrderState.WAIT.getOrderState(),SupplierOrderState.IN_PROGRESS.getOrderState()));
        } else {
          sql.append("AND so.c_order_state = ? ");
          params.add(tabState.getOrderState());
        }
      }
    }
    // projectType
    String projectType = Convert.toStr(query.get("projectType"));
    if (StrUtil.isNotBlank(projectType)) {
      sql.append("AND ro.c_project_type = ? ");
      params.add(projectType);
    }
    // startCreateTime
    Long startCreateTime = Convert.toLong(query.get("startCreateTime"));
    if (startCreateTime != null) {
      sql.append("AND ro.c_create_time >= ? ");
      params.add(startCreateTime);
    }
    // endCreateTime
    Long endCreateTime = Convert.toLong(query.get("endCreateTime"));
    if (endCreateTime != null) {
      sql.append("AND ro.c_create_time <= ? ");
      params.add(endCreateTime);
    }
    // supplierName
    String supplierName = Convert.toStr(query.get("supplierName"));
    if (StrUtil.isNotBlank(supplierName)) {
      sql.append("AND so.c_supplier_name like ? ");
      params.add("%" + supplierName + "%");
    }
    // purchaseMan
    String purchaseMan = Convert.toStr(query.get("purchaseMan"));
    if (StrUtil.isNotBlank(purchaseMan)) {
      SQLUtils.addLikeListConditions(sql, params, "so.c_purchase_man", purchaseMan);
    }
    // purchaseDept
    String purchaseDept = Convert.toStr(query.get("purchaseDept"));
    if (StrUtil.isNotBlank(purchaseDept)) {
      sql.append("AND so.c_purchase_dept like ? ");
      params.add("%" + purchaseDept + "%");
    }
    // productCode
    String productCode = Convert.toStr(query.get("productCode"));
    if (StrUtil.isNotBlank(productCode)) {
      sql.append("AND sop.c_code like ? ");
      params.add("%" + productCode + "%");
    }
    // brand
    String brand = Convert.toStr(query.get("brand"));
    if (StrUtil.isNotBlank(brand)) {
      sql.append("AND sop.c_brand like ? ");
      params.add("%" + brand + "%");
    }
    // productName
    String productName = Convert.toStr(query.get("productName"));
    if (StrUtil.isNotBlank(productName)) {
      sql.append("AND sop.c_name like ? ");
      params.add("%" + productName + "%");
    }
    // description
    String description = Convert.toStr(query.get("description"));
    if (StrUtil.isNotBlank(description)) {
      sql.append("AND sod.c_description like ? ");
      params.add("%" + description + "%");
    }
    // manuCode
    String manuCode = Convert.toStr(query.get("manuCode"));
    if (StrUtil.isNotBlank(manuCode)) {
      sql.append("AND sop.c_manu_code like ? ");
      params.add("%" + manuCode + "%");
    }
    // specification
    String specification = Convert.toStr(query.get("specification"));
    if (StrUtil.isNotBlank(specification)) {
      sql.append("AND sop.c_specification like ? ");
      params.add("%" + specification + "%");
    }
    // model
    String model = Convert.toStr(query.get("model"));
    if (StrUtil.isNotBlank(model)) {
      sql.append("AND sop.c_model like ? ");
      params.add("%" + model + "%");
    }
    // unit
    String unit = Convert.toStr(query.get("unit"));
    if (StrUtil.isNotBlank(unit)) {
      sql.append("AND sop.c_unit like ? ");
      params.add("%" + unit + "%");
    }
    // num + numOperator
    BigDecimal num = Convert.toBigDecimal(query.get("num"));
    String numOperator = Convert.toStr(query.get("numOperator"));
    if (num != null && StrUtil.isNotBlank(numOperator)) {
      SQLUtils.addStrLogicalOperators(numOperator, num, sql, params, "sod.c_num");
    }
    // unitPrice + unitPriceOperator
    BigDecimal unitPrice = Convert.toBigDecimal(query.get("unitPrice"));
    String unitPriceOperator = Convert.toStr(query.get("unitPriceOperator"));
    if (unitPrice != null && StrUtil.isNotBlank(unitPriceOperator)) {
      SQLUtils.addStrLogicalOperators(unitPriceOperator, unitPrice, sql, params, "sod.c_price");
    }
    // taxRate
    BigDecimal taxRate = Convert.toBigDecimal(query.get("taxRate"));
    if (taxRate != null) {
      sql.append("AND sod.c_tax_rate = ? ");
      params.add(taxRate);
    }
    // totalPrice + totalPriceOperator
    BigDecimal totalPrice = Convert.toBigDecimal(query.get("totalPrice"));
    String totalPriceOperator = Convert.toStr(query.get("totalPriceOperator"));
    if (totalPrice != null && StrUtil.isNotBlank(totalPriceOperator)) {
      SQLUtils.addStrLogicalOperators(totalPriceOperator, totalPrice, sql, params, "sod.c_total_price");
    }
    // returnedNum + returnedNumOperator
    BigDecimal returnedNum = Convert.toBigDecimal(query.get("returnedNum"));
    String returnedNumOperator = Convert.toStr(query.get("returnedNumOperator"));
    if (returnedNum != null && StrUtil.isNotBlank(returnedNumOperator)) {
      SQLUtils.addStrLogicalOperators(returnedNumOperator, returnedNum, sql, params, "sod.c_stock_output_qty");
    }
    // cancelReturnNum + cancelReturnNumOperator
    BigDecimal cancelReturnNum = Convert.toBigDecimal(query.get("cancelReturnNum"));
    String cancelReturnNumOperator = Convert.toStr(query.get("cancelReturnNumOperator"));
    if (cancelReturnNum != null && StrUtil.isNotBlank(cancelReturnNumOperator)) {
      SQLUtils.addStrLogicalOperators(cancelReturnNumOperator, cancelReturnNum, sql, params, "sod.c_cancel_qty");
    }
    // remainReturnNum + remainReturnNumOperator
    BigDecimal remainReturnNum = Convert.toBigDecimal(query.get("remainReturnNum"));
    String remainReturnNumOperator = Convert.toStr(query.get("remainReturnNumOperator"));
    if (remainReturnNum != null && StrUtil.isNotBlank(remainReturnNumOperator)) {
      SQLUtils.addStrLogicalOperators(remainReturnNumOperator,
          remainReturnNum, sql, params,
          "(IFNULL(sod.c_num, 0) - IFNULL(sod.c_stock_output_qty, 0) - IFNULL(sod.c_cancel_qty, 0))");
    }
    // warehouse
    String warehouse = Convert.toStr(query.get("warehouse"));
    if (StrUtil.isNotBlank(warehouse)) {
      sql.append("AND sod.c_warehouse = ? ");
      params.add(warehouse);
    }
    // originalOrderNo
    String originalOrderNo = Convert.toStr(query.get("originalOrderNo"));
    if (StrUtil.isNotBlank(originalOrderNo)) {
      sql.append("AND ro.c_original_order_no like ? ");
      params.add("%" + originalOrderNo + "%");
    }
    // batchNo
    String batchNo = Convert.toStr(query.get("batchNo"));
    if (StrUtil.isNotBlank(batchNo)) {
      sql.append("AND sod.c_batch_no like ? ");
      params.add("%" + batchNo + "%");
    }
    // needRedInvoice
    Boolean needRedInvoice = Convert.toBool(query.get("needRedInvoice"));
    if (needRedInvoice != null) {
      if (BooleanUtil.isTrue(needRedInvoice)) {
        sql.append("AND ro.c_need_red_invoice = ? ");
        params.add(needRedInvoice);
      }
      if (BooleanUtil.isFalse(needRedInvoice)) {
        sql.append("AND ( ro.c_need_red_invoice = ? or ro.c_need_red_invoice is null ) ");
        params.add(needRedInvoice);
      }
    }
    // supplierOpenInvoiceState
    String supplierOpenInvoiceState = Convert.toStr(query.get("supplierOpenInvoiceState"));
    if (StrUtil.isNotBlank(supplierOpenInvoiceState)) {
      sql.append("AND so.c_supplier_open_invoice_state =? ");
      params.add(supplierOpenInvoiceState);
    }
    // loss
    Boolean loss = Convert.toBool(query.get("loss"));
    if (loss != null) {
      if (BooleanUtil.isTrue(loss)) {
        sql.append(" AND so.c_loss = ? ");
        params.add(loss);
      }
      if (BooleanUtil.isFalse(loss)) {
        sql.append(" AND ( so.c_loss = ? or so.c_loss is null ) ");
        params.add(loss);
      }
    }
    // 附件 uploadAttachment
    Boolean uploadAttachment = Convert.toBool(query.get("uploadAttachment"));
    if (uploadAttachment != null) {
      if (BooleanUtil.isTrue(uploadAttachment)) {
        sql.append("AND exists (select 1 from t_file file where file.c_relationId = so.id and file.c_relationType = ?) ");
      }
      if (BooleanUtil.isFalse(uploadAttachment)) {
        sql.append("AND not exists (select 1 from t_file file where file.c_relationId = so.id and file.c_relationType = ?) ");
      }
      params.add(Constants_FileRelationType.ORDER_ANNEX);
    }
  }
}
