package com.xhgj.srm.jpa.entity;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/** 订单退货表 */
@Entity
@Table(name = "t_order_return")
@Data
public class OrderReturn {


    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    /**
     * 关联订单
     */
    @JoinColumn(name = "order_id")
    @ManyToOne
    private Order order;

    /** 关联订单 */
    @Column(name = "order_id", insertable = false, updatable = false)
    private String orderId;

    /** 退货单号 */
    @Column(name = "c_return_no")
    private String returnNo;

    /** 退货数量 */
    @Column(name = "c_num")
    private BigDecimal num;

    /** 退货金额 */
    @Column(name = "c_price")
    private BigDecimal price;

    /** 申请时间 */
    @Column(name = "c_apply_time")
    private Long applyTime;

    /** 申请人 */
    @Column(name = "c_apply_man")
    private String applyMan;

    /** 联系方式 */
    @Column(name = "c_mobile")
    private String mobile;

    /** 退货/取消原因 */
    @Column(name = "c_reason")
    private String reason;

    /** 拒绝原因 */
    @Column(name = "c_reject_reason")
    private String rejectReason;

    /** 退货/取消状态 */
    @Column(name = "c_return_state")
    private String returnState;

    /** 退货单类型(取消/退货) */
    @Column(name = "c_type")
    private String type;

    /** 供应商id */
    @Column(name = "c_supplier_id")
    private String supplierId;

    /** 完成时间 */
    @Column(name = "c_complete_time")
    private Long completeTime;

    /** 履约退货单id */
    @Column(name = "c_oms_return_id")
    private String omsReturnId;

    /** 履约退货单id */
    @Column(name = "c_oms_open_id")
    private String omsOpenId;

    /** 状态 */
    @Column(name = "c_state")
    private String state;

    /** 创建时间 */
    @Column(name = "c_create_time")
    private Long createTime;

    /** ERP 采购退料单号 */
    @Column(name = "c_erp_no")
    private String erpNo;

    /**
     * 退货单采购订单号
     */
    @Column(name = "c_purchase_order_no")
    private String purchaseOrderNo;

}
