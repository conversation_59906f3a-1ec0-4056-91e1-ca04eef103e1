package com.xhgj.srm.jpa.repository;



import com.xhgj.srm.jpa.entity.SupplierPerformance;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import org.springframework.data.jpa.repository.Query;
import java.util.List;

/**
 * 落地商履约信息(SupplierPerformance)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-14 09:59:12
 */
public interface SupplierPerformanceRepository extends BootBaseRepository<SupplierPerformance,String> {

  /**
   * 通过平台编码和供应商 id 和数据状态获取履约信息
   * @param platformCode 平台编码
   * @param supplierId 供应商 id
   * @param state 数据状态
   * @return
   */
  SupplierPerformance getFirstByPlatformCodeAndSupplierIdAndStateOrderByUpdateTimeDesc(String platformCode,
      String supplierId,String state);

  /**
   * 通过合同id获取履约信息
   * @param landingContractId 合同id
   * @param state   数据状态
   * @return
   */
  SupplierPerformance getFirstByLandingContractIdAndState(String landingContractId, String state);


  /**
   * @param platformCode 平台编码
   * @param state 数据状态
   * @return 供应商履约信息集合
   */
  List<SupplierPerformance> findAllByPlatformCodeAndState(String platformCode, String state);

  /**
   * @param platformCode 平台编码
   * @param state 数据状态
   * @return 统计该下单平台下的数量
   */
  long countAllByPlatformCodeAndState(String platformCode, String state);

  /**
   * @param supplierId 供应商id
   * @param status 生效状态
   * @param state 数据状态
   * @return 履约信息集合
   */
  List<SupplierPerformance> findBySupplierIdAndStatusAndState(String supplierId, String status,
      String state);

  /**
   * @param supplierId 供应商id
   * @return 履约信息集合
   */
  List<SupplierPerformance> findBySupplierIdAndState(String supplierId, String status);
  /**
   * 通过下单平台、供应商id获取履约信息列表
   * @param platformCode 下单平台
   * @param supplierId 供应商id
   * @param state 数据状态
   */
  List<SupplierPerformance> findAllByPlatformCodeAndSupplierIdAndState(String platformCode, String supplierId,String state);

  /**
   * 批量获取履约信息
   * @param landingContractIds
   * @param stateOk
   * @return
   */
  List<SupplierPerformance> findByLandingContractIdInAndState(List<String> landingContractIds, String stateOk);

  /**
   * 通过合同id获取所有履约信息
   * @param landingContractId 合同id
   * @param state   数据状态
   * @return
   */
  List<SupplierPerformance> findAllByLandingContractIdAndState(String landingContractId,
      String state);
  /**
   * 根据平台编码、供应商id、数据状态获取履约信息
   * @param platformCode 平台编码
   * @param supplierId 供应商id
   * @param state 数据状态
   */
  SupplierPerformance findFirstByPlatformCodeAndSupplierIdAndState(String platformCode,
      String supplierId, String state);

  /**
   * 根据平台编码、供应商id、数据状态获取履约信息
   * @param platformCode 平台编码
   * @param supplierId 供应商id
   * @param state 数据状态
   */
  SupplierPerformance findFirstByPlatformCodeAndSupplierIdAndStateOrderByCreateTimeDesc(String platformCode,
      String supplierId, String state);

  /**
   * 根据平台编码、供应商ids、数据状态获取履约信息
   * @param platformCode
   * @param supplierIds
   * @param state
   * @return
   */
  List<SupplierPerformance> findAllByPlatformCodeAndSupplierIdInAndState(String platformCode, List<String> supplierIds, String state);

  /**
   * 根据平台编码、供应商id、数据状态获取履约信息
   * @param supplierId
   * @param platformCodes
   * @param state
   * @return
   */
  List<SupplierPerformance> findAllBySupplierIdAndPlatformCodeInAndState(String supplierId, List<String> platformCodes, String state);

  /**
   * 查询旧数据
   * @return
   */
  @Query(
      value =  "from SupplierPerformance sp where sp.state = '1' and ( sp.dockingPurchaseErpCode "
          + "is null or sp.dockingPurchaseErpCode = '') "
  )
  List<SupplierPerformance> findOldData();
}


