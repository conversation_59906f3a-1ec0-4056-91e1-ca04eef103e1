package com.xhgj.srm.jpa.entity;

import com.xhgj.srm.common.Constants;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;

/**
 * t_supplier_performance表实体
 *
 * <AUTHOR>
 * @since 2023-04-14 09:59:12
 */
@Data
@Entity
@Table(name = "t_supplier_performance")
@SQLDelete(
        sql = "update t_supplier_performance set c_state = '" + Constants.COMMONSTATE_DELETE + "' where id = ?")
public class SupplierPerformance implements Serializable {

  private static final long serialVersionUID = 782469501232253055L;
  /**
   * 主键
   */
  @Id
  @Column(name = "id", nullable = false)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;
  /**
   * 供应商 id
   */
  @Column(name = "supplier_id")
  private String supplierId;
  /**
   * 平台类型
   */
  @Column(name = "c_platform_code")
  private String platformCode;
  /**
   * 平台类型
   */
  @Column(name = "c_platform_name")
  private String platformName;
  /**
   * 状态
   */
  @Column(name = "c_status")
  private String status;
  /**
   * 联系人
   */
  @Column(name = "c_contacts")
  private String contacts;
  /**
   * 电话
   */
  @Column(name = "c_mobile")
  private String mobile;
  /**
   * 合作区域/项目
   */
  @Column(name = "c_area")
  private String area;
  /**
   * 合作模式
   * 6.8.0版本去除
   */
  @Column(name = "c_cooperation_type")
  @Deprecated
  private String cooperationType;
  /**
   * 合同签署状态
   * 6.8.0版本去除
   */
  @Column(name = "c_signing_status")
  @Deprecated
  private String signingStatus;
  /**
   * 关联合同
   */
  @Column(name = "landing_contract_id")
  private String landingContractId;
  /**
   * 业务负责人
   */
  @Column(name = "c_business_leader")
  private String businessLeader;
  /**
   * 对接采购 ERP 编码
   */
  @Column(name = "c_docking_purchase_erp_code")
  private String dockingPurchaseErpCode;
  /**
   * 对接助理
   */
  @Column(name = "c_docking_assistant")
  private String dockingAssistant;

  /** 创建时间 */
  @Column(name = "c_create_time")
  private Long createTime;
  /** 更新时间 */
  @Column(name = "c_update_time")
  private Long updateTime;
  /** 更新状态时间 */
  @Column(name = "c_update_state_time")
  private Long updateStateTime;
  /** 创建人 */
  @Column(name = "c_create_man")
  private String createMan;
  /** 更新人 */
  @Column(name = "c_update_man")
  private String updateMan;
  /**
   * 数据状态
   */
  @Column(name = "c_state")
  private String state = Constants.STATE_OK;
}

