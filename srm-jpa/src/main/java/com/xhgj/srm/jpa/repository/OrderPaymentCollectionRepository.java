package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.OrderPaymentCollection;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;

public interface OrderPaymentCollectionRepository extends
    BootBaseRepository<OrderPaymentCollection, String> {

  List<OrderPaymentCollection> findAllByOrderPaymentIdAndState(String orderPaymentId, String state);

  List<OrderPaymentCollection> findAllByAmountIsNullAndState(String state);

}
