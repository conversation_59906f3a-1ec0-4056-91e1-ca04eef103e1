package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.Financial;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.List;
import java.util.Optional;
import org.springframework.transaction.annotation.Transactional;

public interface FinancialRepository extends BootBaseRepository<Financial, String> {

  /**
   * 删除指定组织内供应商关联的所有财务信息
   *
   * @param supplierInGroupId 组织内供应商 id
   */
  @Transactional(rollbackFor = Exception.class)
  void deleteAllBySupplierInGroupId(String supplierInGroupId);

  /**
   * 根据供应商组织id查询财务信息实体
   * @param supplierInGroupId 供应商组织id
   * @param state 数据状态
   */
  Optional<Financial> findFirstBySupplierInGroupIdAndState(String supplierInGroupId,
      String state);

  List<Financial> findAllBySupplierInGroupIdAndState(String supplierInGroupId, String state);

}
