package com.xhgj.srm.jpa.sharding.hook;/**
 * @since 2025/4/17 13:22
 */

import com.xhgj.srm.jpa.sharding.Interceptor.HintInterceptor;

import org.apache.shardingsphere.infra.database.metadata.DataSourceMetaData;
import org.apache.shardingsphere.infra.executor.sql.hook.SQLExecutionHook;
import java.util.List;

/**
 * <AUTHOR>
 * SQL执行钩子，用于在SQL执行前后进行操作
 */
public class CustomSQLExecutionHook implements SQLExecutionHook {

  @Override
  public void start(String s, String s1, List<Object> list, DataSourceMetaData dataSourceMetaData,
      boolean b) {
  }

  @Override
  public void finishSuccess() {
    HintInterceptor.hintClose();
  }

  @Override
  public void finishFailure(Exception cause) {
    HintInterceptor.hintClose();
  }
}
