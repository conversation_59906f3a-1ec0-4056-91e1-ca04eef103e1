package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.jpa.dao.SearchSchemeDao;
import com.xhgj.srm.jpa.entity.Meeting;
import com.xhgj.srm.jpa.entity.SearchScheme;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.dao.AbstractBaseDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class SearchSchemeDaoImpl extends AbstractExtDao<SearchScheme> implements SearchSchemeDao {

    public List<SearchScheme> getSearchSchemeListByUser(String userId, String type) {
        String hql = " from SearchScheme s where s.state = ?  ";
        Object[] params = new Object[] { Constants.STATE_OK};
        if(!StringUtils.isNullOrEmpty(userId)){
            hql += "  and s.createMan = ? ";
            params = ObjectUtils.objectAdd(params,userId);
        }
        if(!StringUtils.isNullOrEmpty(type)){
            hql += "  and s.type = ? ";
            params = ObjectUtils.objectAdd(params,type);
        }
        hql += "order by s.createTime desc";
        return  getHqlList(hql,params);
    }

    @Override
    public List<SearchScheme> getSearchSchemeListByName(String userId, String type, String name,String id) {
        String hql = " from SearchScheme s where s.state = ?  ";
        Object[] params = new Object[] { Constants.STATE_OK};
        if(!StringUtils.isNullOrEmpty(userId)){
            hql += "  and s.createMan = ? ";
            params = ObjectUtils.objectAdd(params,userId);
        }
        if(!StringUtils.isNullOrEmpty(id)){
            hql += "  and s.id <> ? ";
            params = ObjectUtils.objectAdd(params,id);
        }
        if(!StringUtils.isNullOrEmpty(type)){
            hql += "  and s.type = ? ";
            params = ObjectUtils.objectAdd(params,type);
        }
        if(!StringUtils.isNullOrEmpty(name)){
            hql += "  and s.name = ? ";
            params = ObjectUtils.objectAdd(params,name);
        }
        hql += "order by s.createTime desc";
        return  getHqlList(hql,params);
    }


    @Override
    public SearchScheme getDefaultSearchScheme(String userId, String type) {
        String hql = " from SearchScheme s where s.state = ? and s.isDefault = ? ";
        Object[] params = new Object[] { Constants.STATE_OK,Constants.YES};
        if(!StringUtils.isNullOrEmpty(userId)){
            hql += "  and s.createMan = ? ";
            params = ObjectUtils.objectAdd(params,userId);
        }
        if(!StringUtils.isNullOrEmpty(type)){
            hql += "  and s.type = ? ";
            params = ObjectUtils.objectAdd(params,type);
        }
        hql += "order by s.createTime desc";
        return getFirstHqlEntity(hql, params);
    }

}
