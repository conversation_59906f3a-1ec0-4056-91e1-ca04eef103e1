package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.dto.OrderDetailDTO;
import com.xhgj.srm.common.utils.DateUtil;
import com.xhgj.srm.common.utils.SQLUtils;
import com.xhgj.srm.common.utils.supplierorder.BigDecimalUtil;
import com.xhgj.srm.jpa.dao.OrderDetailDao;
import com.xhgj.srm.jpa.entity.OrderDetail;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OrderDetailDaoImpl extends AbstractExtDao<OrderDetail> implements OrderDetailDao {

  @Override
  public BigDecimal getOrderReturnSkuCountByOrderId(String orderId) {
    String hql =
        "select sum(od.c_return_num) from t_order_detail od where od.c_state = ? and od"
            + ".order_id = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, orderId};
    try {
      Object sqlObj = getFirstSqlObj(hql, params);
      return sqlObj instanceof BigDecimal
          ? BigDecimalUtil.formatForStandard((BigDecimal) sqlObj)
          : BigDecimal.ZERO;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e));
      return BigDecimal.ZERO;
    }
  }

  @Override
  public BigDecimal getOrderSkuNumCountByOrderId(String orderId) {
    String hql =
        "select sum(od.c_num) from t_order_detail od where od.c_state = ? and od.order_id"
            + " = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, orderId};
    try {
      Object sqlObj = getFirstSqlObj(hql, params);
      return sqlObj instanceof BigDecimal
          ? BigDecimalUtil.formatForStandard((BigDecimal) sqlObj)
          : BigDecimal.ZERO;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e));
      return BigDecimal.ZERO;
    }
  }

  @Override
  public BigDecimal getShipOrderSkuNumCountByOrderId(String orderId) {
    String hql =
        "select sum(od.c_ship_num) from t_order_detail od where od.c_state = ? and od.order_id = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, orderId};
    try {
      Object sqlObj = getFirstSqlObj(hql, params);
      return sqlObj instanceof BigDecimal
          ? BigDecimalUtil.formatForStandard((BigDecimal) sqlObj)
          : BigDecimal.ZERO;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e));
      return BigDecimal.ZERO;
    }
  }

  @Override
  public List<OrderDetail> getOrderDetailByOrderId(String orderId) {
    String hql =
        "from OrderDetail od where od.state != ? and od.order.id = ? order by od.deliveryState+0 asc,od.num desc";
    Object[] params = new Object[] {Constants.STATE_DELETE, orderId};
    return getHqlList(hql, params);
  }

  @Override
  public OrderDetail getOrderDetailByOrderIdAndCode(String orderId, String code) {
    Assert.notBlank(orderId);
    Assert.notBlank(code);
    String hql =
        "from OrderDetail od where od.state != ? and od.order.id = ? and od.code = ? order by od.createTime desc";
    Object[] params = new Object[] {Constants.STATE_DELETE, orderId, code};
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public OrderDetail getOrderDetailByOrderIdAndRowNo(String orderId, String rowNo) {
    Assert.notBlank(orderId);
    Assert.notBlank(rowNo);
    String hql =
        "from OrderDetail od where od.state != ? and od.order.id = ? and od.rowNo = ? order by od.createTime desc";
    Object[] params = new Object[] {Constants.STATE_DELETE, orderId, rowNo};
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public BigDecimal getSumUnShipCount(String orderId) {
    Assert.notBlank(orderId);
    String hql =
        "select sum(c_unship_num) from  t_order_detail od where od.c_state = ? and od.order_id = "
            + " ? ";
    Object[] params = new Object[] {Constants.STATE_OK, orderId};
    Object sumUnshipNum = getFirstSqlObj(hql, params);
    if (sumUnshipNum instanceof BigDecimal) {
      return ((BigDecimal) sumUnshipNum).stripTrailingZeros();
    }
    if (sumUnshipNum == null) {
      return BigDecimal.ZERO;
    }
    throw new IllegalArgumentException("获取订单未发货数量异常，非法的数据类型" + sumUnshipNum.getClass().getName());
  }

  @Override
  public List<OrderDetail> getAllOrderDetail() {
    String hql = "from OrderDetail od where od.state != ? ";
    Object[] params = new Object[] {Constants.STATE_DELETE};
    return getHqlList(hql, params);
  }

  @Override
  public OrderDetail getOrderDetailByOrderNoAndTypeAndCode(
      String supplierOrderId, String orderNo, String type, String code) {
    String hql =
        "from OrderDetail od where od.state != ? and od.order.orderNo = ? and od.order.type = ? "
            + " and od.code = ? ";
    Object[] params = new Object[] {Constants.STATE_DELETE, orderNo, type, code};
    if (StrUtil.isNotBlank(supplierOrderId)) {
      hql += "and od.order.supplierOrderId = ? ";
      params = ObjectUtils.objectAdd(params, supplierOrderId);
    }
    hql += " order by od.createTime desc ";

    return getFirstHqlEntity(hql, params);
  }

  @Override
  public List<OrderDetailDTO> getDetailListByOrderId(String orderId) {
    String hql =
        "SELECT o.c_order_no orderNo,( o.c_price - c_refund_price ) settlementPrice,o.c_customer customer,"
            + "o.c_consignee consignee,o.c_type type,od.c_name productName,"
            + "od.c_model model,od.c_brand brand,od.c_unit unit,od.c_num num,"
            + "od.c_return_num returnNum,( c_num - c_return_num ) settlementNum,"
            + "od.c_price price,od.c_tax_free_cb_price taxFreeCbPrice,od.c_cost_price_tax_rate costPriceTaxRate, "
            + "od.c_total_amount totalAmount,od.c_total_tax_amount totalTaxAmount, "
            + "od.c_total_amount_including_tax totalAmountIncludingTax FROM "
            + "t_order_detail od LEFT JOIN t_order o ON od.order_id = o.id WHERE od.c_state != ? and "
            + "o.id = ? order by od.c_create_time desc";
    Object[] params = new Object[] {Constants.STATE_DELETE, orderId};
    return getSqlTypedObjList(hql, OrderDetailDTO.class, params);
  }

  @Override
  public List<OrderDetail> findBeforeByOrderTime(Long orderTime) {
    Assert.notNull(orderTime);
    String sql =
        "select DISTINCT od.* from t_order_detail od left join t_order o on od.order_id "
            + " = o.id where "
            + " o.c_order_state != ? and o.c_state = ? and od.c_state = ? and o.c_order_time < ? ";
    Object[] params =
        new Object[] {
          Constants_order.ORDER_STATE_WITHDRAW, Constants.STATE_OK, Constants.STATE_OK, orderTime
        };
    return getSqlList(sql, params);
  }

  @Override
  public List<OrderDetail> getListBySalesPriceRateAndCostPrice() {
    String sql =
        "SELECT od.* FROM"
            + " t_order_detail od LEFT JOIN t_order o ON od.order_id = o.id WHERE "
            + "od.c_sales_price_tax_rate IS NULL AND od.c_cost_price_tax_rate IS NULL"
            + " AND o.c_supplier_order_id IS NOT NULL";
    Object[] params = new Object[] {};
    return getSqlList(sql, params);
  }

  @Override
  public Object[] getReturnCountAndShipCountAndSkuNumCount(String orderId) {
    String hql =
        "select sum(od.c_return_num), sum(od.c_ship_num), sum(od.c_num), sum(od.c_cancel_num) "
            + "from t_order_detail od "
            + "where "
            + "od.c_state = ? and od"
            + ".order_id = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, orderId};
    Object[] result = new Object[] {BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO};
    try {
      Object[] sqlObj = (Object[]) getFirstSqlObj(hql, params);
      if (ArrayUtil.isEmpty(sqlObj)) {
        return result;
      }
      for (int i = 0; i < sqlObj.length; i++) {
        result[i] = Optional.ofNullable(Convert.toBigDecimal(sqlObj[i])).orElse(BigDecimal.ZERO)
            .stripTrailingZeros();
      }
      return result;
    } catch (Exception e) {
      log.error(ExceptionUtil.stacktraceToString(e));
      return result;
    }
  }

  @Override
  public Page<OrderDetail> getOrderDetailPageByOrderId(
      String orderId, String keyWord, Integer pageNo, Integer pageSize, List<String> ids) {
    String hql = "from OrderDetail od where od.state != ? and od.order.id = ? and od.unshipNum >? ";
    Object[] params = new Object[] {Constants.STATE_DELETE, orderId, BigDecimal.ZERO};
    if (StrUtil.isNotBlank(keyWord)) {
      hql += "and (code like ? or brand like ? or name like ? or model like ?) ";
      params = ObjectUtils.objectAdd(params, StrUtil.wrap(keyWord, "%"));
      params = ObjectUtils.objectAdd(params, StrUtil.wrap(keyWord, "%"));
      params = ObjectUtils.objectAdd(params, StrUtil.wrap(keyWord, "%"));
      params = ObjectUtils.objectAdd(params, StrUtil.wrap(keyWord, "%"));
    }
    if (CollUtil.isNotEmpty(ids)) {
      hql += " and od.id in( ";
      for (String type : ids) {
        hql += " ?,";
        params = ObjectUtils.objectAdd(params, type);
      }
      hql = hql.substring(0, hql.length() - 1);
      hql += " ) ";
    }
    hql += " order by od.deliveryState+0 asc,od.num desc ";
    return findPage(hql, params, pageNo, pageSize);
  }

  @Override
  public Long countByOrderIds(List<String> orderIds) {
    StringBuilder sql = new StringBuilder("select count(od.id) from t_order_detail od "
        + "where od.c_state = ? ");
    Object[] params =
        new Object[]{Constants.STATE_OK};
    params = HqlUtil.appendFieldIn(sql, params, "od.order_id", orderIds);
    return countSql(sql.toString(), params);
  }

  @Override
  public Long countOrdersByQueryParams(String userIds,
      String orderNo,
      String customer,
      String consignee,
      String mobile,
      String accepted,
      String orderState,
      String startDate,
      String endDate,
      String platform,
      String supplierName,
      String price,
      String invoicingState,
      Boolean hasUploadSignVoucher,
      String customerReturnProgress,
      Long allShipTimeStart,
      String paymentStatus,
      List<String> customerReturnProgressList,
      String accountStatus,
      String accountOpenInvoiceStatus,
      Boolean salesReturnState,
      String saleOrderNo,
      String erpOrderNo,
      String startCreateTime,
      String endCreateTime,
      String paymentProportionOperators, BigDecimal paymentProportion, Long startArrivalTime,
      Long endArrivalTime,Long startWriteOffTime,Long endWriteOffTime,String paymentType,
      String paymentCondition, Boolean backToBack,Integer accountingPeriod,
      Long startPaymentConditionTime,Long endPaymentConditionTime,Long startPredictPaymentTime, Long endPredictPaymentTime,Boolean receivableState,Boolean payableDate
  ) {
    StringBuilder hql = new StringBuilder("select count(od.id) from OrderDetail od  inner join "
        + "Order o on od.`order_id` = o.id"
        + "where od.c_state = ? and o.c_state = ? ");
    Object[] params =
        new Object[]{Constants.STATE_OK,Constants.STATE_OK};
    if (!StringUtils.isNullOrEmpty(orderNo)) {
      hql.append("and o.orderNo like ? ");
      params = ObjectUtils.objectAdd(params, "%" + orderNo + "%");
    }
    if (!StringUtils.isNullOrEmpty(supplierName)) {
      hql.append("and o.supplier.enterpriseName like ? ");
      params = ObjectUtils.objectAdd(params, "%" + supplierName + "%");
    }
    if (!StringUtils.isNullOrEmpty(customer)) {
      hql.append("and o.customer like ? ");
      params = ObjectUtils.objectAdd(params, "%" + customer + "%");
    }
    if (!StringUtils.isNullOrEmpty(platform)) {
      hql.append("and o.type = ? ");
      params = ObjectUtils.objectAdd(params, platform);
    }
    if (!StringUtils.isNullOrEmpty(consignee)) {
      hql.append("and o.consignee like ? ");
      params = ObjectUtils.objectAdd(params, "%" + consignee + "%");
    }
    if (!StringUtils.isNullOrEmpty(mobile)) {
      hql.append("and o.mobile like ? ");
      params = ObjectUtils.objectAdd(params, "%" + mobile + "%");
    }
    if (!StringUtils.isNullOrEmpty(orderState)) {
      hql.append("and o.orderState = ? ");
      params = ObjectUtils.objectAdd(params, orderState);
    }
    if (!StringUtils.isNullOrEmpty(accepted)) {
      hql.append("and o.orderState = ? ");
      params = ObjectUtils.objectAdd(params, accepted);
    }
    if (CollUtil.isNotEmpty(customerReturnProgressList)) {
      params =
          HqlUtil.appendFieldIn(
              hql, params, "o.customerReturnProgress", customerReturnProgressList);
    }
    if (!StringUtils.isNullOrEmpty(invoicingState)) {
      hql.append("and o.invoicingState = ? ");
      params = ObjectUtils.objectAdd(params, invoicingState);
    }
    if (!StringUtils.isNullOrEmpty(customerReturnProgress)) {
      hql.append("and o.customerReturnProgress  =  ? ");
      params = ObjectUtils.objectAdd(params, customerReturnProgress);
    }
    if (hasUploadSignVoucher != null) {
      // 有签收信息数据并且状态为审核中
      hql.append(
              "AND (SELECT COUNT(*) FROM OrderAccept oa WHERE oa.orderId = o.id and oa"
                  + ".auditStatus = "
                  + Constants_order.ORDER_ACCEPT_PENDING_AUDITING
                  + " ) ")
          .append(hasUploadSignVoucher ? ">" : "<=")
          .append("0 ");
    }
    if (!StringUtils.isNullOrEmpty(price)) {
      hql.append("and o.price = ? ");
      params = ObjectUtils.objectAdd(params, new BigDecimal(price));
    }
    if (!StringUtils.isNullOrEmpty(userIds)) {
      hql.append("and o.supplier.purchaserId in (").append(userIds).append(") ");
    }
    if (!StringUtils.isNullOrEmpty(startDate)) {
      hql.append("and o.orderTime >= ? ");
      params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(startDate));
    }
    if (!StringUtils.isNullOrEmpty(endDate)) {
      hql.append("and o.orderTime < ? ");
      params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(endDate));
    }
    if (!StringUtils.isNullOrEmpty(startCreateTime)) {
      hql.append("and o.createTime >= ? ");
      params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(startCreateTime));
    }
    if (!StringUtils.isNullOrEmpty(endCreateTime)) {
      hql.append("and o.createTime < ? ");
      params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(endCreateTime));
    }
    if (!StringUtils.isNullOrEmpty(startCreateTime)) {
      hql.append("and o.createTime >= ? ");
      params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(startCreateTime));
    }
    if (!StringUtils.isNullOrEmpty(endCreateTime)) {
      hql.append("and o.createTime < ? ");
      params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(endCreateTime));
    }
    if (allShipTimeStart != null) {
      hql.append("and o.allShipTime > ? ");
      params = ObjectUtils.objectAdd(params, allShipTimeStart);
    }
    if (StrUtil.isNotBlank(accountStatus)) {
      hql.append("and o.accountStatus = ? ");
      params = ObjectUtils.objectAdd(params, accountStatus);
    }
    if (StrUtil.isNotBlank(accountOpenInvoiceStatus)) {
      hql.append("and o.supplierOpenInvoiceStatus = ? ");
      params = ObjectUtils.objectAdd(params, accountOpenInvoiceStatus);
    }
    if (StrUtil.isNotBlank(paymentStatus)) {
      hql.append("and o.paymentStatus = ? ");
      params = ObjectUtils.objectAdd(params, paymentStatus);
    }
    if (salesReturnState != null) {
      hql.append("and o.orderReturnState = ? ");
      params = ObjectUtils.objectAdd(params, salesReturnState);
    }
    if (!StringUtils.isNullOrEmpty(saleOrderNo)) {
      hql.append("and o.saleOrderNo like ? ");
      params = ObjectUtils.objectAdd(params, "%" + saleOrderNo + "%");
    }
    if (!StringUtils.isNullOrEmpty(erpOrderNo)) {
      hql.append("and o.erpOrderNo like ? ");
      params = ObjectUtils.objectAdd(params, "%" + erpOrderNo + "%");
    }
    params =
        SQLUtils.addStrLogicalOperators(
            paymentProportionOperators, paymentProportion, hql, params, "o.paymentProportion");
    if (startArrivalTime != null && endArrivalTime != null) {
      hql.append(" and o.arrivalTime >= ? and o.arrivalTime <= ? ");
      params = ObjectUtils.objectAdd(params, startArrivalTime);
      params = ObjectUtils.objectAdd(params, endArrivalTime);
    }
    if (startWriteOffTime != null && endWriteOffTime != null) {
      hql.append(" and o.writeOffTime >= ? and o.writeOffTime <= ? ");
      params = ObjectUtils.objectAdd(params, startWriteOffTime);
      params = ObjectUtils.objectAdd(params, endWriteOffTime);
    }
    if (!StringUtils.isNullOrEmpty(paymentType)) {
      hql.append(" and o.paymentType like ? ");
      params = ObjectUtils.objectAdd(params, "%" + paymentType + "%");
    }
    if (!StringUtils.isNullOrEmpty(paymentCondition)) {
      hql.append(" and ( ");
      String[] split = StrUtil.split(paymentCondition, StrUtil.COMMA);
      for (int i = 0; i < split.length; i++) {
        String paymentConditionStr = split[i];
        if (i == split.length - 1) {
          hql.append(" o.paymentCondition like ? ");
        } else {
          hql.append(" o.paymentCondition like ? and ");
        }
        params = ObjectUtils.objectAdd(params, "%" + paymentConditionStr + "%");
      }
      hql.append(" ) ");
    }
    if (backToBack != null) {
      hql.append( " and o.backToBack = ? ");
      params = ObjectUtils.objectAdd(params, backToBack);
    }
    if (accountingPeriod != null) {
      hql.append( " and o.accountingPeriod = ? ");
      params = ObjectUtils.objectAdd(params, accountingPeriod);
    }
    if (startPaymentConditionTime != null && endPaymentConditionTime != null) {
      hql.append(" and o.paymentConditionTime >= ? and o.paymentConditionTime <= ? ");
      params = ObjectUtils.objectAdd(params, startPaymentConditionTime);
      params = ObjectUtils.objectAdd(params, endPaymentConditionTime);
    }
    if (startPredictPaymentTime != null && endPredictPaymentTime != null) {
      hql.append(" and o.predictPaymentTime >= ? and o.predictPaymentTime <= ? ");
      params = ObjectUtils.objectAdd(params, startPredictPaymentTime);
      params = ObjectUtils.objectAdd(params, endPredictPaymentTime);
    }
    if (receivableState != null) {
      hql.append( " and o.customerReturnProgress <> ?  and o.paymentStatus = ? "
          + "  and o.backToBack <> ? ");
      params = ObjectUtils.objectAdd(params, Constants_order.CUSTOMER_PAYBACK_CONFIRM);
      params = ObjectUtils.objectAdd(params, Constants_order.COMPLETE_PAYMENT_TYPE);
      params = ObjectUtils.objectAdd(params, Boolean.TRUE);
    }
    if (payableDate != null) {
      hql.append( " and o.predictPaymentTime <= ?  ");
      params = ObjectUtils.objectAdd(params, DateUtil.getDailyEndTime(System.currentTimeMillis()));
    }

    hql.append("order by o.createTime desc");
    return count(hql.toString(), params);
  }

  @Override
  public List<String> getAllReturnOrderIds(List<String> orderIds) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();

    if (orderIds == null || orderIds.isEmpty()) {
      return new ArrayList<>();
    }

    sql.append("SELECT DISTINCT order_id ")
        .append("FROM t_order_detail ")
        .append("WHERE order_id IN ( ");

    // 添加参数占位符
    for (int i = 0; i < orderIds.size(); i++) {
      if (i > 0) {
        sql.append(",");
      }
      sql.append("?");
      params.add(orderIds.get(i));
    }

    sql.append(") ")
        .append("GROUP BY order_id ")
        .append("HAVING SUM(CASE WHEN COALESCE(c_num, 0) - COALESCE(c_return_num, 0) - COALESCE(c_cancel_num, 0) = 0 THEN 0 ELSE 1 END) = 0 ");
    List<String> sqlObjList = this.getSqlObjList(sql.toString(), params.toArray());
    if (CollUtil.isNotEmpty(sqlObjList)) {
      return sqlObjList;
    } else {
      return Collections.emptyList();
    }
  }

}
