package com.xhgj.srm.jpa.entity;

import java.math.BigDecimal;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

/**
 * create by GengShy 2024/04/24
 * 入驻报备单阶梯折扣比例表
 */
@Entity
@Table(name = "t_entry_registration_discount")
@Data
public class EntryRegistrationDiscount {

  /**
   * ID。
   */
  @Id
  @Column(name = "id", length = 32)
  @GeneratedValue(generator = "system-uuid")
  @GenericGenerator(name = "system-uuid", strategy = "uuid")
  private String id;

  /**
   * 类型，如阶梯折扣、品牌折扣。
   * {@link com.xhgj.srm.common.enums.entryregistration.EntryRegistrationDiscountTypeEnum}
   */
  @Column(name = "c_type", length = 2)
  private String type;

  /**
   * 履约金额。
   */
  @Column(name = "c_performance_amount", precision = 20, scale = 10)
  private BigDecimal performanceAmount;

  /**
   * 折扣比例。
   */
  @Column(name = "c_discount_ratio", precision = 4, scale = 2)
  private BigDecimal discountRatio;

  /**
   * 品牌ID。
   */
  @Column(name = "brand_id", length = 32)
  private String brandId;

  /**
   * 数据状态。
   */
  @Column(name = "c_state", length = 1)
  private String state;

  /**
   * 创建时间（以毫秒为单位的Unix时间戳）。
   */
  @Column(name = "c_create_time")
  private Long createTime;

  /**
   * 更新时间（以毫秒为单位的Unix时间戳）。
   */
  @Column(name = "c_update_time")
  private Long updateTime;

  /**
   * 创建人。
   */
  @Column(name = "c_create_man", length = 32)
  private String createMan;

  /**
   * 更新人。
   */
  @Column(name = "c_update_man", length = 32)
  private String updateMan;

  /**
   * 入驻报备单id
   */
  @Column(name = "entry_registration_order_id", length = 32)
  private String entryRegistrationOrderId;

  /**
   * 品牌名称
   */
  @Column(name = "c_brand_name", length = 100)
  private String brandName;

  /**
   * 合同id
   */
  @Column(name = "landing_contract_id", length = 32)
  private String landingContractId;
}