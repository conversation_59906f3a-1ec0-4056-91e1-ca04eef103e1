package com.xhgj.srm.jpa.config;/**
 * @since 2025/5/30 9:39
 */

import com.xhgj.srm.jpa.util.BootDictUtil;
import com.xhiot.boot.dict.core.service.BootDictDetailService;
import com.xhiot.boot.dict.core.service.BootDictService;
import org.springframework.context.annotation.Configuration;

/**
 *<AUTHOR>
 *@date 2025/5/30 09:39:03
 *@description
 */
@Configuration
public class SRMBootDictConfig {

  public SRMBootDictConfig(BootDictService bootDictService,
      BootDictDetailService bootDictDetailService) {
    BootDictUtil.setBootDictService(bootDictService);
    BootDictUtil.setBootDictDetailService(bootDictDetailService);
  }
}
