package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.*;
import java.io.Serializable;

/**
 * @description 物料上架项目字段表
 * <AUTHOR>
 * @date 2023-05-06 18:31:11
 */
@Entity
@Data
@Table(name = "t_product_field")
public class ProductField implements Serializable {

	@Id
	@Column(name = "id", insertable = false, nullable = false)
	@GeneratedValue(generator = "system-uuid")
	@GenericGenerator(name = "system-uuid", strategy = "uuid")
	private String id;

	/**
	 * 物料id
	 */
	@Column(name = "c_product_id")
	private String productId;

	/**
	 * 上架项目
	 */
	@Column(name = "c_platform")
	private String platform;

	/**
	 * 项目字段key
	 */
	@Column(name = "c_field_key")
	private String fieldKey;

	/**
	 * 项目字段名称
	 */
	@Column(name = "c_field_name")
	private String fieldName;

	/**
	 * 项目字段值
	 */
	@Column(name = "c_field_value")
	private String fieldValue;

	/**
	 * 数据状态(1_有效 0_失效)
	 */
	@Column(name = "c_state")
	private String state;

  /**
   * 是否必填(1_有效 2_失效)
   */
  @Column(name = "c_require")
  private String require;

  /**
   *  取值范围
   */
  @Column(name = "c_range_list")
  private String rangeList;

	/**
	 * 创建时间
	 */
	@Column(name = "c_create_time")
	private Long createTime;
  /**
   * 前端显示的控件类型
   */
  @Column(name = "c_control_type")
  private String controlType;

}

