package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;
import javax.persistence.*;
import java.io.Serializable;

/**
 * @description
 * <AUTHOR>
 * @date 2023-07-19 15:17:32
 */
@Entity
@Data
@Table(name = "t_supplier_brand")
public class SupplierBrand implements Serializable {

	/**
	 * 主键id
	 */
	@Id
	@Column(name = "id", insertable = false, nullable = false)
	@GeneratedValue(generator = "system-uuid")
	@GenericGenerator(name = "system-uuid", strategy = "uuid")
	private String id;

	/**
	 * 供应商id
	 */
	@Column(name = "c_supplier_id")
	private String supplierId;

	/**
	 * 供应商名称
	 */
	@Column(name = "c_supplier_name")
	private String supplierName;


  /**
   * mpm品牌编码
   */
  @Column(name = "c_brand_code")
  private String brandCode;


  /**
	 * mpm品牌id
	 */
	@Column(name = "c_brand_mpm_id")
	private String brandMpmId;

  /**
   *  品牌名称(中文)
   */
  @Column(name = "c_brand_name_cn")
  private String brandNameCn;

  /**
   *  品牌名称(英文)
   */
  @Column(name = "c_brand_name_en")
  private String brandNameEn;

  /**
   * 经营形式(1_品牌方 2_集货商)
   */
  @Column(name = "c_manage_type")
  private String manageType;

  /**
   * 描述信息
   */
  @Column(name = "c_desc")
  private String desc;


  /**
   *  品牌logo地址
   */
  @Column(name = "c_brand_logo_url")
  private String brandLogoUrl;

	/**
	 * mpm审核状态(1_审核中 2_驳回 3_审核通过 -1放弃)
	 */
	@Column(name = "c_mpm_state")
	private String mpmState;

	/**
	 * 审核时间
	 */
	@Column(name = "c_mpm_operation_time")
	private Long mpmOperationTime = 0L;

	/**
	 * mpm审核人
	 */
	@Column(name = "c_mpm_operator")
	private String mpmOperator;

	/**
	 * mpm审核意见
	 */
	@Column(name = "c_mpm_comment")
	private String mpmComment;

	/**
	 * 有效标识(1_正常)
	 */
	@Column(name = "c_state")
	private String state;

	/**
	 * 创建时间
	 */
	@Column(name = "c_create_time")
	private Long createTime = 0L;

}

