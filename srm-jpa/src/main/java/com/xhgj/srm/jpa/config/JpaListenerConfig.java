package com.xhgj.srm.jpa.config;/**
 * @since 2025/5/28 23:25
 */

import com.xhgj.srm.common.event.impl.SrmSpringEventPublisher;
import com.xhgj.srm.jpa.sharding.listenner.SupplierOrderEntityListener;
import org.springframework.context.annotation.Configuration;
import javax.annotation.Resource;

/**
 *<AUTHOR>
 *@date 2025/5/28 23:25:18
 *@description
 */
@Configuration
public class JpaListenerConfig {

  @Resource
  public void setDependencies(SrmSpringEventPublisher publisher) {
    SupplierOrderEntityListener.setPublisher(publisher);
  }
}
