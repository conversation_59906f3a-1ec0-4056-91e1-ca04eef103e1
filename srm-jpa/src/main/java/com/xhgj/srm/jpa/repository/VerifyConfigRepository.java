package com.xhgj.srm.jpa.repository;

import com.xhgj.srm.jpa.entity.VerifyConfig;
import com.xhiot.boot.framework.jpa.repository.BootBaseRepository;
import java.util.Optional;

/**
 * VerifyConfigRepository
 */
public interface VerifyConfigRepository extends BootBaseRepository<VerifyConfig, String> {

  Optional<VerifyConfig> findFirstByConfigType(String configType);

  /**
   * 根据配置类型和启用状态查询
   *
   * @param configType 配置类型
   * @param enable     启用状态
   * @return
   */
  VerifyConfig findFirstByConfigTypeAndEnable(String configType, Boolean enable);

}
