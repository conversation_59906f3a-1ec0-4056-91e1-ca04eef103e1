package com.xhgj.srm.jpa.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.xhgj.srm.common.Constants;
import com.xhgj.srm.common.Constants_order;
import com.xhgj.srm.common.enums.supplier.SupplierLevelEnum;
import com.xhgj.srm.jpa.dao.SupplierDao;
import com.xhgj.srm.jpa.entity.Supplier;
import com.xhiot.boot.core.common.util.DateUtils;
import com.xhiot.boot.core.common.util.ObjectUtils;
import com.xhiot.boot.core.common.util.StringUtils;
import java.util.ArrayList;
import java.util.List;
import com.xhiot.boot.framework.jpa.util.HqlUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class SupplierDaoImpl extends AbstractExtDao<Supplier> implements SupplierDao {

  @Override
  public String isSupplierCode() {
    String hql = "select su.c_code from t_supplier su order by su.c_code+0 desc ";
    Object[] params = new Object[] {};
    String result = (String) getFirstSqlObj(hql, params);
    return !StringUtils.isNullOrEmpty(result) ? result : "0";
  }

  @Override
  public Supplier getSupplierByEnterpriseNameNotDel(String enterpriseName, String id) {
    String hql = "from Supplier s where s.state != ? and s.enterpriseLevel != ? ";
    Object[] params =
        new Object[] {Constants.COMMONSTATE_DELETE, SupplierLevelEnum.POTENTIAL.getCode()};
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      hql += "and s.enterpriseName = ? ";
      params = ObjectUtils.objectAdd(params, enterpriseName);
    }
    if (!StringUtils.isNullOrEmpty(id)) {
      hql += "and s.id != ? ";
      params = ObjectUtils.objectAdd(params, id);
    }
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public Supplier getSupplierByEnterName(String name) {
    String hql = "from Supplier s where s.state!= ? and s.state != ?  and s.enterpriseName = ? ";
    Object[] params = new Object[] {Constants.COMMONSTATE_DELETE, Constants.COMMONSTATE_LOCK, name};
    // obj = (Supplier) BaseDao.getUniqueHqlObj(hql, params);
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public Supplier getSupplierById(String id) {
    String hql = "from Supplier s where s.state != ? and s.id = ? ";
    Object[] params = new Object[] {Constants.COMMONSTATE_DELETE, id};
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public Page<String> getEditCheckSupplierPage(
      String useGroup,
      String enterpriseName,
      String uscc,
      String corporate,
      String brands,
      String type,
      String startTime,
      String endTime,
      String userId,
      int pageNo,
      int pageSize) {
    String sql =
        "select a.id from ( select su.id,su.c_createTime from t_supplier su "
            + "left join t_brand b on su.id = b.supplierId "
            + "left join t_contact co on su.id = co.supplierId "
            + "where (su.c_state = ? or su.c_state = ?) and su.c_supType = ? "
            + "and ( su.c_manageId = ? and su.c_auditState = ? ) and ( su.c_iscancel != ? or su.c_iscancel is null ) and su.c_isFromSupplier = ? ";
    Object[] params =
        new Object[] {
          Constants.COMMONSTATE_OK,
          Constants.COMMONSTATE_CHECKING,
          Constants.SUPPLIERTYPE_CHINA,
          userId,
          Constants.AUDIT_STATE_PURCHASEIN,
          Constants.YES,
          Constants.YES
        };
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      sql += "and su.c_enterpriseName like ? ";
      params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
    }
    if (!StringUtils.isNullOrEmpty(uscc)) {
      sql += "and su.c_uscc like ? ";
      params = ObjectUtils.objectAdd(params, "%" + uscc + "%");
    }
    if (!StringUtils.isNullOrEmpty(corporate)) {
      sql += "and su.c_corporate like ? ";
      params = ObjectUtils.objectAdd(params, "%" + corporate + "%");
    }
    if (!StringUtils.isNullOrEmpty(useGroup)) {
      sql += "and su.c_useGroup like ? ";
      params = ObjectUtils.objectAdd(params, "%" + useGroup + "%");
    }
    if (!StringUtils.isNullOrEmpty(brands)) {
      sql += "and ( b.c_brandname_en like ? or b.c_brandname_cn like ? ) ";
      params = ObjectUtils.objectAdd(params, "%" + brands + "%");
      params = ObjectUtils.objectAdd(params, "%" + brands + "%");
    }
    if (!StringUtils.isNullOrEmpty(startTime) && !StringUtils.isNullOrEmpty(endTime)) {
      sql +=
          " and ( ( su.c_state = ? and su.c_editTime >= ? and su.c_editTime < ? ) or ( su.c_state = ? and su.c_createTime >= ? and su.c_createTime < ? ) ) ";
      // update zhuhd 2021年7月13日13:42:33 结束时间加一天
      params = ObjectUtils.objectAdd(params, Constants.COMMONSTATE_OK);
      params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(startTime));
      params =
          ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(endTime) + 86400000L);
      params = ObjectUtils.objectAdd(params, Constants.COMMONSTATE_CHECKING);
      params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(startTime));
      params =
          ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(endTime) + 86400000L);
    }
    if (!StringUtils.isNullOrEmpty(type)) {
      if (Constants.SUPPLIERCHECKTYPE_MAP_UPDATE.equals(type)) {
        sql += "and su.c_editTime > 0 ";
      } else if (Constants.SUPPLIERCHECKTYPE_MAP_ADD.equals(type)) {
        sql += "and su.c_editTime = 0 ";
      }
    }
    sql += "group by su.id ) a order by a.c_createTime desc";
    return findPageSqlObject(sql, params, pageNo, pageSize);
  }

  @Override
  public Page<Supplier> getSupplierDomesticByPage(
      String enterpriseName, String enterpriseCode, String groupId, int pageNo, int pageSize) {
    String sql =
        "select * from (select s.* from t_supplier s "
            + "left join t_supplier_in_group sig  on sig.supplier_id = s.id "
            + " where sig.c_state = ? and( sig.c_enterprise_level <> ? or sig.c_enterprise_level is null) "
            + " and s.c_state = ? and s.mdm_code is not null and s.mdm_code <> '' ";
    Object[] params =
        new Object[] {
          Constants.COMMONSTATE_OK, SupplierLevelEnum.POTENTIAL.getCode(), Constants.COMMONSTATE_OK
        };
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      sql += "and s.c_enterpriseName like ? ";
      params = ObjectUtils.objectAdd(params, StrUtil.wrap(enterpriseName, "%"));
    }
    if (!StringUtils.isNullOrEmpty(enterpriseCode)) {
      sql += "and s.mdm_code like ? ";
      params = ObjectUtils.objectAdd(params, StrUtil.wrap(enterpriseCode, "%"));
    }
    if (!StringUtils.isNullOrEmpty(groupId)) {
      sql += "and sig.group_id = ? ";
      params = ObjectUtils.objectAdd(params, groupId);
    }
    sql += "group by id ";
    sql += ") a order by c_createCode asc ";
    return findPageSql(sql, params, pageNo, pageSize);
  }

  @Override
  public String getSupplierDomestic(String mdmCode, String enterpriseName, String groupId) {
    String sql =
        "select * from (select distinct s.id from t_supplier s "
            + "left join t_supplier_in_group sig  on sig.supplier_id = s.id "
            + " where sig.c_state = ? and sig.c_enterprise_level <> ? "
            + " and s.c_state = ? and s.mdm_code is not null and s.mdm_code <> ''  ";
    Object[] params =
        new Object[] {
          Constants.COMMONSTATE_OK, SupplierLevelEnum.POTENTIAL.getCode(), Constants.COMMONSTATE_OK
        };
    if (!StringUtils.isNullOrEmpty(mdmCode)) {
      sql += "and s.mdm_code = ? ";
      params = ObjectUtils.objectAdd(params, mdmCode);
    }
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      sql += "and s.c_enterpriseName = ? ";
      params = ObjectUtils.objectAdd(params, enterpriseName);
    }
    if (!StringUtils.isNullOrEmpty(groupId)) {
      sql += "and sig.group_id = ? ";
      params = ObjectUtils.objectAdd(params, groupId);
    }
    sql += "order by c_createCode asc ) a ";
    return (String) getFirstSqlObj(sql, params);
  }

  @Override
  public Page<String> getSupplierBlackPage(
      String mdmCode,
      String enterpriseName,
      String enterpriseLevel,
      String useGroup,
      String enterpriseNature,
      String industry,
      String purchaserName,
      String brands,
      String mobile,
      String contacts,
      int pageNo,
      int pageSize) {
    String sql =
        "select a.id from ( select su.id,su.c_create_time from t_supplier_in_group su "
            + "left join t_group g on g.id = su.group_id ";
    if (!StringUtils.isNullOrEmpty(enterpriseName)
        || !StringUtils.isNullOrEmpty(industry)
        || !StringUtils.isNullOrEmpty(mobile)
        || !StringUtils.isNullOrEmpty(mdmCode)) {
      sql += "left join t_supplier s on s.id = su.supplier_id ";
    }
    if (!StringUtils.isNullOrEmpty(purchaserName)) {
      sql += "left join t_user u on u.id = su.purchaser_id ";
    }
    if (!StringUtils.isNullOrEmpty(brands)) {
      sql += "left join t_brand b on su.id = b.c_relation_id ";
    }
    if (!StringUtils.isNullOrEmpty(contacts)) {
      sql += "left join t_contact co on su.id = co.supplier_in_group_id ";
    }
    sql += " where su.c_state = ? ";
    Object[] params = new Object[] {Constants.COMMONSTATE_BLACKLIST};
    if (!StringUtils.isNullOrEmpty(mdmCode)) {
      sql += " and s.mdm_code like ? ";
      params = ObjectUtils.objectAdd(params, "%" + mdmCode + "%");
    }
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      sql += " and s.c_enterpriseName like ? ";
      params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
    }
    if (!StringUtils.isNullOrEmpty(enterpriseLevel)) {
      sql += " and su.c_enterprise_level = ? ";
      params = ObjectUtils.objectAdd(params, enterpriseLevel);
    }
    if (!StringUtils.isNullOrEmpty(useGroup)) {
      sql += " and g.c_erpCode = ? ";
      params = ObjectUtils.objectAdd(params, useGroup);
    }
    if (!StringUtils.isNullOrEmpty(enterpriseNature)) {
      sql += " and su.c_enterprise_nature like ? ";
      params = ObjectUtils.objectAdd(params, "%" + enterpriseNature + "%");
    }
    if (!StringUtils.isNullOrEmpty(industry)) {
      sql += " and s.c_industry like ? ";
      params = ObjectUtils.objectAdd(params, "%" + industry + "%");
    }
    if (!StringUtils.isNullOrEmpty(purchaserName)) {
      sql += " and u.c_realname like ? ";
      params = ObjectUtils.objectAdd(params, "%" + purchaserName + "%");
    }
    if (!StringUtils.isNullOrEmpty(brands)) {
      sql += " and ( b.c_brandname_en like ? or b.c_brandname_cn like ? ) ";
      params = ObjectUtils.objectAdd(params, "%" + brands + "%");
      params = ObjectUtils.objectAdd(params, "%" + brands + "%");
    }
    if (!StringUtils.isNullOrEmpty(contacts)) {
      sql += " and co.c_name like ? ";
      params = ObjectUtils.objectAdd(params, "%" + contacts + "%");
    }
    if (!StringUtils.isNullOrEmpty(mobile)) {
      sql += " and s.c_mobile like ? ";
      params = ObjectUtils.objectAdd(params, "%" + mobile + "%");
    }
    sql += " group by su.id ) a order by a.c_create_time desc  ";
    return findPageSqlObject(sql, params, pageNo, pageSize);
  }

  @Override
  public Page<Object[]> getFrontNormalSupplierPage(
      String userId,
      String createCode,
      String enterpriseName,
      String mdmCode,
      int pageNo,
      int pageSize) {
    String sql =
        "select su.id,su.c_enterpriseName ,su.mdm_code,count from t_supplier su left join "
            + "(select supplierId ,count(*) count from t_supplier_user tu where tu.c_state = ? group by tu.supplierId )tsu "
            + "on su.id = tsu.supplierId "
            + "where su.c_state = ? and su.c_supType = ? and su.mdm_code is not null and su.mdm_code <> ? ";
    Object[] params =
        new Object[] {
          Constants.STATE_OK, Constants.COMMONSTATE_OK, Constants.SUPPLIERTYPE_CHINA, StrUtil.EMPTY
        };
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      sql += "and su.c_enterpriseName like ? ";
      params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
    }
    if (!StringUtils.isNullOrEmpty(createCode)) {
      sql += "and su.c_createCode like ? ";
      params = ObjectUtils.objectAdd(params, "%" + createCode + "%");
    }
    if (!StringUtils.isNullOrEmpty(mdmCode)) {
      sql += "and su.mdm_code like ? ";
      params = ObjectUtils.objectAdd(params, "%" + mdmCode + "%");
    }
    sql += "order by count desc,mdm_code desc";
    return findPageSqlObject(sql, params, pageNo, pageSize);
  }

  @Override
  public List<Supplier> getNormalSupplierListByPurchaser(String userId) {
    String hql = " from Supplier s where s.state != ? ";
    Object[] params = new Object[] {Constants.COMMONSTATE_DELETE};
    if (!StringUtils.isNullOrEmpty(userId)) {
      hql += "and s.purchaserId = ? ";
      params = ObjectUtils.objectAdd(params, userId);
    }
    hql += "order by s.createTime desc";
    return getHqlList(hql, params);
  }

  @Override
  public List<Supplier> getEnterpriseByDepart(String departId) {
    String hql = " from Supplier s where s.state != ? ";
    Object[] params = new Object[] {Constants.STATE_DELETE};
    if (!StringUtils.isNullOrEmpty(departId)) {
      hql += "and s.departId = ? ";
      params = ObjectUtils.objectAdd(params, departId);
    }
    hql += "order by s.createTime desc";
    return getHqlList(hql, params);
  }

  @Override
  public List<Supplier> getEnterpriseByOrg(String code) {
    String hql = " from Supplier s where s.state != ? ";
    Object[] params = new Object[] {Constants.STATE_DELETE};
    if (!StringUtils.isNullOrEmpty(code)) {
      hql += "and  s.createCode = ? ";
      params = ObjectUtils.objectAdd(params, code);
    }
    hql += "order by s.createTime desc";
    return getHqlList(hql, params);
  }

  @Override
  public List<Supplier> getEnterpriseByType() {
    String hql = " from Supplier s where s.state = ? and s.enterpriseLevel != ?";
    Object[] params = new Object[] {Constants.STATE_OK, SupplierLevelEnum.POTENTIAL.getCode()};
    hql += "order by s.createTime desc";
    return getHqlList(hql, params);
  }

  @Override
  public Page<String> getOrderSupplierPageByCondition(
      String enterpriseName, String erpCode, String platform, String status, Long effectiveStart,
      Long effectiveEnd, int pageNo,
      int pageSize) {
    StringBuilder sql =
        new StringBuilder(
            "select su.id from t_supplier su "
                + "where su.c_state = ? and ( su.c_enterpriseLevel != ? or su.c_enterpriseLevel is null ) and su.c_supType = ? and su.c_is_open_order = ? ");
    Object[] params =
        new Object[] {
          Constants.COMMONSTATE_OK,
            SupplierLevelEnum.POTENTIAL.getCode(),
          Constants.SUPPLIERTYPE_CHINA,
          Constants.YES
        };
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      sql.append("and su.c_enterpriseName like ? ");
      params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
    }
    if (!StringUtils.isNullOrEmpty(platform)) {
      sql.append("and CONCAT(',', su.c_platform, ',') like ?  ");
      params = ObjectUtils.objectAdd(params, "%," + platform + ",%");
    }
    if (StrUtil.isNotBlank(status) && StrUtil.isNotBlank(platform)) {
      if (StrUtil.equals(status,Constants.SUPPLIER_PERFORMANCE_STATUS_EFFECT)) {
        // 存在一条生效的记录
        sql.append("and exists (select 1 from t_supplier_performance sp where "
            + "sp.supplier_id = su.id and sp.c_state = ? and sp.c_status = ? and sp.c_platform_code = ? ) ");
      } else {
        // 不存在一条生效记录
        sql.append("and not exists (select 1 from t_supplier_performance sp where "
            + "sp.supplier_id = su.id and sp.c_state = ? and sp.c_status = ? and sp.c_platform_code = ? ) ");
      }
      params = ObjectUtils.objectAdd(params, Constants.STATE_OK);
      params = ObjectUtils.objectAdd(params, Constants.SUPPLIER_PERFORMANCE_STATUS_EFFECT);
      params = ObjectUtils.objectAdd(params, platform);
    }
    // 履约周期
    if (effectiveStart != null) {
      Assert.notBlank(platform, "履约周期筛选时平台不能为空");
      sql.append("and exists (select 1 from t_supplier_performance sp inner join "
          + "t_landing_contract lc on sp.landing_contract_id = lc.id where sp.supplier_id = su.id"
          + " and sp.c_state = ? and lc.c_state = ? and sp.c_platform_code = ? and lc.c_effective_start >= ? ) ");
      params = ObjectUtils.objectAdd(params, Constants.STATE_OK);
      params = ObjectUtils.objectAdd(params, Constants.STATE_OK);
      params = ObjectUtils.objectAdd(params, platform);
      params = ObjectUtils.objectAdd(params, effectiveStart);
    }
    if (effectiveEnd != null) {
      Assert.notBlank(platform, "履约周期筛选时平台不能为空");
      sql.append("and exists (select 1 from t_supplier_performance sp inner join "
          + "t_landing_contract lc on sp.landing_contract_id = lc.id where sp.supplier_id = su.id"
          + " and sp.c_state = ? and lc.c_state = ? and sp.c_platform_code = ? and lc.c_effective_end <= ? ) ");
      params = ObjectUtils.objectAdd(params, Constants.STATE_OK);
      params = ObjectUtils.objectAdd(params, Constants.STATE_OK);
      params = ObjectUtils.objectAdd(params, platform);
      params = ObjectUtils.objectAdd(params, effectiveEnd);
    }

    if (!StringUtils.isNullOrEmpty(erpCode)) {
      sql.append("and su.mdm_code like ? ");
      params = ObjectUtils.objectAdd(params, "%" + erpCode + "%");
    }
    sql.append("order by su.c_createTime desc ");
    return findPageSqlObject(sql.toString(), params, pageNo, pageSize);
  }

  @Override
  public Page<String> getTakeEffectOrderSupplierPage(
      String enterpriseName, String platform, String status, int pageNo, int pageSize) {
    StringBuilder sql =
        new StringBuilder(
            "SELECT s.id FROM t_supplier s  LEFT JOIN t_supplier_performance p on s.id = p.supplier_id "
                + "where s.c_state = ? and p.c_state = ? and p.c_status = '1' ");
    Object[] params = new Object[] {Constants.YES, Constants.YES, status};
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      sql.append("and su.c_enterpriseName like ? ");
      params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
    }
    if (!StringUtils.isNullOrEmpty(platform)
        && !platform.contains(Constants_order.ORDER_PLATFORM_ZDY)) {
      sql.append("and p.c_platform_name like ? ");
      params = ObjectUtils.objectAdd(params, "%" + platform + "%");
    }
    sql.append("order by s.c_createTime desc ");
    return findPageSqlObject(sql.toString(), params, pageNo, pageSize);
  }

  @Override
  public Page<String> getOrderSupplierPage(
      String enterpriseName, String erpCode, List<String> platforms, int pageNo, int pageSize) {
    StringBuilder sql =
        new StringBuilder(
            "select su.id from t_supplier su "
                + "where su.c_state = ? and ( su.c_enterpriseLevel != ? or  su.c_enterpriseLevel is null ) and su.c_supType = ? ");
    Object[] params =
        new Object[] {
          Constants.COMMONSTATE_OK,
            SupplierLevelEnum.POTENTIAL.getCode(),
          Constants.SUPPLIERTYPE_CHINA,
        };
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      sql.append("and su.c_enterpriseName like ? ");
      params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
    }
    if (CollUtil.isNotEmpty(platforms)) {
      for (String platform : platforms) {
        sql.append("and su.c_platform like ? ");
        params = ObjectUtils.objectAdd(params, "%" + platform + "%");
      }
    }
    if (!StringUtils.isNullOrEmpty(erpCode)) {
      sql.append("and su.mdm_code like ? ");
      params = ObjectUtils.objectAdd(params, "%" + erpCode + "%");
    }
    sql.append("order by su.c_createTime desc ");
    return findPageSqlObject(sql.toString(), params, pageNo, pageSize);
  }

  @Override
  public Page<Object[]> getOrderSupplierPage(
      String enterpriseName,
      List<String> platforms,
      String createCode,
      Boolean openOrder,
      Boolean openSupplierOrder,
      String mdmCode,
      String uscc,
      int pageNo,
      int pageSize) {
    StringBuilder sql =
        new StringBuilder(
            "select * from (select distinct su.id,count,mdm_code "
                + "from t_supplier su"
                + " left join "
                + "(select supplierId ,count(*) count from t_supplier_user tu where tu.c_state = ? group by tu.supplierId )tsu "
                + "on su.id = tsu.supplierId ");
    if (StrUtil.isNotBlank(createCode)) {
      sql.append(
          "left join t_supplier_in_group tsig on su.id = tsig.supplier_id "
              + "left join t_group tg on tsig.group_id = tg.id ");
    }
    sql.append(
        "where su.c_state = ? and ( su.c_enterpriseLevel != ? or  su.c_enterpriseLevel is null ) and su.c_supType = ? ");

    Object[] params =
        new Object[] {
          Constants.COMMONSTATE_OK,
          Constants.COMMONSTATE_OK,
            SupplierLevelEnum.POTENTIAL.getCode(),
          Constants.SUPPLIERTYPE_CHINA,
        };
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      sql.append("and su.c_enterpriseName like ? ");
      params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
    }
    if (CollUtil.isNotEmpty(platforms)) {
      sql.append("and( ");
      for (String platform : platforms) {
        sql.append("CONCAT(',', su.c_platform, ',') like ? and ");
        params = ObjectUtils.objectAdd(params, StrUtil.wrap("," + platform + ",", "%"));
      }
      sql.delete(sql.length() - 4, sql.length() - 1);
      sql.append(") ");
    }
    if (StrUtil.isNotBlank(createCode)) {
      sql.append("and tg.c_code = ? ");
      params = ObjectUtils.objectAdd(params, createCode);
    }
    if (!StringUtils.isNullOrEmpty(mdmCode)) {
      sql.append("and su.mdm_code like ? ");
      params = ObjectUtils.objectAdd(params, "%" + mdmCode + "%");
    }
    if (!StringUtils.isNullOrEmpty(uscc)) {
      sql.append("and su.c_uscc like ? ");
      params = ObjectUtils.objectAdd(params, "%" + uscc + "%");
    }
    if (openOrder != null) {
      sql.append("and su.c_is_open_order = ? ");
      if (openOrder) {
        params = ObjectUtils.objectAdd(params, Constants.YES);
      } else {
        params = ObjectUtils.objectAdd(params, Constants.NO);
      }
    }
    if (openSupplierOrder != null) {
      sql.append("and su.c_open_supplier_order = ? ");
      params = ObjectUtils.objectAdd(params, openSupplierOrder);
    }
    sql.append(" )temp ");
    sql.append("order by count desc,mdm_code desc");
    return findPageSqlObject(sql.toString(), params, pageNo, pageSize);
  }



  @Override
  public Page<Object[]> getOrderSupplierPageSearch(
      String enterpriseName,
      List<String> platforms,
      String createCode,
      Boolean openOrder,
      Boolean openSupplierOrder,
      String mdmCode,
      String uscc,
      String oldSupplierType,
      int pageNo,
      int pageSize) {
    Object[] params;
    StringBuilder sql;
    boolean joinSupplierInGroup =
        StrUtil.equals(oldSupplierType, Constants.SUPPLIERTYPE_CHINA)
            && StrUtil.isNotBlank(createCode);
    if(StrUtil.equals(oldSupplierType,Constants.SUPPLIERTYPE_CHINA)){
      sql =
          new StringBuilder(
              "select * from (select distinct su.id,mdm_code "
                  + "from t_supplier su "
                  + "left join (select supplierId ,count(*) count from "
                  + "t_supplier_user tu where tu.c_state = ? group by tu.supplierId )tsu "
                  + "on su.id = tsu.supplierId ");
      if (joinSupplierInGroup) {
        sql.append(
            "left join t_supplier_in_group tsig on su.id = tsig.supplier_id "
                + "left join t_group tg on tsig.group_id = tg.id ");
      }
      sql.append(
          "where su.c_state = ? and (su.c_supType = ? or su.c_supType = ? or su.c_supType = ?)  ");
      params =
          new Object[] {
            Constants.COMMONSTATE_OK,
            Constants.COMMONSTATE_OK,
            Constants.SUPPLIERTYPE_CHINA,
            Constants.SUPPLIERTYPE_ABROAD,
            Constants.SUPPLIERTYPE_PERSONAL
          };
      // 如果关联组织查询，则需要保证组织内供应商也是正常状态
      if (joinSupplierInGroup) {
        sql.append("AND tsig.c_state = ? ");
        params = ObjectUtils.objectAdd(params, Constants.COMMONSTATE_OK);
      }
    }else {
      sql =
          new StringBuilder(
              "select * from (select distinct su.id,mdm_code "
                  + "from t_supplier su "
                  + "where su.c_state = ? ");
      params =
          new Object[] {
              Constants.COMMONSTATE_OK
          };
      if(StrUtil.equals(oldSupplierType,Constants.SUPPLIERTYPE_ABROAD)){
        sql.append(" and su.c_supType = ? ");
        params = ObjectUtils.objectAdd(params, Constants.SUPPLIER_TYPE_INTERNAL);
      }
      if(StrUtil.equals(oldSupplierType,Constants.SUPPLIERTYPE_PERSONAL)){
        sql.append(" and su.c_supType = ? ");
        params = ObjectUtils.objectAdd(params, Constants.SUPPLIER_TYPE_PROVISIONAL);
      }
    }
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      sql.append("and su.c_enterpriseName like ? ");
      params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
    }
    if (CollUtil.isNotEmpty(platforms)) {
      sql.append("and( ");
      for (String platform : platforms) {
        sql.append("su.c_platform like ? and ");
        params = ObjectUtils.objectAdd(params, StrUtil.wrap(platform, "%"));
      }
      sql.delete(sql.length() - 4, sql.length() - 1);
      sql.append(") ");
    }
    if (joinSupplierInGroup) {
      sql.append("and tg.c_code = ? ");
      params = ObjectUtils.objectAdd(params, createCode);
    }
    if (!StringUtils.isNullOrEmpty(mdmCode)) {
      sql.append("and su.mdm_code like ? ");
      params = ObjectUtils.objectAdd(params, "%" + mdmCode + "%");
    }
    if (!StringUtils.isNullOrEmpty(uscc)) {
      sql.append("and su.c_uscc like ? ");
      params = ObjectUtils.objectAdd(params, "%" + uscc + "%");
    }
    if (openOrder != null) {
      sql.append("and su.c_is_open_order = ? ");
      if (openOrder) {
        params = ObjectUtils.objectAdd(params, Constants.YES);
      } else {
        params = ObjectUtils.objectAdd(params, Constants.NO);
      }
    }
    if (openSupplierOrder != null) {
      sql.append("and su.c_open_supplier_order = ? ");
      params = ObjectUtils.objectAdd(params, openSupplierOrder);
    }
    sql.append(" )temp ");
    sql.append("order by mdm_code desc");
    return findPageSqlObject(sql.toString(), params, pageNo, pageSize);
  }

  @Override
  public List<String> getIdListOrderSupplierList(
      String enterpriseName,
      List<String> platforms,
      String createCode,
      Boolean openOrder,
      Boolean openSupplierOrder,
      String mdmCode) {
    StringBuilder sql =
        new StringBuilder(
            "select distinct su.id from t_supplier su"
                + " left join "
                + "(select supplierId ,count(*) count from t_supplier_user tu where tu.c_state = ? group by tu.supplierId )tsu "
                + "on su.id = tsu.supplierId "
                + "where su.c_state = ? and ( su.c_enterpriseLevel != ? or  su.c_enterpriseLevel is null ) and su.c_supType = ? ");
    Object[] params =
        new Object[] {
          Constants.COMMONSTATE_OK,
          Constants.COMMONSTATE_OK,
            SupplierLevelEnum.POTENTIAL.getCode(),
          Constants.SUPPLIERTYPE_CHINA,
        };
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      sql.append("and su.c_enterpriseName like ? ");
      params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
    }
    if (CollUtil.isNotEmpty(platforms)) {
      sql.append("and( ");
      for (String platform : platforms) {
        sql.append("su.c_platform like ? and ");
        params = ObjectUtils.objectAdd(params, StrUtil.wrap("," + platform + ",", "%"));
      }
      sql.delete(sql.length() - 4, sql.length() - 1);
      sql.append(") ");
    }
    if (!StringUtils.isNullOrEmpty(createCode)) {
      sql.append("and su.c_createCode = ? ");
      params = ObjectUtils.objectAdd(params, createCode);
    }
    if (!StringUtils.isNullOrEmpty(mdmCode)) {
      sql.append("and su.mdm_code like ? ");
      params = ObjectUtils.objectAdd(params, "%" + mdmCode + "%");
    }
    if (openOrder != null) {
      sql.append("and su.c_is_open_order = ? ");
      if (openOrder) {
        params = ObjectUtils.objectAdd(params, Constants.YES);
      } else {
        params = ObjectUtils.objectAdd(params, Constants.NO);
      }
    }
    if (openSupplierOrder != null) {
      sql.append("and su.c_open_supplier_order = ? ");
      params = ObjectUtils.objectAdd(params, openSupplierOrder);
    }
    sql.append("order by count desc,mdm_code desc");
    return getSqlObjList(sql.toString(), params);
  }

  @Override
  public long getSupplierOrderOpenCount() {
    long count;
    String sql =
        "select count( su.id ) from t_supplier su "
            + "where su.c_state = ? and ( su.c_enterpriseLevel != ? or su.c_enterpriseLevel is null ) and su.c_supType = ?  "
            + " and su.c_is_open_order = ? ";
    Object[] params =
        new Object[] {
          Constants.COMMONSTATE_OK,
            SupplierLevelEnum.POTENTIAL.getCode(),
          Constants.SUPPLIERTYPE_CHINA,
          Constants.YES
        };
    count = countSql(sql, params);
    return count;
  }

  @Override
  public List<Supplier> getOrderSupplierList(String enterpriseName) {
    String hql =
        " from Supplier su where su.state = ? and ( su.enterpriseLevel != ? or su.enterpriseLevel is null )  and su.supType = ? and su.isOpenOrder = ? ";
    Object[] params =
        new Object[] {
          Constants.COMMONSTATE_OK,
            SupplierLevelEnum.POTENTIAL.getCode(),
          Constants.SUPPLIERTYPE_CHINA,
          Constants.YES
        };
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      hql += "and su.enterpriseName like ? ";
      params = ObjectUtils.objectAdd(params, "%" + enterpriseName + "%");
    }
    return getHqlList(hql, params);
  }

  @Override
  public List<String> getDomesticSupplierList(
      String erpCode,
      String uid,
      String name,
      String enterpriseLevel,
      String useGroup,
      String enterpriseNature,
      String industry,
      String brands,
      String contacts,
      String mobile,
      String purchaserName,
      String startDate,
      String endDate,
      String erpState,
      String isOpen) {
    String sql =
        "select a.id from ( select su.id,su.c_createTime from t_supplier su "
            + "left join t_brand b on su.id = b.supplierId "
            + "left join t_contact co on su.id = co.supplierId "
            + "where (su.c_state = ? or  ( su.c_state = ? and su.c_createMan = ? ) ) and su.c_enterpriseLevel != ?  and su.c_supType = ?  ";
    Object[] params =
        new Object[] {
          Constants.COMMONSTATE_OK,
          Constants.COMMONSTATE_TEMPORARYSTORAGE,
          uid,
            SupplierLevelEnum.POTENTIAL.getCode(),
          Constants.SUPPLIERTYPE_CHINA
        };
    if (!StringUtils.isNullOrEmpty(name)) {
      sql += "and su.c_enterpriseName like ? ";
      params = ObjectUtils.objectAdd(params, "%" + name + "%");
    }
    if (!StringUtils.isNullOrEmpty(erpCode)) {
      sql += "and su.c_erpcode like ? ";
      params = ObjectUtils.objectAdd(params, "%" + erpCode + "%");
    }
    if (!StringUtils.isNullOrEmpty(enterpriseLevel)) {
      sql += "and su.c_enterpriseLevel = ? ";
      params = ObjectUtils.objectAdd(params, enterpriseLevel);
    }
    if (!StringUtils.isNullOrEmpty(useGroup)) {
      sql += "and su.c_createCode like ? ";
      params = ObjectUtils.objectAdd(params, "%" + useGroup + "%");
    }
    if (!StringUtils.isNullOrEmpty(enterpriseNature)) {
      sql += "and su.c_enterpriseNature like ? ";
      params = ObjectUtils.objectAdd(params, "%" + enterpriseNature + "%");
    }
    if (!StringUtils.isNullOrEmpty(industry)) {
      sql += "and su.c_industry like ? ";
      params = ObjectUtils.objectAdd(params, "%" + industry + "%");
    }
    if (!StringUtils.isNullOrEmpty(purchaserName)) {
      sql += "and su.c_purchaserName like ? ";
      params = ObjectUtils.objectAdd(params, "%" + purchaserName + "%");
    }
    if (!StringUtils.isNullOrEmpty(brands)) {
      sql += "and ( b.c_brandname_en like ? or b.c_brandname_cn like ? ) ";
      params = ObjectUtils.objectAdd(params, "%" + brands + "%");
      params = ObjectUtils.objectAdd(params, "%" + brands + "%");
    }
    if (!StringUtils.isNullOrEmpty(contacts)) {
      sql += "and co.c_name like ? ";
      params = ObjectUtils.objectAdd(params, "%" + contacts + "%");
    }
    if (!StringUtils.isNullOrEmpty(mobile)) {
      sql += "and su.c_mobile like ? ";
      params = ObjectUtils.objectAdd(params, "%" + mobile + "%");
    }
    if (!StringUtils.isNullOrEmpty(startDate)) {
      sql += "and su.c_createTime >= ? ";
      params = ObjectUtils.objectAdd(params, DateUtils.parseNormalDateToTimeStamp(startDate));
    }
    if (!StringUtils.isNullOrEmpty(endDate)) {
      sql += "and su.c_createTime < ? ";
      params =
          ObjectUtils.objectAdd(
              params, DateUtils.parseNormalDateToTimeStamp(endDate) + 24 * 60 * 60 * 1000);
    }
    if (!StringUtils.isNullOrEmpty(erpState)) {
      sql += "and su.c_synState = ? ";
      params = ObjectUtils.objectAdd(params, erpState);
    }
    if (!StringUtils.isNullOrEmpty(isOpen)) {
      if (Constants.YES.equals(isOpen)) {
        sql += "and su.c_isOpen = ? ";
        params = ObjectUtils.objectAdd(params, isOpen);
      } else {
        sql += "and su.c_isOpen is null ";
      }
    }
    sql += "group by su.id ) a order by a.c_createTime desc  ";
    return getSqlObjList(sql, params);
  }

  @Override
  public List<Supplier> getEnterpriseBySynState(String synState, int score) {
    String hql =
        " from Supplier s where s.state = ? and s.enterpriseLevel != ? and ( s.enterpriseLevel = ? or s.enterpriseLevel = ? or s.enterpriseLevel = ? ) "
            + "and s.synState = ? and s.integrity+0 > ? ";
    Object[] params =
        new Object[] {
          Constants.STATE_OK,
            SupplierLevelEnum.POTENTIAL.getCode(),
            SupplierLevelEnum.STRATEGIC.getCode(),
            SupplierLevelEnum.HIGH_QUALITY.getCode(),
            SupplierLevelEnum.GENERAL.getCode(),
          synState,
          score
        };
    hql += "order by s.createTime desc";
    return getHqlList(hql, params);
  }

  @Override
  public List<String> getAllIdByNameLikeOrUsedName(String nameOrUsedName, String supType) {
    Assert.notEmpty(nameOrUsedName);
    String sql =
        "SELECT s.id FROM t_supplier s LEFT JOIN t_supplier_biz_info bi "
            + "ON s.id = bi.supplier_id "
            + "WHERE s.c_state = ? "
            + "AND (s.c_enterpriseName LIKE ? or bi.c_used_name = ?) ";
    Object[] params =
        new Object[] {Constants.STATE_OK, StrUtil.wrap(nameOrUsedName, "%"), nameOrUsedName};
    if (!StringUtils.isNullOrEmpty(supType)) {
      sql += "AND s.c_supType = ? ";
      params = ObjectUtils.objectAdd(params, supType);
    }
    return CollUtil.emptyIfNull(getSqlObjList(sql, params));
  }

  @Override
  public Supplier getSupplierByEnterNameAndMobile(String personName, String mobile) {
    String hql = "from Supplier s where s.state = ? and s.enterpriseName = ? and s.mobile = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, personName, mobile};
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public Supplier getByMdmCode(String mdmCode) {
    String hql = "from Supplier s where s.state = ? and s.mdmCode = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, mdmCode};
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public String getNameByMdmCode(String mdmCode) {
    String hql = "select enterpriseName from Supplier s where s.state = ? and s.mdmCode = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, mdmCode};
    return (String) getFirstHqlObj(hql, params);
  }

  @Override
  public String getIdByNameOrUsedNameAndState(String nameOrUsedName, String supType, String state) {
    Assert.notEmpty(nameOrUsedName);
    Assert.notEmpty(state);
    String sql =
        "SELECT s.id FROM t_supplier s LEFT JOIN t_supplier_biz_info bi "
            + "ON s.id = bi.supplier_id "
            + "WHERE s.c_state = ? "
            + "AND (s.c_enterpriseName = ? or bi.c_used_name = ?) ";
    Object[] params = new Object[] {state, nameOrUsedName, nameOrUsedName};
    if (!StringUtils.isNullOrEmpty(supType)) {
      sql += "AND s.c_supType = ? ";
      params = ObjectUtils.objectAdd(params, supType);
    }
    return (String) getFirstSqlObj(sql, params);
  }

  @Override
  public List<Supplier> getNormalAndBlackEnterprise() {
    String hql =
        " from Supplier s where ( s.state = ? or s.state = ? ) and s.enterpriseLevel != ? and s.createTime < ? ";
    Object[] params =
        new Object[] {
          Constants.COMMONSTATE_OK,
          Constants.COMMONSTATE_BLACKLIST,
            SupplierLevelEnum.POTENTIAL.getCode(),
          1665741600000L
        };
    hql += "order by s.createTime desc";
    return getHqlList(hql, params);
  }

  @Override
  public List<Supplier> getCreteCodeIsEmpty() {
    String hql =
        " from Supplier s where  s.state <> ? and (createCode is null or createCode = '' ) ";
    Object[] params = new Object[] {Constants.STATE_DELETE};
    return getHqlList(hql, params);
  }

  @Override
  public Supplier getSupplierByEnterCode(String code) {
    String hql = "from Supplier s where s.state!= ? and s.state != ?  and s.code = ? ";
    Object[] params = new Object[] {Constants.COMMONSTATE_DELETE, Constants.COMMONSTATE_LOCK, code};
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public Supplier getByMdmCodeAndName(String mdmCode, String enterpriseName) {
    String hql = "from Supplier s where s.state = ? and s.mdmCode = ? and s.enterpriseName = ? ";
    Object[] params = new Object[] {Constants.STATE_OK, mdmCode, enterpriseName};
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public List<Supplier> getAllByCooperateType(String rangType) {
    String hql = " from Supplier s where  s.state <> ?";
    Object[] params = new Object[] {Constants.STATE_DELETE};
    if (StrUtil.isNotEmpty(rangType)) {
      if (StrUtil.equals(rangType, Constants.COOPERATE_TYPE_ALL)) {
        hql += (" and s.cooperateType != null");
      } else {
        hql += (" and (s.cooperateType = ? or s.cooperateType = 3)");
        params = ObjectUtils.objectAdd(params, rangType);
      }
    }
    hql += (" group by s.id");
    return getHqlList(hql, params);
  }

  @Override
  public Page<Supplier> getProvisionalSupplierPage(
      String supType,
      String enterpriseName,
      String mdmCode,
      Long orderTimeStart,
      Long orderTimeEnd,
      Pageable toPageable) {
    StringBuilder hql = new StringBuilder("from Supplier where state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    if (!StringUtils.isNullOrEmpty(supType)) {
      hql.append(" and supType = ? ");
      params = ObjectUtils.objectAdd(params, supType);
    }
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      hql.append("and enterpriseName like ? ");
      params = ObjectUtils.objectAdd(params, StrUtil.wrap(enterpriseName, "%"));
    }
    if (!StringUtils.isNullOrEmpty(mdmCode)) {
      hql.append("and mdmCode like ? ");
      params = ObjectUtils.objectAdd(params, StrUtil.wrap(mdmCode, "%"));
    }
    if (orderTimeStart != null) {
      hql.append("and createTime > ? ");
      params = ObjectUtils.objectAdd(params, orderTimeStart);
    }
    if (orderTimeEnd != null) {
      hql.append("and createTime < ? ");
      params = ObjectUtils.objectAdd(params, orderTimeEnd);
    }
    hql.append("order by mdmCode ASC ");
    return findPage(hql.toString(), params, toPageable.getPageNumber() + 1, toPageable.getPageSize());
  }

  @Override
  public Page<Supplier> getInteriorSupplierPage(String supType, String enterpriseName,
      String mdmCode, Long orderTimeStart, Long orderTimeEnd, Pageable toPageable) {
    StringBuilder hql = new StringBuilder("from Supplier where state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    if (!StringUtils.isNullOrEmpty(supType)) {
      hql.append(" and supType = ? ");
      params = ObjectUtils.objectAdd(params, supType);
    }
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      hql.append("and enterpriseName like ? ");
      params = ObjectUtils.objectAdd(params, StrUtil.wrap(enterpriseName, "%"));
    }
    if (!StringUtils.isNullOrEmpty(mdmCode)) {
      hql.append("and mdmCode like ? ");
      params = ObjectUtils.objectAdd(params, StrUtil.wrap(mdmCode, "%"));
    }
    if (orderTimeStart != null) {
      hql.append("and createTime > ? ");
      params = ObjectUtils.objectAdd(params, orderTimeStart);
    }
    if (orderTimeEnd != null) {
      hql.append("and createTime < ? ");
      params = ObjectUtils.objectAdd(params, orderTimeEnd);
    }
    hql.append("order by editTime DESC ");
    return findPage(hql.toString(), params, toPageable.getPageNumber() + 1, toPageable.getPageSize());
  }

  @Override
  public Supplier getInteriorSupplierByEditTime(String supType) {
    String hql = "from Supplier s where s.state = ? and s.supType = ? order by editTime DESC  ";
    Object[] params = new Object[] {Constants.STATE_OK, supType };
    return getFirstHqlEntity(hql, params);
  }

  @Override
  public Supplier getByMdmCodeOrName(String mdmCode, String enterpriseName) {
    StringBuilder hql = new StringBuilder("from Supplier where state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    if (!StringUtils.isNullOrEmpty(mdmCode)) {
      hql.append("and mdmCode = ? ");
      params = ObjectUtils.objectAdd(params, mdmCode);
    }
    if (!StringUtils.isNullOrEmpty(enterpriseName)) {
      hql.append("and enterpriseName = ? ");
      params = ObjectUtils.objectAdd(params, enterpriseName);
    }
    return getFirstHqlEntity(hql.toString(), params);
  }

  @Override
  public List<Supplier> getNameList(List<String> name, String supType) {
    StringBuilder hql = new StringBuilder("from Supplier where state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    params = HqlUtil.appendFieldIn(hql, params, "enterpriseName", name);
    if (StrUtil.isNotBlank(supType)) {
      hql.append("and supType = ? ");
      params = ObjectUtils.objectAdd(params, supType);
    }
    return getHqlList(hql.toString(),params);
  }

  @Override
  public List<Supplier> getSupplierByIds(List<String> supplierIds) {
    if (CollUtil.isEmpty(supplierIds)) {
      return new ArrayList<>();
    }
    StringBuilder hql = new StringBuilder("from Supplier where state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    if (CollUtil.isNotEmpty(supplierIds)) {
      params = HqlUtil.appendFieldIn(hql, params, "id", supplierIds);
    }
    return getHqlList(hql.toString(), params);
  }

  @Override
  public Supplier getSupplierInfoByMdmCodeAndEnterpriseName(String mdmCode, String enterpriseName) {
    StringBuilder hql = new StringBuilder("from Supplier s where s.state = ? ");
    Object[] params = new Object[] {Constants.STATE_OK};
    if (StrUtil.isNotBlank(mdmCode)) {
      hql.append("and s.mdmCode = ? ");
      params = ObjectUtils.objectAdd(params, mdmCode);
    }
    if (StrUtil.isNotBlank(enterpriseName)) {
      hql.append("and s.enterpriseName = ? ");
      params = ObjectUtils.objectAdd(params, enterpriseName);
    }
    return getFirstHqlEntity(hql.toString(), params);
  }

  @Override
  public Supplier getSupplierInfoByMdmCodeOrEnterpriseName(String mdmCodeOrEnterpriseName) {
    String hql =
        "from Supplier s where s.state = ? and ( s.mdmCode = ? " + "or s.enterpriseName = ? " + ")";
    Object[] params = new Object[] {Constants.STATE_OK,mdmCodeOrEnterpriseName,mdmCodeOrEnterpriseName};
    return getFirstHqlEntity(hql, params);
  }

}
