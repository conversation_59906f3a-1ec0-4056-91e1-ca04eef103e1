package com.xhgj.srm.jpa.entity;

import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

@Table(name = "t_user_check")
@Entity
@Data
public class UserCheck implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(generator = "system-uuid")
    @GenericGenerator(name = "system-uuid", strategy = "uuid")
    private String id;

    @JoinColumn(name = "userId")
    @ManyToOne
    private User user;

    @Column(name = "c_type")
    private String type;

  /** 供应商类型（等级）：{@link com.xhgj.srm.common.Constants#SUPPLIERLEVELMAP} */
  @Column(name = "c_supplierType")
  private String supplierType;

    @Column(name = "c_userErpCode")
    private String userErpCode;

    @Column(name = "c_state")
    private String state;

    @Column(name = "c_createTime")
    private Long createTime;


}
