package com.xhgj.srm.jpa.dto.purchase.order;

import java.math.BigDecimal;
import java.math.RoundingMode;
import lombok.Data;

/**
 * 采购订单统计 订单视图
 * <AUTHOR>
 */
@Data
public class PurchaseOrderStatistics {
  /**
   * 订货数量 3位小数
   */
  private BigDecimal num;

  /**
   * 订货金额 2位小数
   */
  private BigDecimal price;

  /**
   * 3位小数
   * @return
   */
  public BigDecimal getNum() {
    if (num == null) {
      return BigDecimal.ZERO;
    }
    return num.setScale(3, RoundingMode.HALF_UP);
  }

  /**
   * 2位小数
   * @return
   */
  public BigDecimal getPrice() {
    if (price == null) {
      return BigDecimal.ZERO;
    }
    return price.setScale(2, RoundingMode.HALF_UP);
  }
}
