package com.xhgj.srm.jpa.util;/**
 * @since 2025/1/6 9:15
 */

import cn.hutool.core.util.ReflectUtil;
import org.hibernate.proxy.HibernateProxy;
import org.springframework.stereotype.Component;
import javax.persistence.ManyToOne;
import javax.persistence.PostLoad;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 *<AUTHOR>
 *@date 2025/1/6 09:15:37
 *@description 懒加载实体监听器
 */
@Component
public class LazyLoadEntityListener {
  // 缓存字段上的 ManyToOne 注解信息
  private static final Map<Class<?>, Field[]> fieldCache = new HashMap<>();

  @PostLoad
  public void postLoad(Object entity) {
    // 如果缓存中已经有该类的字段信息，直接使用
    Field[] fields = fieldCache.computeIfAbsent(entity.getClass(), this::getManyToOneFields);

    for (Field field : fields) {
      try {
        field.setAccessible(true);  // 设置私有字段可访问
        Object fieldValue = field.get(entity);  // 获取字段的值
        // 如果字段值是 HibernateProxy 的代理对象，说明是懒加载对象
        if (fieldValue instanceof HibernateProxy) {
          // 使用自定义的懒加载处理逻辑
          Object proxyValue = LazyLoader.createProxy(fieldValue, LazyLoaderContext.isLazyLoad());
          field.set(entity, proxyValue);  // 设置字段的新值
        }
      } catch (IllegalAccessException e) {
        e.printStackTrace();  // 处理反射访问异常
      }
    }
  }

  // 提取类上所有 @ManyToOne 注解的字段，并缓存
  private Field[] getManyToOneFields(Class<?> clazz) {
    Field[] fields = ReflectUtil.getFields(clazz);
    return Arrays.stream(fields)
        .filter(field -> field.isAnnotationPresent(ManyToOne.class))
        .toArray(Field[]::new);
  }
}
